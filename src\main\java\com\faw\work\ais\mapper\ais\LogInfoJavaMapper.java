package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.LogInfoJava;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LogInfoJavaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(LogInfoJava record);

    int insertSelective(LogInfoJava record);

    LogInfoJava selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(LogInfoJava record);

    int updateByPrimaryKeyWithBLOBs(LogInfoJava record);

    int updateByPrimaryKey(LogInfoJava record);

    /**
     * 根据batchId获取内容
     * @param batchId
     * @return
     */
    LogInfoJava getInfoByBatchId(String batchId);

    List<LogInfoJava> getInfoByBatchIds(@Param("batchIds") List<String> batchIds);

    String getJavaLogByBatchId(@Param("batchId") String batchId);
}