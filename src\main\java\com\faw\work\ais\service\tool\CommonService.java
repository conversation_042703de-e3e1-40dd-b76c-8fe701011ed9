package com.faw.work.ais.service.tool;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 通用服务类，提供基础信息
 *
 * <AUTHOR>
 * @since 2025-05-29 17:02
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonService {

    @Tool(name = "getCurrentTime", description = "获取当前时间方法")
    public String getCurrentTime(ToolContext toolContext) {
        log.info("[CommonService][getCurrentTime][entrance] toolContext: {}", JSON.toJSONString(toolContext));

        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("[CommonService][getCurrentTime] currentTime: {}", currentTime);
        return String.format("当前时间为 %s。", currentTime);
    }

}
