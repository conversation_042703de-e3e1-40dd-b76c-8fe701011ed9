package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "知识转移请求")
public class FaqKnowledgeTransferRequest {
    
    @NotEmpty(message = "知识ID列表不能为空")
    @Schema(description = "需要转移的知识ID列表")
    private List<String> knowledgeIds;
    
    @NotNull(message = "目标类目ID不能为空")
    @Schema(description = "目标类目ID")
    private String targetCategoryId;
} 