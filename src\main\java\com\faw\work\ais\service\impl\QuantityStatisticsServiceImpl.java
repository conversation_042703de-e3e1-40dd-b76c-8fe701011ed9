package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.dcp.common.rest.Result;
import com.faw.work.ais.common.enums.*;
import com.faw.work.ais.common.util.DateUtils;
import com.faw.work.ais.mapper.ais.AiTaskResultNewDao;
import com.faw.work.ais.mapper.ais.HumanResultMapper;
import com.faw.work.ais.mapper.ais.QuantityStatisticsDao;
import com.faw.work.ais.model.*;
import com.faw.work.ais.service.QuantityStatisticsService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;


/**
* 数量统计表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 14:12:35
*/
@Slf4j
@Service
public class QuantityStatisticsServiceImpl implements QuantityStatisticsService {

	@Resource
	private QuantityStatisticsDao quantityStatisticsDao;

    @Resource
    private AiTaskResultNewDao aiTaskResultNewDao;

    @Resource
    private HumanResultMapper humanResultMapper;

	/**
    * 新增或修改
    */
	@Override
	public Result<Integer> insertOrUpdate(QuantityStatistics req) {
    Result<Integer> result ;
      try {
          String curTime = DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE);
          String newDate = req.getTiming();
          if(StringUtils.isEmpty(newDate)){
              //执行日期的前一天
              newDate  =   DateUtils.getAppointDate(curTime, -1);
          }
          String startDate = newDate + " 00:00:00";
          String endDate = newDate + " 23:59:59";
          AiTaskResultNew resultNew = new AiTaskResultNew();
          resultNew.setStartTime(startDate);
          resultNew.setEndTime(endDate);
          //清理當天數據
          QuantityStatistics quantityStatisticsReq = new QuantityStatistics();
          quantityStatisticsReq.setTiming(newDate);
          List<QuantityStatistics> quantityStatisticsList = quantityStatisticsDao.getQuantityIdsStatisticsList(quantityStatisticsReq);
          if(CollectionUtils.isNotEmpty(quantityStatisticsList)){
              List<List<QuantityStatistics>> thisPartition = Lists.partition(quantityStatisticsList, 500);
              thisPartition.forEach(k -> {
              quantityStatisticsDao.deleteByIds(k.stream().map(QuantityStatistics::getId).collect(Collectors.toList()));
              });
          }
          //ai
           List<AiTaskResultNew>  aiTaskResultNewList = aiTaskResultNewDao.getAiTaskResultThisList(resultNew);
           Map<String, List<AiTaskResultNew>> sysIdMap = aiTaskResultNewList.stream().collect(Collectors.groupingBy(AiTaskResultNew::getSystemId));
          //hum
          List<HumanResult> humanResultList =  humanResultMapper.getHumanResultThisList(resultNew);
          Map<String,List<HumanResult>> sysIdHumMap = humanResultList.stream().collect(Collectors.groupingBy(HumanResult::getSystemId));

              SystemIdEnum[] LevelingStatusEnums = SystemIdEnum.values();
              for (SystemIdEnum LevelingStatusEnum : LevelingStatusEnums) {
                String sysId =  LevelingStatusEnum.getCode();
                if(sysIdMap.get(sysId)!=null){
                    procAiValue(sysId, sysIdMap.get(sysId), newDate,true);
                }else {
                    procAiValue(sysId, sysIdMap.get(sysId), newDate,false);
                }

                if(sysIdHumMap.get(sysId)!=null){
                    procHumValue(sysIdHumMap.get(sysId), newDate, sysId,true);
                }else {
                    procHumValue(sysIdHumMap.get(sysId), newDate, sysId,false);
                }

              }

      } catch (Exception e) {
        log.error("error.impl.insertOrUpdate", e);
         return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
      }
         return Result.success(1);

	}

    private void procHumValue(List<HumanResult> humanResultListTemp, String newDate, String sysId,Boolean valueFlag) {
        //hum-通过数量
        //人工复审材料数：每一条记录
        QuantityStatistics quantityStatistics = new QuantityStatistics();
        quantityStatistics.setDataType(DataTypeEnum.HUM_SIX.getCode());
        if(valueFlag) {
            quantityStatistics.setQuantity(new BigDecimal(humanResultListTemp.size()));
        } else {
            quantityStatistics.setQuantity(BigDecimal.ZERO);
        }
        quantityStatistics.setTiming(newDate);
        quantityStatistics.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatistics);
        //人工复审材料准确数： 每一条 通过算一条
        QuantityStatistics quantityStatisticsThree = new QuantityStatistics();
        quantityStatisticsThree.setDataType(DataTypeEnum.HUM_SEVEN.getCode());
        if(valueFlag){
            long humPassNum = humanResultListTemp.stream().filter(item -> HumanCheckResultEnum.AI_ONE.getCode().equals(item.getHumanCheckResultSingle())).count();
            quantityStatisticsThree.setQuantity(new BigDecimal(humPassNum));
        }else {
            quantityStatisticsThree.setQuantity(BigDecimal.ZERO);
        }
        quantityStatisticsThree.setTiming(newDate);
        quantityStatisticsThree.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsThree);
        //人工审核任务数:batid 一个算条
        Map<String,List<HumanResult>> batchIdMap = null;
        QuantityStatistics quantityStatisticsZero = new QuantityStatistics();
        quantityStatisticsZero.setDataType(DataTypeEnum.HUM_FOUR.getCode());
        if(valueFlag){
            batchIdMap = humanResultListTemp.stream().filter(item -> StringUtils.isNotEmpty(item.getBatchId())).collect(Collectors.groupingBy(HumanResult::getBatchId));
            quantityStatisticsZero.setQuantity(new BigDecimal(batchIdMap.size()));
        }else {
            quantityStatisticsZero.setQuantity(BigDecimal.ZERO);
        }
        quantityStatisticsZero.setTiming(newDate);
        quantityStatisticsZero.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsZero);
        //AI审核任务通过数：batid 都通过 算一条
        QuantityStatistics quantityStatisticsOne = new QuantityStatistics();
        quantityStatisticsOne.setDataType(DataTypeEnum.HUM_FIVE.getCode());
        if( valueFlag) {
            long humTaskPassNum = batchIdMap.values().stream().filter(item -> item.stream().allMatch(item1 -> HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getHumanCheckResultSingle()))).count();
            quantityStatisticsOne.setQuantity(new BigDecimal(humTaskPassNum));
        } else {
            quantityStatisticsOne.setQuantity(BigDecimal.ZERO);
        }
        quantityStatisticsOne.setTiming(newDate);
        quantityStatisticsOne.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsOne);

        //TP TP数：ai √ ，人工 √
        long tpNum = 0;
        long fpNum = 0;
        long tnNum = 0;
        long fnNum = 0;
        if(valueFlag) {
             tpNum = humanResultListTemp.stream().filter(item1 -> HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getHumanCheckResultSingle()) && HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getAiResultSingle())).count();
             fpNum = humanResultListTemp.stream().filter(item1 -> !HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getHumanCheckResultSingle()) && HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getAiResultSingle())).count();
             tnNum = humanResultListTemp.stream().filter(item1 -> !HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getHumanCheckResultSingle()) && !HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getAiResultSingle())).count();
             fnNum = humanResultListTemp.stream().filter(item1 -> HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getHumanCheckResultSingle()) && !HumanCheckResultEnum.AI_ONE.getCode().equals(item1.getAiResultSingle())).count();
        }
        QuantityStatistics quantityStatisticsTp = new QuantityStatistics();
        quantityStatisticsTp.setDataType(DataTypeEnum.TP.getCode());
        quantityStatisticsTp.setQuantity(new BigDecimal(tpNum));
        quantityStatisticsTp.setTiming(newDate);
        quantityStatisticsTp.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsTp);
        //FP FP数：ai √ ，人工 ×
        QuantityStatistics quantityStatisticsFp = new QuantityStatistics();
        quantityStatisticsFp.setDataType(DataTypeEnum.FP.getCode());
        quantityStatisticsFp.setQuantity(new BigDecimal(fpNum));
        quantityStatisticsFp.setTiming(newDate);
        quantityStatisticsFp.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsFp);
        // TN TN数：ai × ，人工 ×
        QuantityStatistics quantityStatisticsTn = new QuantityStatistics();
        quantityStatisticsTn.setDataType(DataTypeEnum.TN.getCode());
        quantityStatisticsTn.setQuantity(new BigDecimal(tnNum));
        quantityStatisticsTn.setTiming(newDate);
        quantityStatisticsTn.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsTn);
        //FN FN数：ai × ，人工 √
        QuantityStatistics quantityStatisticsFn = new QuantityStatistics();
        quantityStatisticsFn.setDataType(DataTypeEnum.FN.getCode());
        quantityStatisticsFn.setQuantity(new BigDecimal(fnNum));
        quantityStatisticsFn.setTiming(newDate);
        quantityStatisticsFn.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsFn);
    }

    private void procAiValue(String sysId, List<AiTaskResultNew> aiTaskResultNewListTemp, String newDate,Boolean valueFlag) {

        //ai通过数量
        //AI审核材料数：每一条记录算一条
        QuantityStatistics quantityStatistics = new QuantityStatistics();
        quantityStatistics.setDataType(DataTypeEnum.AI_TWO.getCode());
        if(valueFlag) {
            quantityStatistics.setQuantity(new BigDecimal(aiTaskResultNewListTemp.size()));
        }else {
            quantityStatistics.setQuantity(BigDecimal.ZERO);
        }
        quantityStatistics.setTiming(newDate);
        quantityStatistics.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatistics);
        //AI审核材料通过数： 每一条 通过算一条
        QuantityStatistics quantityStatisticsThree = new QuantityStatistics();
        quantityStatisticsThree.setDataType(DataTypeEnum.AI_THREE.getCode());
        if(valueFlag) {
            long aiPassNum = aiTaskResultNewListTemp.stream().filter(item -> AiCheckResultEnum.AI_ONE.getCode().equals(item.getAiResult())).count();
            quantityStatisticsThree.setQuantity(new BigDecimal(aiPassNum));
        }else {
            quantityStatisticsThree.setQuantity(BigDecimal.ZERO);
        }
        quantityStatisticsThree.setTiming(newDate);
        quantityStatisticsThree.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsThree);
        //AI审核任务数:batid 一个算条
        QuantityStatistics quantityStatisticsZero = new QuantityStatistics();
        quantityStatisticsZero.setDataType(DataTypeEnum.AI_ZERO.getCode());
        Map<String, List<AiTaskResultNew>> batchIdMap = null;
        if(valueFlag) {
            batchIdMap = aiTaskResultNewListTemp.stream().filter(item -> StringUtils.isNotEmpty(item.getBatchId())).collect(Collectors.groupingBy(AiTaskResultNew::getBatchId));
            quantityStatisticsZero.setQuantity(new BigDecimal(batchIdMap.size()));
        }else {
            quantityStatisticsZero.setQuantity(BigDecimal.ZERO);
        }
        quantityStatisticsZero.setTiming(newDate);
        quantityStatisticsZero.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsZero);
        //AI审核任务通过数：batid 都通过 算一条
        QuantityStatistics quantityStatisticsOne = new QuantityStatistics();
        quantityStatisticsOne.setDataType(DataTypeEnum.AI_ONE.getCode());
        if(valueFlag) {
            long aiTaskPassNum = batchIdMap.values().stream().filter(item -> item.stream().allMatch(item1 -> AiCheckResultEnum.AI_ONE.getCode().equals(item1.getAiResult()))).count();
            quantityStatisticsOne.setQuantity(new BigDecimal(aiTaskPassNum));
        }else {
            quantityStatisticsOne.setQuantity(BigDecimal.ZERO);
        }
        quantityStatisticsOne.setTiming(newDate);
        quantityStatisticsOne.setSystemId(sysId);
        quantityStatisticsDao.insert(quantityStatisticsOne);
    }

    public static void main( String [] asgs ){
        Map<String,List<AiTaskResultNew>> map = new HashMap<>();
        List<AiTaskResultNew> list = new ArrayList<>();
        AiTaskResultNew a = new AiTaskResultNew();
        a.setBatchId("1");
        a.setAiResult(1);
        list.add(a);
        AiTaskResultNew ab = new AiTaskResultNew();
        ab.setBatchId("1");
        ab.setAiResult(1);
        list.add(ab);
        map.put("1", list);

        List<AiTaskResultNew> list2 = new ArrayList<>();
        AiTaskResultNew a2= new AiTaskResultNew();
        a2.setBatchId("2");
        a2.setAiResult(0);
        list2.add(a2);
        AiTaskResultNew ab2 = new AiTaskResultNew();
        ab2.setBatchId("2");
        ab2.setAiResult(1);
        list2.add(ab2);
        map.put("2", list2);

        List<AiTaskResultNew> list21 = new ArrayList<>();
        AiTaskResultNew a21= new AiTaskResultNew();
        a21.setBatchId("3");
        a21.setAiResult(1);
        list21.add(a21);
        AiTaskResultNew ab21 = new AiTaskResultNew();
        ab21.setBatchId("3");
        ab21.setAiResult(1);
        list21.add(ab21);
        map.put("3", list21);

        map.values().stream().forEach(item -> {
            System.out.println(item.size());
        });
        long aiTaskPassNum = map.values().stream().filter(item -> item.stream().allMatch(item1 -> item1.getAiResult() == 1)).count();


        System.out.println("eeee");
    }

	/**
    * 新增
    */
	@Override
	public Result<Integer> insert(QuantityStatistics req) {
        int result = 0;
        try {
            LocalDate startDate = DateUtils.convertDateToLocalDate(req.getStartTime());
            LocalDate endDate = DateUtils.convertDateToLocalDate(req.getEndTime());
            List<LocalDate> localDateList = DateUtils.getDatesBetween(startDate, endDate);
            if(CollectionUtils.isNotEmpty(localDateList)) {
                localDateList.stream().forEach(item -> {
                    QuantityStatistics  quantityStatistics = new QuantityStatistics();
                    quantityStatistics.setTiming(item.toString());
                    insertOrUpdate(quantityStatistics);
                });
            }

        } catch (Exception e) {
          log.error("error.impl.insert", e);
          return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
          return Result.success(result);

    }




 	/**
    * 分页全部查询
    */
    @Override
    public Result<List<QuantityStatisticsDayValueRes>>  getQuantityStatisticsList(QuantityStatistics quantityStatistics){
        if(StringUtils.isEmpty(quantityStatistics.getStartTime())){
             return Result.failed("开始时间不能为空");
        }
        if(StringUtils.isEmpty(quantityStatistics.getEndTime())){
             return Result.failed("结束时间不能为空");
        }
        if (quantityStatistics.getDataFlag()==null) {
            return Result.failed("入参列表类型不能为空");
        }
        quantityStatistics.setStartTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getStartTime()), DateUtils.FORMAT_DATE));
        quantityStatistics.setEndTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getEndTime()), DateUtils.FORMAT_DATE));


        List<QuantityStatisticsDayValueRes> res = new ArrayList<>();
		List<QuantityStatistics> list = quantityStatisticsDao.getIntervalQuantityStatisticsList(quantityStatistics);
        Map<String, List<QuantityStatistics>> sysIdMap = list.stream().collect(Collectors.groupingBy(QuantityStatistics::getSystemId));
        //系統分組
        sysIdMap.forEach((key,value) ->{
             QuantityStatisticsDayValueRes valueRes = new QuantityStatisticsDayValueRes();
             valueRes.setSystemId(key);
             valueRes.setSystemName(SystemIdEnum.getValue(key));
             List<QuantityStatisticsDayValue> resultList = new ArrayList<>();
             valueRes.setQuantityStatisticsDayValueList(resultList);
             //時間排序-分組
            LinkedHashMap<String, List<QuantityStatistics>> timeMap = value.stream().sorted(Comparator.comparing(QuantityStatistics::getTiming))
                    .collect(Collectors.groupingBy(QuantityStatistics::getTiming, LinkedHashMap::new, Collectors.toList()));
             timeMap.forEach((k,v) ->{
                 QuantityStatisticsDayValue quantityStatisticsDayValue = new QuantityStatisticsDayValue();
                 quantityStatisticsDayValue.setTiming(k);
                 quantityStatisticsDayValue.setSystemId(key);
                 quantityStatisticsDayValue.setSystemName(SystemIdEnum.getValue(key));
                 Map<Integer,QuantityStatistics> dataTypeMap = v.stream().collect(Collectors.toMap(e ->e.getDataType(), a -> a, (k1, k2) -> k1));
                 //AI审核任务通过率
                 if(quantityStatistics.getDataFlag().intValue()==0) {
                     BigDecimal aiAuditTaskPassRate = BigDecimal.ZERO;
                     BigDecimal aiTrue = BigDecimal.ZERO;
                     BigDecimal aiTotal = BigDecimal.ZERO;
                     if (dataTypeMap.get(DataTypeEnum.AI_ONE.getCode()) != null) {
                         aiTrue = dataTypeMap.get(DataTypeEnum.AI_ONE.getCode()).getQuantity();
                     }
                     if (dataTypeMap.get(DataTypeEnum.AI_ZERO.getCode()) != null) {
                         aiTotal = dataTypeMap.get(DataTypeEnum.AI_ZERO.getCode()).getQuantity();
                     }
                     if (aiTrue != null && aiTotal != null && aiTotal.compareTo(BigDecimal.ZERO) != 0) {
                         aiAuditTaskPassRate = aiTrue.divide(aiTotal, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                     }
                     quantityStatisticsDayValue.setAiAuditTaskPassRate(aiAuditTaskPassRate);
                 }else {
                     //人工复审材料准确率
                     BigDecimal manualAccuracyRate = BigDecimal.ZERO;
                     BigDecimal tp = BigDecimal.ZERO;
                     BigDecimal tn = BigDecimal.ZERO;
                     BigDecimal humTotal = BigDecimal.ZERO;
                     if (dataTypeMap.get(DataTypeEnum.TP.getCode()) != null) {
                         tp = dataTypeMap.get(DataTypeEnum.TP.getCode()).getQuantity();
                     }
                     if (dataTypeMap.get(DataTypeEnum.TN.getCode()) != null) {
                         tn = dataTypeMap.get(DataTypeEnum.TN.getCode()).getQuantity();
                     }
                     tp = tp.add(tn);
                     if (dataTypeMap.get(DataTypeEnum.HUM_SIX.getCode()) != null) {
                         humTotal = dataTypeMap.get(DataTypeEnum.HUM_SIX.getCode()).getQuantity();
                     }
                     if (tp != null && humTotal != null && humTotal.compareTo(BigDecimal.ZERO) != 0) {
                         manualAccuracyRate = tp.divide(humTotal, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                     }
                     quantityStatisticsDayValue.setManualAccuracyRate(manualAccuracyRate);
                 }
                 resultList.add(quantityStatisticsDayValue);
             });
             //较昨日
                if(quantityStatistics.getDataFlag().intValue()==0) {
                    for (int r = 0; r < resultList.size(); r++) {
                        if(r>0){
                            QuantityStatisticsDayValue quantityStatisticsDayValue = resultList.get(r);
                            QuantityStatisticsDayValue quantityStatisticsDayValuePre = resultList.get(r-1);
                                quantityStatisticsDayValue.setAiCompareYesterday(quantityStatisticsDayValue.getAiAuditTaskPassRate().subtract(quantityStatisticsDayValuePre.getAiAuditTaskPassRate()));
                           /* else {
                                quantityStatisticsDayValue.setManualCompareYesterday(quantityStatisticsDayValue.getManualAccuracyRate().subtract(quantityStatisticsDayValuePre.getManualAccuracyRate()));
                            }*/
                        }
                    }
                }
            res.add(valueRes);
             });

        return Result.success(res);

	}
    /**
     * ai审核汇总数据
     */
    @Override
    public Result<QuantityStatisticsTotalValue> getAiQuantityStatistics(QuantityStatistics quantityStatistics) {
        QuantityStatisticsTotalValue res = new QuantityStatisticsTotalValue();

       if(StringUtils.isEmpty(quantityStatistics.getStartTime())){
            return Result.failed("开始时间不能为空");
        }
        if(StringUtils.isEmpty(quantityStatistics.getEndTime())){
            return Result.failed("结束时间不能为空");
        }
        quantityStatistics.setStartTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getStartTime()), DateUtils.FORMAT_DATE));
        quantityStatistics.setEndTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getEndTime()), DateUtils.FORMAT_DATE));


        //AI审核任务通过率
        BigDecimal aiAuditTaskPassRate = BigDecimal.ZERO;
        BigDecimal aiTrue ;
        BigDecimal aiTotal ;
        //AI审核任务总数
        quantityStatistics.setDataType(DataTypeEnum.AI_TWO.getCode());
        Long  aiCount= quantityStatisticsDao.getAiCount(quantityStatistics);
        aiTotal = BigDecimal.valueOf(aiCount);
        //AI审核任务通过值
        quantityStatistics.setDataType(DataTypeEnum.AI_THREE.getCode());
        Long  aiTrueCount= quantityStatisticsDao.getAiCount(quantityStatistics);
        aiTrue = BigDecimal.valueOf(aiTrueCount);
        if(aiTrue!=null&&aiTotal!=null&&aiTotal.compareTo(BigDecimal.ZERO)!=0) {
            aiAuditTaskPassRate = aiTrue.divide(aiTotal, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        res.setAiAuditTaskPass(aiTrue);
        res.setAiAuditTaskTotal(aiTotal);
        res.setAiAuditTaskPassRate(aiAuditTaskPassRate);

        //AI批次-审核任务通过率
        BigDecimal aiBatchAuditTaskPassRate = BigDecimal.ZERO;
        BigDecimal aiBatchTrue ;
        BigDecimal aiBatchTotal;
        quantityStatistics.setDataType(DataTypeEnum.AI_ZERO.getCode());
        Long  aiBatchCount= quantityStatisticsDao.getAiCount(quantityStatistics);
        aiBatchTotal = BigDecimal.valueOf(aiBatchCount);
        quantityStatistics.setDataType(DataTypeEnum.AI_ONE.getCode());
        Long  aiBatchTrueCount= quantityStatisticsDao.getAiCount(quantityStatistics);
        aiBatchTrue = BigDecimal.valueOf(aiBatchTrueCount);
        if(aiBatchTrue!=null&&aiBatchTotal!=null&&aiBatchTotal.compareTo(BigDecimal.ZERO)!=0){
             aiBatchAuditTaskPassRate = aiBatchTrue.divide(aiBatchTotal, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        res.setAiBatchAuditTaskPassRate(aiBatchAuditTaskPassRate);
        res.setAiBatchAuditTaskPass(aiBatchTrue);
        res.setAiBatchAuditTaskTotal(aiBatchTotal);




        return Result.success(res);
    }
    /**
     * 人工审核汇总数据
     */
    @Override
    public Result<QuantityStatisticsTotalValue> getHumQuantityStatistics(QuantityStatistics quantityStatistics){
        QuantityStatisticsTotalValue res = new QuantityStatisticsTotalValue();
       if(StringUtils.isEmpty(quantityStatistics.getStartTime())){
            return Result.failed("开始时间不能为空");
        }
        if(StringUtils.isEmpty(quantityStatistics.getEndTime())){
            return Result.failed("结束时间不能为空");
        }
        quantityStatistics.setStartTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getStartTime()), DateUtils.FORMAT_DATE));
        quantityStatistics.setEndTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getEndTime()), DateUtils.FORMAT_DATE));

        //AI审核任务通过率
        BigDecimal humAuditTaskPassRate = BigDecimal.ZERO;
        BigDecimal humTrue ;
        BigDecimal humTotal ;
        //人工复审材料数：每一条记录
        quantityStatistics.setDataType(DataTypeEnum.HUM_SIX.getCode());
        Long  aiCount= quantityStatisticsDao.getAiCount(quantityStatistics);
        humTotal = BigDecimal.valueOf(aiCount);
        //TP数：ai √ ，人工 √
        quantityStatistics.setDataType(DataTypeEnum.TP.getCode());
        Long  tpCount= quantityStatisticsDao.getAiCount(quantityStatistics);
        //TN数：ai × ，人工 ×
        quantityStatistics.setDataType(DataTypeEnum.TN.getCode());
        Long  tnCount= quantityStatisticsDao.getAiCount(quantityStatistics);

        humTrue = BigDecimal.valueOf(tpCount+tnCount);
        if(humTrue!=null&&humTotal!=null&&humTotal.compareTo(BigDecimal.ZERO)!=0) {
            humAuditTaskPassRate = humTrue.divide(humTotal, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        res.setManualAccuracy(humTrue);
        res.setManualAccuracyTotal(humTotal);
        res.setManualAccuracyRate(humAuditTaskPassRate);

        return Result.success(res);
    }

    /**
     * 人工审核汇总数据
     */
    @Override
    public Result<QuantityStatisticsTotalTp> getHumTpStatistics(@RequestBody QuantityStatistics quantityStatistics){
        log.info("getHumTpStatistics.response: {}", JSONObject.toJSONString(quantityStatistics));

        if(StringUtils.isEmpty(quantityStatistics.getStartTime())){
            return Result.failed("开始时间不能为空");
        }
        if(StringUtils.isEmpty(quantityStatistics.getEndTime())){
            return Result.failed("结束时间不能为空");
        }
        quantityStatistics.setStartTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getStartTime()), DateUtils.FORMAT_DATE));
        quantityStatistics.setEndTime(DateUtils.formatDate(DateUtils.getDateByString(quantityStatistics.getEndTime()), DateUtils.FORMAT_DATE));

        QuantityStatisticsTotalTp res = new QuantityStatisticsTotalTp();
        //TP数：ai √ ，人工 √
        BigDecimal tpNum = BigDecimal.ZERO;
        //FP数：ai √ ，人工 ×
        BigDecimal fpNum = BigDecimal.ZERO;
        //TN数：ai × ，人工 ×
        BigDecimal tnNum = BigDecimal.ZERO;
        //FN数：ai × ，人工 √
        BigDecimal fnNum = BigDecimal.ZERO;

        quantityStatistics.setDataTypeList(Arrays.asList(DataTypeEnum.TP.getCode(),DataTypeEnum.FP.getCode(),DataTypeEnum.TN.getCode(),DataTypeEnum.FN.getCode()));
        List<QuantityStatistics>  quantityStatisticsList = quantityStatisticsDao.getAiCountGroup(quantityStatistics);
        Map<Integer,BigDecimal> quantityMap = quantityStatisticsList.stream().collect(Collectors.toMap(QuantityStatistics::getDataType, QuantityStatistics::getQuantity));
        if(quantityMap.get(DataTypeEnum.TP.getCode())!=null){
            tpNum = quantityMap.get(DataTypeEnum.TP.getCode());
        }
        if(quantityMap.get(DataTypeEnum.FP.getCode())!=null){
            fpNum = quantityMap.get(DataTypeEnum.FP.getCode());
        }
        if(quantityMap.get(DataTypeEnum.TN.getCode())!=null){
            tnNum = quantityMap.get(DataTypeEnum.TN.getCode());
        }
        if(quantityMap.get(DataTypeEnum.FN.getCode())!=null){
            fnNum = quantityMap.get(DataTypeEnum.FN.getCode());
        }
        BigDecimal sum = tpNum.add(fpNum).add(tnNum).add(fnNum);
        res.setTpNum(tpNum);
        res.setFpNum(fpNum);
        res.setTnNum(tnNum);
        res.setFnNum(fnNum);
        if(sum.compareTo(BigDecimal.ZERO)!=0){
            res.setTpRate(tpNum.divide(sum, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
            res.setFpRate(fpNum.divide(sum, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
            res.setTnRate(tnNum.divide(sum, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
            res.setFnRate(fnNum.divide(sum, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
        }else {
            res.setTpRate(BigDecimal.ZERO);
            res.setFpRate(BigDecimal.ZERO);
            res.setTnRate(BigDecimal.ZERO);
            res.setFnRate(BigDecimal.ZERO);
        }


        return Result.success(res);

    }



}

