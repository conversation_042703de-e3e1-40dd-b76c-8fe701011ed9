package com.faw.work.ais.aic.common.enums;

import lombok.Getter;

/**
 * 知识库数据类型枚举
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Getter
public enum RagKnowledgeDataTypeEnum {
    /**
     * 非结构文档
     */
    UNSTRUCTURED("00", "非结构文档pdf doc"),
    /**
     * 结构化文档
     */
    STRUCTURED("01", "结构化文档excel");

    RagKnowledgeDataTypeEnum(String code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final String code;

    private final String msg;
} 