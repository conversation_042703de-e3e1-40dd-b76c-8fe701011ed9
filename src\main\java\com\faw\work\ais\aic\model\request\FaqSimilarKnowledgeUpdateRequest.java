package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FAQ相似问请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ相似问请求对象")
public class FaqSimilarKnowledgeUpdateRequest {
    
    @Schema(description = "主键ID")
    private String id;
    
    @NotBlank(message = "相似问不能为空")
    @Schema(description = "相似问")
    private String similarQuestion;

} 