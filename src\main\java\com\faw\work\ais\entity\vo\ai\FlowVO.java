package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "流程信息")
@Data
public class FlowVO {

    /**
     * 流程列表l3
     */
    @Schema(description = "流程列表l3")
    List<FlowInfoVO> l3flowInfoList;

    /**
     * 流程列表l4
     */
    @Schema(description = "流程列表l4")
    List<FlowInfoVO> l4flowInfoList;

    /**
     * 流程列表l5
     */
    @Schema(description = "流程列表l5")
    List<FlowInfoVO> l5flowInfoList;

}
