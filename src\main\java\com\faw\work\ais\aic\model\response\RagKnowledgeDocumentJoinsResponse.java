package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 知识库文档关联响应
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "知识库文档关联响应")
public class RagKnowledgeDocumentJoinsResponse {

    @Schema(description = "绑定ID")
    private String id;

    @Schema(description = "知识库id")
    private Long baseId;

    @Schema(description = "文档id")
    private Long documentId;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
} 