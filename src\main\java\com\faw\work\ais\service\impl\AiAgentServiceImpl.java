package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.MessageConstants;
import com.faw.work.ais.common.PromptConstants;
import com.faw.work.ais.common.dto.chat.AiAgentRequest;
import com.faw.work.ais.common.dto.chat.AiChatResponse;
import com.faw.work.ais.common.dto.chat.ToolCacheEntity;
import com.faw.work.ais.common.enums.chat.AnswerSourceEnum;
import com.faw.work.ais.common.enums.chat.ChatSceneEnum;
import com.faw.work.ais.common.enums.chat.ChatToolEnum;
import com.faw.work.ais.common.enums.chat.GenderEnum;
import com.faw.work.ais.common.util.DateUtils;
import com.faw.work.ais.config.chat.ChatRedisMemory;
import com.faw.work.ais.feign.chat.AliYunFeignClient;
import com.faw.work.ais.service.AiAgentService;
import com.faw.work.ais.service.tool.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbacks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Ai智能体 实现类
 *
 * <AUTHOR>
 * @since 2025-06-23 10:13
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiAgentServiceImpl implements AiAgentService {

    private final ChatClient chatClient;

    private final RedisService redisService;

    private final ChatRedisMemory chatMemory;

    private final ChatClient universalClient;

    private final ChatClient recommendClient;

    private final CommonService commonService;

    private final FunctionService functionService;

    private final KnowledgeService knowledgeService;

    private final VehicleService vehicleService;

    private final AliYunFeignClient aliYunFeignClient;


    private static final Map<String, String> GENDER_MAP = Map.of(GenderEnum.FEMALE.getDesc(), GenderEnum.MALE.getDesc());


    private final Map<String, ToolHandler> toolHandlers = Map.of(
            ChatToolEnum.GET_VEHICLE_MANUAL.getName(), this::handleGetVehicleManual,
            ChatToolEnum.MAINTENANCE.getName(), this::handleMaintenance,
            ChatToolEnum.STAFF_SERVICE.getName(), this::handleStaffService,
            ChatToolEnum.PICK_UP_CAR.getName(), this::handlePickUpCar);


    @Override
    public String getAgentResponse(AiAgentRequest request) {
        log.info("[AiAgentService][getAgentResponse][entrance] request: {}", JSON.toJSONString(request));
        ChatSceneEnum intention = ChatSceneEnum.getByCode(request.getSceneCode());

        return switch (intention) {
            case ANALYZE_PHOTO -> new PhotoAnalysisService(aliYunFeignClient)
                    .analyzePhoto(request.getImageUrls(), request.getQuestion());
            case IDENTIFY_INTENT -> universalClient
                    .prompt(String.format(PromptConstants.IDENTIFY_INTENT_PROMPT, request.getQuestion()))
                    .call().content();
            case RECOMMEND -> recommendClient
                    .prompt(String.format(PromptConstants.RECOMMEND_PROMPT, request.getQuestion()))
                    .call().content();
            default -> "未知智能体";
        };
    }

    @Override
    public Flux<AiChatResponse> getAgentFluxResponse(AiAgentRequest request) {
        log.info("[AiAgentService][getAgentFluxResponse][entrance] request: {}", JSON.toJSONString(request));
        // 1 init params
        String currentTime = DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE_TIME);
        String dayWeek = DateUtils.getWeekOfDate(DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE));

        // 2 build params
        String initPrompt;
        ToolCallback[] toolArray;
        Map<String, Object> toolContext = new HashMap<>(Map.of(
                CommonConstants.KEY_CACHE_ID, request.getCacheId(),
                CommonConstants.KEY_USER_INFO, request.getUserInfo()));

        ChatSceneEnum intention = ChatSceneEnum.getByCode(request.getSceneCode());
        toolArray = switch (intention) {
            case SMALL_TALK -> {
                String gender = GenderEnum.FEMALE.getDesc();
                if (StringUtils.isNotEmpty(request.getUserInfo().getGender())) {
                    gender = GENDER_MAP.getOrDefault(request.getUserInfo().getGender(), GenderEnum.FEMALE.getDesc());
                }

                initPrompt = String.format(PromptConstants.SMALL_TALK_PROMPT, gender);
                yield ToolCallbacks.from(commonService);
            }
            case KNOWLEDGE -> {
                initPrompt = String.format(PromptConstants.KNOWLEDGE_PROMPT, request.getUserInfo().getSeries());
                yield ToolCallbacks.from(knowledgeService);
            }
            case STAFF_SERVICE -> {
                initPrompt = PromptConstants.STAFF_SERVICE_PROMPT;
                yield ToolCallbacks.from(functionService);
            }
            case PICK_UP_CAR -> {
                initPrompt = String.format(PromptConstants.PICK_UP_CAR_PROMPT, currentTime, dayWeek);
                yield ToolCallbacks.from(vehicleService);
            }
            case MAINTENANCE -> {
                initPrompt = PromptConstants.MAINTENANCE_PROMPT;
                yield ToolCallbacks.from(vehicleService);
            }
            case VEHICLE_FAULT -> {
                initPrompt = PromptConstants.VEHICLE_FAULT_PROMPT;
                yield ToolCallbacks.from(vehicleService);
            }
            case REPAIR_PROGRESS -> {
                initPrompt = PromptConstants.REPAIR_PROGRESS_PROMPT;
                yield ToolCallbacks.from(vehicleService);
            }
            default -> {
                initPrompt = String.format(PromptConstants.CHAT_MODEL_INIT_PROMPT, currentTime);
                yield ToolCallbacks.from(commonService, functionService, knowledgeService, vehicleService);
            }
        };
        log.info("[AiAgentService][getAgentFluxResponse] initPrompt: {}", initPrompt);

        // 3 execute agent
        AiChatResponse response = AiChatResponse.builder().chatId(request.getCacheId())
                .answerSource(AnswerSourceEnum.BIG_MODEL.getCode()).sceneCode(ChatSceneEnum.KNOWLEDGE.getCode()).build();
        if (ChatSceneEnum.PICK_UP_CAR.getCode().equals(request.getSceneCode())) {
            response.setSceneCode(ChatSceneEnum.PICK_UP_CAR.getCode());
        }

        Flux<AiChatResponse> chatResult;
        try {
            chatResult = chatClient.prompt(initPrompt)
                    .user(request.getQuestion())
                    .advisors(new MessageChatMemoryAdvisor(chatMemory, request.getChatId(), CommonConstants.HUNDRED))
                    .toolContext(toolContext)
                    .tools(toolArray)
                    .stream().chatResponse().map(
                            chatResponse -> this.afterChat(chatResponse, response, request.getCacheId()));
        } catch (Exception e) {
            log.warn("[AiAgentService][getAgentFluxResponse][exception] e: ", e);
            response.setContent(MessageConstants.AI_CHAT_ERROR);
            chatResult = Flux.just(response);
        }

        return chatResult;
    }

    /**
     * 聊天后处理
     *
     * @param chatResponse 大模型响应
     * @param response     响应信息暂存对象
     * @return 接口响应
     */
    private AiChatResponse afterChat(ChatResponse chatResponse, AiChatResponse response, String cacheId) {
        log.info("[AiAgentService][afterChat][entrance] chatResponse: {}, response: {}", JSON.toJSONString(chatResponse), JSON.toJSONString(response));

        // 1 analysis toolCache
        String toolCacheStr = (String) redisService.get(cacheId);
        log.info("[AiAgentService][afterChat] toolCacheStr: {}", toolCacheStr);
        if (StringUtils.isNotEmpty(toolCacheStr)) {
            ToolCacheEntity toolCache = JSON.parseObject(toolCacheStr, ToolCacheEntity.class);
            response.setThoughtChain(toolCache.getThoughtChain());

            ToolHandler handler = toolHandlers.get(toolCache.getToolName());
            if (handler != null) {
                handler.handle(toolCache, response);
            }

            // delete cache
            redisService.delete(cacheId);
        }

        // 2 build response
        return AiChatResponse.builder()
                .chatId(response.getChatId())
                .content(chatResponse.getResult().getOutput().getText())
                .thoughtChain(response.getThoughtChain())
                .answerSource(response.getAnswerSource())
                .sceneCode(response.getSceneCode())
                .finishReason(chatResponse.getResult().getMetadata().getFinishReason())
                .params(response.getParams())
                .build();
    }

    /**
     * 工具后处理方法（车辆手册信息获取）
     *
     * @param toolCache 工具缓存
     * @param response  响应
     */
    private void handleGetVehicleManual(ToolCacheEntity toolCache, AiChatResponse response) {
        if (Boolean.TRUE.equals(toolCache.getToolStatus())) {
            response.setSceneCode(ChatSceneEnum.KNOWLEDGE.getCode());
            response.setAnswerSource(AnswerSourceEnum.KNOWLEDGE_BASE.getCode());
        } else {
            response.setSceneCode(ChatSceneEnum.STAFF_SERVICE.getCode());
            response.setParams(toolCache.getToolValue());
        }
    }

    /**
     * 工具后处理方法（预约维修保养方法）
     *
     * @param toolCache 工具缓存
     * @param response  响应
     */
    private void handleMaintenance(ToolCacheEntity toolCache, AiChatResponse response) {
        if (Boolean.TRUE.equals(toolCache.getToolStatus())) {
            response.setSceneCode(ChatSceneEnum.MAINTENANCE.getCode());
            response.setParams(toolCache.getToolValue());
        } else {
            response.setSceneCode(ChatSceneEnum.KNOWLEDGE.getCode());
        }
    }

    /**
     * 工具后处理方法（人工客服方法）
     *
     * @param toolCache 工具缓存
     * @param response  响应
     */
    private void handleStaffService(ToolCacheEntity toolCache, AiChatResponse response) {
        if (Boolean.TRUE.equals(toolCache.getToolStatus())) {
            response.setSceneCode(ChatSceneEnum.STAFF_SERVICE.getCode());
            response.setParams(toolCache.getToolValue());
        }
    }

    /**
     * 工具后处理方法（预约上门取车方法）
     *
     * @param toolCache 工具缓存
     * @param response  响应
     */
    private void handlePickUpCar(ToolCacheEntity toolCache, AiChatResponse response) {
        if (Boolean.TRUE.equals(toolCache.getToolStatus())) {
            response.setSceneCode(ChatSceneEnum.PICK_UP_CAR.getCode());
            response.setParams(toolCache.getToolValue());
        } else {
            response.setSceneCode(ChatSceneEnum.KNOWLEDGE.getCode());
        }
    }

}
