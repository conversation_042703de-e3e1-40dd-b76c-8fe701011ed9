package com.faw.work.ais.feign;

import com.faw.work.ais.feign.interceptor.IworkProdOpenApiFeignInterceptor;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * iwork开放api  feign调用接口，物理机特殊只有生产环境可以调用
 *
 * <AUTHOR>
 */
@FeignClient(value = "IworkProdOpenApiFeignClient", url = "https://prod-api.faw.cn" , configuration = IworkProdOpenApiFeignInterceptor.class)
public interface IworkProdOpenApiFeignClient {

    /**
     * 回调接口-固定url
     *
     * @param uploadFile
     * @return Object
     */
    @PostMapping(value = "/JT/SA/SA-0214/AIS/DEFAULT/frontCut", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    Response frontCut(@RequestPart("uploadFile") MultipartFile uploadFile);

}
