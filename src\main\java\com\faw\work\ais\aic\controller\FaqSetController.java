package com.faw.work.ais.aic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.faw.work.ais.aic.common.aop.IdempotentLock;
import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.domain.FaqRobotPO;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.FaqCategoryResponse;
import com.faw.work.ais.aic.model.response.FaqKnowledgeCopyResponse;
import com.faw.work.ais.aic.model.response.FaqKnowledgeDetailResponse;
import com.faw.work.ais.aic.model.response.FaqKnowledgeListResponse;
import com.faw.work.ais.aic.model.response.FaqReportResponse;
import com.faw.work.ais.aic.model.response.FaqRobotResponse;
import com.faw.work.ais.aic.service.*;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * FAQ设置Controller
 * 包含知识库、机器人、类目、相似问的CRUD操作
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/faq-set")
@Tag(name = "类FAQ设置管理", description = "[author:10200571]")
@Slf4j
public class FaqSetController {

    @Autowired
    private FaqRobotKnowledgeJoinsService faqRobotKnowledgeJoinsService;
    @Autowired
    private FaqKnowledgeService faqKnowledgeService;
    @Autowired
    private FaqRobotService faqRobotService;
    @Autowired
    private FaqCategoryService faqCategoryService;
    @Autowired
    private FaqSimilarKnowledgeService faqSimilarKnowledgeService;
    @Autowired
    private FaqReportService faqReportService;

    // ==================== 运营报表相关接口 ====================
    @PostMapping("/report-query")
    @Operation(summary = "查询运营报表", description = "[author:10200571]")
    public AiResult<FaqReportResponse> queryReport(@RequestBody @Valid FaqReportQueryRequest request) {
        FaqReportResponse report = faqReportService.getReport(request);
        return AiResult.success(report);
    }

    @PostMapping("/knowledge-list")
    @Operation(summary = "运营报表查询知识列表", description = "[author:10200571]")
    public AiResult<IPage<FaqKnowledgeListResponse>> listKnowledge(@RequestBody @Valid FaqKnowledgeListRequest request) {
        IPage<FaqKnowledgeListResponse> page = faqKnowledgeService.listReportKnowledge(request);
        return AiResult.success(page);
    }

    // ==================== 知识库相关接口 ====================

    @GetMapping("/knowledge-publish")
    @Operation(summary = "发布知识", description = "[author:10200571]")
    @IdempotentLock(duration = 10L)
    public AiResult<Boolean> knowledgeRobot(@RequestParam("knowledgeId") String knowledgeId) throws InterruptedException {
        faqRobotKnowledgeJoinsService.migrateKnowledge(knowledgeId);
        return AiResult.success(Boolean.TRUE);
    }

    @PostMapping("/knowledge-create")
    @Operation(summary = "创建知识", description = "[author:10200571]")
    @IdempotentLock(duration = 10L)
    public AiResult<String> createKnowledge(@RequestBody @Valid FaqKnowledgeCreateRequest request) {
        String id = faqKnowledgeService.createKnowledge(request);
        return AiResult.success(id);
    }


    @PostMapping("/knowledge-update")
    @Operation(summary = "更新知识", description ="[author:10200571]")
    @IdempotentLock(duration = 10L)
    public AiResult<Boolean> updateKnowledge(@RequestBody @Valid FaqKnowledgeUpdateRequest request) {
        boolean success = faqKnowledgeService.updateKnowledge(request);
        return AiResult.success(success);
    }

    @PostMapping("/knowledge-effective-set")
    @Operation(summary = "生效配置", description = "[author:10200571]")
    @IdempotentLock(duration = 10L)
    public AiResult<Boolean> setEffectiveConfig(@RequestBody @Valid FaqKnowledgeSetEffectRequest request) {
        String id = request.getId();
        FaqKnowledgePO knowledge = faqKnowledgeService.getById(id);
        knowledge.setEffectiveType(request.getEffectiveType());
        knowledge.setEffectiveStartTime(request.getEffectiveStartTime());
        knowledge.setEffectiveEndTime(request.getEffectiveEndTime());
        knowledge.setUpdatedBy(UserThreadLocalUtil.getCurrentName());
        knowledge.setPublishStatus("00");
        knowledge.setUpdatedAt(LocalDateTime.now());
        return AiResult.success(faqKnowledgeService.updateById(knowledge));
    }

    @GetMapping("/knowledge-delete/{id}")
    @Operation(summary = "删除知识", description = "[author:10200571]")
    public AiResult<Boolean> deleteKnowledge(@PathVariable("id") @Parameter(description = "知识ID") String id) {
        faqKnowledgeService.deleteKnowledge(id);
        return AiResult.success(Boolean.TRUE);
    }

    @GetMapping("/knowledge-detail/{id}/{env}")
    @Operation(summary = "获取知识详情", description = "[author:10200571]")
    public AiResult<FaqKnowledgeDetailResponse> getKnowledgeDetail(@PathVariable("id") String id, @PathVariable("env") String env) {
        FaqKnowledgePO knowledge = faqKnowledgeService.getKnowledgeDetail(id, env);
        List<FaqSimilarKnowledgePO> similarKnowledges = faqSimilarKnowledgeService.listByOriginalId(id, env);
        FaqKnowledgeDetailResponse response = new FaqKnowledgeDetailResponse(knowledge, similarKnowledges);
        return AiResult.success(response);
    }

    @PostMapping("/knowledge-page")
    @Operation(summary = "分页查询知识列表", description = "[author:10200571]")
    public AiResult<IPage<FaqKnowledgePO>> pageKnowledge(@RequestBody FaqKnowledgeQueryRequest request) {
        IPage<FaqKnowledgePO> page = faqKnowledgeService.pageKnowledge(request);
        return AiResult.success(page);
    }

    @PostMapping("/knowledge-transfer")
    @Operation(summary = "知识转移", description = "[author:10200571]")
    @IdempotentLock(duration = 10L)
    public AiResult<Boolean> transferKnowledge(@RequestBody @Valid FaqKnowledgeTransferRequest request) {
        faqKnowledgeService.transferKnowledge(request);
        return AiResult.success(Boolean.TRUE);
    }

    @PostMapping("/knowledge-copy")
    @Operation(summary = "知识复制", description = "[author:10200571]")
    @IdempotentLock(duration = 30L)
    @Transactional(rollbackFor = Exception.class)
    public AiResult<String> copyKnowledge(@RequestBody @Valid List<FaqKnowledgeCopyRequest> request) {
        for (FaqKnowledgeCopyRequest faqKnowledgeCopyRequest : request) {
            faqKnowledgeService.copyKnowledge(faqKnowledgeCopyRequest);
        }
        return AiResult.success("复制成功");
    }

    // ==================== 机器人相关接口 ====================

    @PostMapping("/robot-saveOrUpdate")
    @Operation(summary = "提交-新建或编辑机器人", description = "[author:10200571]")
    @IdempotentLock(duration = 120L)
    public AiResult<String> saveOrUpdateRobot(@RequestBody @Valid FaqRobotSubmitRequest request) {
        String robotId = request.getId();
        if (robotId == null) {
            robotId = faqRobotService.createRobot(request);
        } else {
            faqRobotService.updateRobot(request);
        }
        return AiResult.success(robotId);
    }


    @GetMapping("/robot-publish")
    @Operation(summary = "发布机器人", description = "[author:10200571]")
    @IdempotentLock(duration = 30L)
    public AiResult<Boolean> publishRobot(@RequestParam("robotId") String robotId) throws InterruptedException {
        boolean success = faqRobotService.publishRobot(robotId);
        return AiResult.success(success);
    }

    @GetMapping("/robot-delete/{id}")
    @Operation(summary = "删除机器人", description = "[author:10200571]")
    @Transactional(rollbackFor = Exception.class)
    public AiResult<Boolean> deleteRobot(@PathVariable("id") @Parameter(description = "机器人ID") String id) {
        boolean success = faqRobotService.removeById(id);
        // 删除关联的知识
        faqRobotService.deleteByRobotId(id);
        return AiResult.success(success);
    }

    @GetMapping("/robot-detail/{id}/{env}")
    @Operation(summary = "获取机器人详情", description = "[author:10200571]")
    public AiResult<FaqRobotResponse> getRobotDetail(@PathVariable("id") @Parameter(description = "机器人ID") String id,
                                                     @PathVariable("env") String env) {
        return AiResult.success(faqRobotService.getRobotDetail(id, env));
    }

    @PostMapping("/robot-list")
    @Operation(summary = "分页查询机器人列表", description = "[author:10200571]")
    public AiResult<IPage<FaqRobotPO>> pageRobot(@Valid @RequestBody FaqRobotQueryRequest request) {
        IPage<FaqRobotPO> page = faqRobotService.pageRobot(request);
        for (FaqRobotPO record : page.getRecords()) {
            switch (record.getStatus()) {
                case 0 -> record.setTextStatus("草稿");
                case 1 -> record.setTextStatus("已发布");
                case 2 -> record.setTextStatus("发布中");
                case 5 -> record.setTextStatus("绑定知识中");
                case 10 -> record.setTextStatus("发布失败");
                default -> record.setTextStatus("未知状态");
            }

            switch (record.getStatus()) {
                case 0 -> record.setProdStatus("未发布");
                case 1 -> record.setProdStatus("已发布V" + record.getVersion());
                case 2 -> record.setProdStatus("发布中...");
                case 10 -> record.setProdStatus("发布失败");
                default -> record.setProdStatus("未知状态");
            }
        }
        return AiResult.success(page);
    }

    // ==================== 知识类目相关接口 ====================

    @GetMapping("/category-tree/{env}")
    @Operation(summary = "类目树形结构", description =  "[author:10200571]")
    public AiResult<List<FaqCategoryResponse>> treeCategory(@PathVariable("env") String env) {
        List<FaqCategoryResponse> responseList = faqCategoryService.treeCategory(env);
        return AiResult.success(responseList);
    }

    @PostMapping("/category-create")
    @Operation(summary = "创建类目", description = "[author:10200571]")
    public AiResult<String> createFlexibleCategory(@RequestBody @Valid FaqCategoryRequest request) {
        return AiResult.success(faqCategoryService.createFlexibleCategory(request));
    }

    @PostMapping("/category-update")
    @Operation(summary = "编辑类目", description = "[author:10200571]")
    public AiResult<Boolean> updateCategory(@RequestBody @Valid FaqCategoryRequest request) {
        if (request.getId() == null) {
            return AiResult.fail("类目ID不能为空");
        }

        FaqCategoryPO category = new FaqCategoryPO();
        category.setId(request.getId());
        category.setName(request.getName());
        category.setUpdatedBy(UserThreadLocalUtil.getCurrentName());

        boolean success = faqCategoryService.updateById(category);
        return AiResult.success(success);
    }

    @GetMapping("/category-delete/{id}")
    @Operation(summary = "删除类目", description = "[author:10200571]")
    public AiResult<Boolean> deleteCategory(@PathVariable("id") @Parameter(description = "类目ID") String id) {
        faqCategoryService.deleteAllDataByCategoryId(id);
        return AiResult.success(Boolean.TRUE);
    }

}