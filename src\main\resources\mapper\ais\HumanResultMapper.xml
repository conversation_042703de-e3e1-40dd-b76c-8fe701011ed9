<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.HumanResultMapper">
  <resultMap id="BaseResultMap" type="com.faw.work.ais.model.HumanResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="human_check_result_single" jdbcType="VARCHAR" property="humanCheckResultSingle" />
    <result column="human_refuse_reason" jdbcType="VARCHAR" property="humanRefuseReason" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="human_check_time" jdbcType="TIMESTAMP" property="humanCheckTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="human_check_result_final" jdbcType="VARCHAR" property="humanCheckResultFinal" />
    <result column="ai_result_single" jdbcType="VARCHAR" property="aiResultSingle"/>
    <result column="ai_result_final" jdbcType="VARCHAR" property="aiResultFinal"/>
    <result column="ai_result_time" jdbcType="TIMESTAMP" property="aiResultTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, human_check_result_single, human_refuse_reason, batch_id, trace_id, biz_id, task_type, 
    human_check_time, create_time, system_id, human_check_result_final,biz_type, ai_result_single, ai_result_final, ai_result_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from human_result_new
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from human_result_new
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.HumanResult" useGeneratedKeys="true">
    insert into human_result_new (human_check_result_single, human_refuse_reason,
      batch_id, trace_id, biz_id, 
      task_type, human_check_time, create_time, 
      system_id, human_check_result_final, biz_type, ai_result_single, ai_result_final, ai_result_time)
    values (#{humanCheckResultSingle,jdbcType=VARCHAR}, #{humanRefuseReason,jdbcType=VARCHAR},
      #{batchId,jdbcType=VARCHAR}, #{traceId,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=VARCHAR}, #{humanCheckTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{systemId,jdbcType=VARCHAR}, #{humanCheckResultFinal,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR},
      #{aiResultSingle,jdbcType=VARCHAR}, #{aiResultFinal,jdbcType=VARCHAR}, #{aiResultTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.HumanResult" useGeneratedKeys="true">
    insert into human_result_new
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="humanCheckResultSingle != null">
        human_check_result_single,
      </if>
      <if test="humanRefuseReason != null">
        human_refuse_reason,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="traceId != null">
        trace_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="humanCheckTime != null">
        human_check_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="humanCheckResultFinal != null">
        human_check_result_final,
      </if>
      <if test="aiResultSingle != null">
        ai_result_single,
      </if>
      <if test="aiResultFinal != null">
        ai_result_final,
      </if>
      <if test="aiResultTime != null">
        ai_result_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="humanCheckResultSingle != null">
        #{humanCheckResultSingle,jdbcType=VARCHAR},
      </if>
      <if test="humanRefuseReason != null">
        #{humanRefuseReason,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="traceId != null">
        #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="humanCheckTime != null">
        #{humanCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="humanCheckResultFinal != null">
        #{humanCheckResultFinal,jdbcType=VARCHAR},
      </if>
      <if test="aiResultSingle != null">
        #{aiResultSingle,jdbcType=VARCHAR},
      </if>
      <if test="aiResultFinal != null">
        #{aiResultFinal,jdbcType=VARCHAR},
      </if>
      <if test="aiResultTime != null">
        #{aiResultTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.faw.work.ais.model.HumanResult">
    update human_result_new
    <set>
      <if test="humanCheckResultSingle != null">
        human_check_result_single = #{humanCheckResultSingle,jdbcType=VARCHAR},
      </if>
      <if test="humanRefuseReason != null">
        human_refuse_reason = #{humanRefuseReason,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="traceId != null">
        trace_id = #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="humanCheckTime != null">
        human_check_time = #{humanCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="humanCheckResultFinal != null">
        human_check_result_final = #{humanCheckResultFinal,jdbcType=VARCHAR},
      </if>
      <if test="aiResultSingle != null">
        ai_result_single = #{aiResultSingle,jdbcType=VARCHAR},
      </if>
      <if test="aiResultFinal != null">
        ai_result_final = #{aiResultFinal,jdbcType=VARCHAR},
      </if>
      <if test="aiResultTime != null">
        ai_result_time = #{aiResultTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.faw.work.ais.model.HumanResult">
    update human_result_new
    set human_check_result_single = #{humanCheckResultSingle,jdbcType=VARCHAR},
      human_refuse_reason = #{humanRefuseReason,jdbcType=VARCHAR},
      batch_id = #{batchId,jdbcType=VARCHAR},
      trace_id = #{traceId,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=VARCHAR},
      human_check_time = #{humanCheckTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      system_id = #{systemId,jdbcType=VARCHAR},
      human_check_result_final = #{humanCheckResultFinal,jdbcType=VARCHAR},
      ai_result_single = #{aiResultSingle,jdbcType=VARCHAR},
      ai_result_final = #{aiResultFinal,jdbcType=VARCHAR},
      ai_result_time = #{aiResultTime,jdbcType=TIMESTAMP}
    where id = #{id}
  </update>

  <delete id="deleteByBatchIdAndTraceIdInt" parameterType="java.lang.String">
    delete from human_result_new
    where batch_id = #{batchId} and trace_id = #{traceId}
  </delete>

  <select id="getHumanResultThisList" resultMap="BaseResultMap">
    SELECT    batch_id, trace_id, system_id, human_check_result_single,ai_result_single
    FROM human_result_new
    where human_check_time >= #{aiTaskResultNew.startTime}
      and human_check_time <![CDATA[<=]]>  #{aiTaskResultNew.endTime}

  </select>

  <sql id="select_condition_sql">
    <if test="dto.systemId != null and dto.systemId != ''">
      and system_id = #{dto.systemId}
    </if>
    <if test="dto.batchId != null and dto.batchId != ''">
      and batch_id = #{dto.batchId}
    </if>
    <if test="dto.traceId != null and dto.traceId != ''">
      and trace_id = #{dto.traceId}
    </if>
    <if test="dto.taskType != null and dto.taskType != ''">
      and task_type = #{dto.taskType}
    </if>
    <if test="dto.humanCheckTimeStart != null and dto.humanCheckTimeStart != ''">
      and human_check_time &gt;= TO_DATE(#{dto.humanCheckTimeStart}, '%Y-%m-%d %H:%i:%s')
    </if>
    <if test="dto.humanCheckTimeEnd != null and dto.humanCheckTimeEnd != ''">
      and human_check_time &lt;= TO_DATE(#{dto.humanCheckTimeEnd}, '%Y-%m-%d %H:%i:%s')
    </if>
    <if test="dto.aiCheckTimeStart != null and dto.aiCheckTimeStart != ''">
      and ai_result_time &gt;= TO_DATE(#{dto.aiCheckTimeStart}, '%Y-%m-%d %H:%i:%s')
    </if>
    <if test="dto.aiCheckTimeEnd != null and dto.aiCheckTimeEnd != ''">
      and ai_result_time &lt;= TO_DATE(#{dto.aiCheckTimeEnd}, '%Y-%m-%d %H:%i:%s')
    </if>
  </sql>

  <select id="getHumanResultList" resultType="com.faw.work.ais.entity.vo.ai.HumanResultVo">
    select
    system_id sysTemId,
    biz_id bizId,
    batch_id batchId,
    ai_result_final aiResultFinal,
    human_check_result_final humanCheckResultFinal,
    task_type taskType,
    trace_id traceId,
    ai_result_single aiResultSingle,
    human_check_result_single humanCheckResultSingle,
    human_refuse_reason humanRefuseReason,
    ai_result_time aiResultTime,
    human_check_time humanCheckTime
    from
    human_result_new
    <where>
      <include refid="select_condition_sql" />
      <if test="dto.billRightRateFlag != null and dto.billRightRateFlag != '' and dto.billRightRateFlag == 1">
        AND human_check_result_final <![CDATA[<>]]> ai_result_final
      </if>
      <if test="dto.ruleCheckRateFlag != null and dto.ruleCheckRateFlag != '' and dto.ruleCheckRateFlag == 1">
        AND human_check_result_single <![CDATA[<>]]> ai_result_single
      </if>
    </where>
    order by create_time desc,batch_id asc
    </select>

  <select id="getHumanResultDividendCount" resultType="java.lang.Integer">
    SELECT
      COUNT( DISTINCT ${type} )
    FROM
      human_result_new
    <where>

      <include refid="select_condition_sql" />
      <if test="type != null and type == 'batch_id' ">
        and human_check_result_final = ai_result_final
      </if>
      <if test="type != null and type == 'trace_id' ">
        and human_check_result_single = ai_result_single
      </if>

    </where>
  </select>

  <select id="getHumanResultDivisorCount" resultType="java.lang.Integer">
    SELECT
    COUNT( DISTINCT ${type} )
    FROM
    human_result_new
    <where>
      <include refid="select_condition_sql" />
    </where>
  </select>

  <select id="getApproveCount" resultType="java.lang.Integer">
    SELECT
    COUNT( DISTINCT batch_id )
    FROM
    human_result_new
    <where>
      <include refid="select_condition_sql" />

      <if test="type != null and type == 'TP' ">
        and ai_result_final = 1
        and human_check_result_final = 1
      </if>
      <if test="type != null and type == 'TN' ">
        and ai_result_final = 1
        and human_check_result_final = 0
      </if>
      <if test="type != null and type == 'FP' ">
        and ai_result_final = 0
        and human_check_result_final = 1
      </if>
      <if test="type != null and type == 'FN' ">
        and ai_result_final = 0
        and human_check_result_final = 0
      </if>
    </where>

  </select>

  <select id="getAiResultList" resultType="com.faw.work.ais.model.AiTaskResultNew">
    select
      trace_id,
      ai_explain
    from
      ai_task_result_new
    where
      trace_id in
      <foreach collection="list" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
  </select>

  <select id="getTaskRuleName" resultType="com.faw.work.ais.entity.vo.ai.TaskRuleVO">
    select
    task_type,
    task_name
    from
    task_rule
    where
    task_type in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>