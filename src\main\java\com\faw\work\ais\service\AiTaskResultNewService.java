package com.faw.work.ais.service;

import java.util.Map;
import java.util.List;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.AiTaskResultNew;

/**
* AI任务结果记录表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 11:20:13
*/
public interface AiTaskResultNewService {

    /**
    * 新增或修改
    */
    public Result<Integer> insertOrUpdate(AiTaskResultNew aiTaskResultNew);

    /**
    * 新增
    */
    public Result<Integer> insert(AiTaskResultNew aiTaskResultNew);



    /**
    * 修改
    */
    public Result<Integer> update(AiTaskResultNew aiTaskResultNew);

    /**
    * 根据Id查询
    */
    public Result<AiTaskResultNew> getAiTaskResultNewById(Long id);

   /**
    * 分页全部查询
    */
    public Result<List<AiTaskResultNew>> getAiTaskResultNewList(AiTaskResultNew aiTaskResultNew);


}

