package com.faw.work.ais.aic.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大模型评测输出Excel数据DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "大模型评测输出Excel数据DTO")
public class ModelEvaluationOutputDTO {
    
    /**
     * 用户输入
     */
    @ExcelProperty("user_input")
    @Schema(description = "用户输入")
    private String userInput;

    /**
     * 地面应答
     */
    @ExcelProperty("ground_answer")
    @Schema(description = "人工标注结果")
    private String groundAnswer;
    
    /**
     * 模型评测结果
     */
    @ExcelProperty("test_result")
    @Schema(description = "模型评测测试结果")
    private String testResult;
}
