package com.faw.work.ais.aic.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文本嵌入响应实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingResponse {
    /**
     * 响应数据
     */
    private List<EmbeddingItem> data;
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 对象类型
     */
    private String object;
    
    /**
     * 使用情况
     */
    private Usage usage;
    
    /**
     * 请求ID
     */
    private String id;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmbeddingItem {
        /**
         * 文本向量
         */
        private float[] embedding;
        
        /**
         * 索引
         */
        private Integer index;
        
        /**
         * 对象类型
         */
        private String object;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Usage {
        /**
         * 提示词token数
         */
        private Integer prompt_tokens;
        
        /**
         * 总token数
         */
        private Integer total_tokens;
    }
} 