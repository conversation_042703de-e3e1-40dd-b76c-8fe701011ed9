<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.bupcore.TaskInfoMapper">

    <select id="getAiCoverBizNum" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT ttt.biz_unit_code)
        FROM
            t_task_instance ti
                LEFT JOIN t_task_template ttt ON ti.task_template_code = ttt.task_template_code
        WHERE
            ti.`status` = 1
          AND ti.team_code = 'CSP'
          AND ti.create_time <![CDATA[>=]]> '2023-06-12 00:00:00'
          AND ti.distribute_user_name = #{dealName}
    </select>

    <select id="getAiCoverRoleNum" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT tttv.role_code)
        FROM
            t_task_template tt
                LEFT JOIN t_task_template_version tttv ON tt.task_template_code = tttv.task_template_code
        <where>
            <if test = "bizUnitInfos != null and bizUnitInfos.size > 0 ">
                AND bu.unit_code IN
                <foreach collection="bizUnitInfos" item="bizType" open="(" separator="," close=")">
                    #{bizType.bizUnitCode}
                </foreach>
            </if>
        </where>
    </select>


    <select id="getFlowInfos" resultType="com.faw.work.ais.entity.vo.ai.FlowInfoVO">
        SELECT
            tfb.l3_process_code l3flowCode,
            tfb.l3_process_name l3flowName
        FROM
            t_task_instance ti
                LEFT JOIN t_task_template ttt ON ti.task_template_code = ttt.task_template_code
                LEFT JOIN t_biz_unit tbu ON ttt.biz_unit_code = tbu.biz_unit_code
                LEFT JOIN t_flow_business tfb ON ttt.biz_unit_code = tfb.business_unit_code
        WHERE
            ti.`status` = 1
          AND ti.team_code = 'CSP'
          AND ti.create_time <![CDATA[>=]]> '2023-06-12 00:00:00'
          AND ti.distribute_user_name = #{dealName}

        GROUP BY
            tfb.l3_process_code,
            tfb.l3_process_name
    </select>

    <select id="getBizUnitInfos" resultType="com.faw.work.ais.entity.vo.ai.BizUnitInfoVO">
        SELECT
            ti.biz_unit_code bizUnitCode,
            tbu.biz_unit_name bizUnitName
        FROM
            t_task_instance ti
                LEFT JOIN t_biz_unit tbu ON ttt.biz_unit_code = tbu.biz_unit_code
        WHERE
            ti.`status` = 1
          AND ti.team_code = 'CSP'
          AND ti.create_time <![CDATA[>=]]> '2023-06-12 00:00:00'
          AND ti.distribute_user_code = #{dealCode}
        GROUP BY
            ti.biz_unit_code,tbu.biz_unit_name
    </select>

    <select id="getL3FlowBizUnitInfos" resultType="com.faw.work.ais.entity.vo.ai.BizUnitInfoVO">
        SELECT
            ttt.biz_unit_code bizUnitCode,
            tbu.biz_unit_name bizUnitName
        FROM
            t_task_instance ti
            LEFT JOIN t_task_template ttt ON ti.task_template_code = ttt.task_template_code
            LEFT JOIN t_biz_unit tbu ON ttt.biz_unit_code = tbu.biz_unit_code
            LEFT JOIN t_flow_business tfb ON ttt.biz_unit_code = tfb.business_unit_code
        WHERE
              ti.`status` = 1
          AND ti.team_code = 'CSP'
          AND ti.create_time <![CDATA[>=]]> '2023-06-12 00:00:00'
          AND ti.distribute_user_name = #{dealName}
          AND tfb.l3_process_code = #{flowId}
        GROUP BY
            tbu.biz_unit_name, ttt.biz_unit_code
    </select>

    <select id="getFlowInfoList" resultType="com.faw.work.ais.model.AiScene">
        SELECT distinct
        tfb.l3_process_code l3ProcessCode,
        tfb.l3_process_name l3ProcessName,
        tfb.l4_process_code l4ProcessCode,
        tfb.l4_process_name l4ProcessName,
        tfb.l5_process_code l5ProcessCode,
        tfb.l5_process_name l5ProcessName
        FROM
        t_flow_business tfb
        WHERE tfb.team_code = 'CSP'
        AND tfb.publish = 1
    </select>

    <select id="getUnitList" resultType="com.faw.work.ais.entity.vo.ai.BizUnitInfoVO">
        SELECT distinct
        tfb.business_unit_code bizUnitCode,
        tfb.business_unit_name bizUnitName
        FROM
        t_flow_business tfb
        WHERE tfb.team_code = 'CSP'
        AND tfb.publish = 1
        <if test="vo.level == '3' or vo.level == 3 ">
            and tfb.l3_process_code = #{vo.l3flowCode}
        </if>
        <if test="vo.level == '4' or vo.level == 4 ">
            and tfb.l4_process_code = #{vo.l3flowCode}
        </if>
        <if test="vo.level == '5' or vo.level == 5 ">
            and tfb.l5_process_code = #{vo.l3flowCode}
        </if>
    </select>
</mapper>
