package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 相似内容搜索响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "相似内容搜索响应")
public class SimilarContentSearchResponse {

    @Schema(description = "向量ID")
    private String id;

    @Schema(description = "文档ID")
    private Long documentId;

    @Schema(description = "标签信息")
    private String label;

    @Schema(description = "相似度得分")
    private Float score;

    @Schema(description = "内容")
    private String documentContent;

    @Schema(description = "文档名称")
    private String documentName;


    @Schema(description = "文档URL")
    private String documentUrl;
}
