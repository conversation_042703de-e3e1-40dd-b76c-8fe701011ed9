package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文档一体化处理响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "文档一体化处理响应")
public class RagDocumentProcessAllResponse {

    @Schema(description = "文档ID")
    private Long documentId;

    @Schema(description = "文档名称")
    private String documentName;

    @Schema(description = "知识库ID")
    private Long ragKnowledgeId;

    @Schema(description = "知识库名称")
    private String knowledgeName;

    @Schema(description = "上传状态")
    private Boolean uploadSuccess;

    @Schema(description = "分片状态")
    private Boolean splitSuccess;

    @Schema(description = "绑定状态")
    private Boolean bindSuccess;

    @Schema(description = "向量化状态")
    private Boolean vectorSuccess;

    @Schema(description = "处理状态描述")
    private String statusMessage;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "分片数量")
    private Integer splitCount;
}
