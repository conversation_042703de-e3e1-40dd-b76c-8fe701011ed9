package com.faw.work.ais.common.util;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

/**
 * 上传文件到 COS 桶
 */
@Slf4j
@Component
public class UploadFileToCOS {

    private static final String regionName = "ap-beijing";
    private static final String bucketName = "ais-1300211780";
    private static final String secretId = "AKIDAC78zmXAbondGgCfuEWcpwBJKRWRRo9O";
    private static final String secretKey = "PAqZSFC4pSh5ayBIFJYvPvKl3FHBwhHu";
    private static final String fileKey = "/uat/ais/";

    /**
     * 通过图片url读取文件流，上传COS
     */
    public static String uploadImgToCOSbyURL(String imageUrl) {
        String cosImageUrl = "";

        try{
            // 从 Url 中获取图片信息
            URL url = new URL(imageUrl);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setInstanceFollowRedirects(true); // 允许重定向

            // 初始化 cosClient
            COSCredentials cred1 = new BasicCOSCredentials(secretId, secretKey);
            Region region1 = new Region(regionName);
            ClientConfig clientConfig1 = new ClientConfig(region1);
            COSClient cosClient = new COSClient(cred1, clientConfig1);

            int responseCode = httpConn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 文件流
                InputStream inputStream = httpConn.getInputStream();
                try {
                    // 上传至 COS
                    String filePath = fileKey + "/image/" + System.currentTimeMillis() + "-" + UUID.randomUUID() + ".png";
                    PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filePath, inputStream, new ObjectMetadata());
                    cosClient.putObject(putObjectRequest);

                    // 获取文件链接
                    GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, filePath, HttpMethodName.GET);
                    Date expirationDate = new Date(System.currentTimeMillis() + 12 * 60 * 60 * 1000);
                    req.setExpiration(expirationDate);
                    URL url1 = cosClient.generatePresignedUrl(req);
                    cosImageUrl = url1.toString();

                    log.info("Image uploaded successfully to Tencent COS. imageUrl: {}, CosImageUrl:{}", imageUrl, cosImageUrl);
                } finally {
                    // 确保输入流被关闭
                    inputStream.close();
                    cosClient.shutdown();
                    httpConn.disconnect();
                }
            } else {
                log.error("ImageUrl: {}, ：{}", imageUrl, responseCode);
            }
        } catch (Exception e){
            log.error("ImageUrl: {}, 上传文件异常：{}", imageUrl, e);
        }
        return cosImageUrl;
    }

}
