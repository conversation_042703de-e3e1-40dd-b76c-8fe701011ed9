package com.faw.work.ais.aic.common.enums;

import lombok.Getter;

/**
 * 文档向量化状态枚举
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Getter
public enum RagDocumentVectorEnum {
    /**
     * 未向量化
     */
    UN_VECTORIZED("00", "未向量化"),
    /**
     * 向量化中
     */
    VECTORIZING("01", "向量化中"),
    /**
     * 向量化完成
     */
    VECTORIZED("02", "向量化完成"),
    /**
     * 向量化失败
     */
    FAILED("10", "向量化失败");

    RagDocumentVectorEnum(String code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final String code;

    private final String msg;

}
