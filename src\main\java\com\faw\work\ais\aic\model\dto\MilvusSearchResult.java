package com.faw.work.ais.aic.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Milvus搜索结果实体类
 * 包含id、embedding、document_id和label字段
 * <AUTHOR>
 */
@Schema(description = "Milvus搜索结果实体类")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MilvusSearchResult {
    
    /**
     * 向量ID
     */
    @Schema(description = "向量ID")
    private String id;
    
    /**
     * 相似度分数
     */
    @Schema(description = "相似度分数")
    private Float score;

    /**
     * 向量数据
     */
    @Schema(description = "向量数据")
    private float[] embedding;

    /**
     * 文档ID
     */
    @Schema(description = "文档ID")
    private Long documentId;

    /**
     * 标签信息
     */
    @Schema(description = "标签信息")
    private String label;

    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;
}
