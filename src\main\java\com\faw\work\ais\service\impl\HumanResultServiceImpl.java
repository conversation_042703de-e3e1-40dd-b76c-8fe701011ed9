package com.faw.work.ais.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.request.OapiAtsRpaResumeMailCollectRequest;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.enums.ResEnum;
import com.faw.work.ais.entity.dto.ai.HumanResultDTO;
import com.faw.work.ais.mapper.ais.AiTaskResultMapper;
import com.faw.work.ais.mapper.ais.HumanResultMapper;
import com.faw.work.ais.model.HumanResult;
import com.faw.work.ais.service.HumanResultService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 人工审核结果
 */

@Service
@Log4j2
public class HumanResultServiceImpl extends ServiceImpl<HumanResultMapper, HumanResult> implements HumanResultService {

    @Autowired
    HumanResultMapper humanResultMapper;

    @Autowired
    AiTaskResultMapper aiTaskResultMapper;

    /**
     * 接收各个能力中心数据-人工审核结果数据
     * @param params
     * @return
     */
    @Override
    public Response obsiveHumanResultDataFromAbalityCenter(List<HumanResultDTO> params) {
        log.info("----------接收数据条数为-----------" + params.size());
        if(CollectionUtils.isEmpty(params)){
            return Response.fail(ResEnum.F_221);
        }
        if(params.size() > 1000){
            return Response.fail(ResEnum.F_222);
        }
        try{
            params.stream().forEach(x->{
                HumanResult humanResult = new HumanResult();
                BeanUtils.copyProperties(x,humanResult);
                Date now = new Date();
                humanResult.setCreateTime(now);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 定义日期格式，与你的字符串格式相匹配
                try {
                    humanResult.setHumanCheckTime(sdf.parse(x.getHumanCheckTime()));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                // 计算单据的AI审核结果并保存到结果表中
                String aiSingleResult = aiTaskResultMapper.getAiResultSingle(x.getBatchId(), x.getTraceId());
                String aiFinalResult = aiTaskResultMapper.getAiResultFinal(x.getBatchId());
                String aiResultTime = aiTaskResultMapper.getAiResultAndCreateTime(x.getBatchId(), x.getTraceId());
                humanResult.setAiResultSingle(aiSingleResult);
                humanResult.setAiResultFinal(aiFinalResult);
                try {
                    if(aiResultTime != null){
                        humanResult.setAiResultTime(sdf.parse(aiResultTime));
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                // 删除历史数据
                humanResultMapper.deleteByBatchIdAndTraceIdInt(x.getTraceId(),x.getBatchId());
                // 插入新数据
                humanResultMapper.insert(humanResult);
            });
        }catch (Exception ex){
            log.error("----接收能力中心数据异常-----" + ex.getCause() + ex.getMessage(), ex);
            return Response.fail(ex.getMessage() + ex);
        }
        return Response.success(ResEnum.SUCCESS_CODE);
    }
}
