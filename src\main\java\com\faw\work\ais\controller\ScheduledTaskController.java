package com.faw.work.ais.controller;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.RePushAiDTO;
import com.faw.work.ais.service.CommonService;
import com.faw.work.ais.service.NumberEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Schema(description = "定时任务")
@Slf4j
@RestController("ScheduledTaskController")
@RequestMapping("/scheduled")
public class ScheduledTaskController {

    @Autowired
    private NumberEmployeeService numberEmployeeService;

    @Autowired
    private CommonService commonService;

    @Operation(summary = "刷新AI覆盖规则、角色、业务单元数", description = "[author:10236535]")
    @PostMapping(value = "/refAiCoverInfo")
    public Response updateAiCoverInfo() {
        try {
            numberEmployeeService.updateAiCoverInfo();
            return Response.success();
        }catch (Exception e){
            log.error("---刷新AI覆盖规则、角色、业务单元数出错----", e + e.getMessage() + e.getCause());
            return Response.fail("刷新AI覆盖规则、角色、业务单元数出错!");
        }
    }

    @Operation(summary = "每小时刷新一次，定时监听过去1.5小时内没有回调的数据，重新送检AI", description = "[author:10236535]")
    @PostMapping(value = "/refAiTask")
    public Response refAiTask(@RequestBody @Valid RePushAiDTO rePushAiDTO) {
        try {
            commonService.rePushAiTask(rePushAiDTO);
            return Response.success();
        } catch (Exception e) {
            log.info("---补推数据出错----", e + e.getMessage() + e.getCause());
            return Response.fail("补推数据出错!");
        }
    }
}
