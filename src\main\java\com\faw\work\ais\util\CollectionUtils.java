package com.faw.work.ais.util;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 集合工具类，提供常用的集合操作。
 * 该类不能被实例化。
 */
public final class CollectionUtils {

    /**
     * 私有构造函数，防止类被实例化。
     */
    private CollectionUtils() {
        // 私有构造函数，阻止实例化
    }

    /**
     * 检查集合是否为 null 或空。
     *
     * @param collection 要检查的集合。
     * @return 如果集合为 null 或空，则返回 true；否则返回 false。
     */
    public static boolean isNullOrEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 检查 Map 是否为 null 或空。
     *
     * @param map 要检查的 Map。
     * @return 如果 Map 为 null 或空，则返回 true；否则返回 false。
     */
    public static boolean isNullOrEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 将一个集合转换为一个不可修改的 List。
     *
     * @param collection 要转换的集合。
     * @return 一个不可修改的 List。
     */
    public static <T> List<T> toUnmodifiableList(Collection<T> collection) {
        if (isNullOrEmpty(collection)) {
            return List.of();
        }
        return collection.stream().collect(Collectors.toUnmodifiableList());
    }
}
