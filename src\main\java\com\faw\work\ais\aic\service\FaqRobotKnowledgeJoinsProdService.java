package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsProdPO;

import java.util.List;

/**
 * FAQ机器人知识关联服务接口
 * <AUTHOR>
 */
public interface FaqRobotKnowledgeJoinsProdService extends IService<FaqRobotKnowledgeJoinsProdPO> {
    /**
     * 通过机器人id获取id
     *
     * @param robotId 机器人id
     * @return {@link List }<{@link String }>
     */
    List<String> getIdsByRobotId(String robotId);

    /**
     * 按机器人id删除
     *
     * @param robotId 机器人id
     */
    void removeByRobotId(String robotId);
} 