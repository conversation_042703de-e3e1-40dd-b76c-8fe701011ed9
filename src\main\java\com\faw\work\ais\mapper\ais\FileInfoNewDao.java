package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.FileInfoNew;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* 文件信息表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 13:51:57
*/
@InterceptorIgnore(tenantLine = "true")
@Mapper
public interface FileInfoNewDao {

    /**
    * 新增
    */
    public int insert(@Param("fileInfoNew") FileInfoNew fileInfoNew);

    /**
     * 新增
     */
    public int insertBatch(@Param("fileInfoNews") List<FileInfoNew> fileInfoNew);
    /**
    * 删除
    */
    public int delete(@Param("id") Long id);
    /**
     * 删除
     */
    public int deleteByIds(@Param("ids") List<Long> id);

    /**
    * 修改
    */
    public int update(@Param("fileInfoNew") FileInfoNew fileInfoNew);


    /**
    * 根据id查询 getFileInfoNewById
    */
    public FileInfoNew getFileInfoNewById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<FileInfoNew> getFileInfoNewList(@Param("fileInfoNew")FileInfoNew fileInfoNew);
    /**
     * 全部查询
     */
    public List<FileInfoNew> getFileInfoNewListByResultIds(@Param("ids") List<Long> id);


    /**
     * 根据traceId 删除文件
     * @param traceId 规则id
     */
    void deleteByTraceId(String traceId);

    /**
     * 根据traceId 查询文件
     * @param traceId 规则id
     * @return List<String> 文件地址
     */
    List<String> getFileInfosByTraceId(String traceId);

    /**
     * 根据traceId 查询文件
     * @param traceId 规则id
     * @return List<Integer> 主键id
     */
    List<Long> getFileIdByTraceId(String traceId);
}

