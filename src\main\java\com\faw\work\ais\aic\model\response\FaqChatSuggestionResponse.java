package com.faw.work.ais.aic.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "机器人聊天建议响应")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FaqChatSuggestionResponse {

    @Schema(description = "原始知识ID")
    private String originalKnowledgeId;

    @Schema(description = "原始知识的question")
    private String originalQuestion;

    @Schema(description = "匹配到的话术")
    private String matchedQuestion;

    @Schema(description = "类目名称")
    private String categoryName;

    @Schema(description = "得分")
    private Float score;
} 