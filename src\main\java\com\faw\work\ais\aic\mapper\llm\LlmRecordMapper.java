package com.faw.work.ais.aic.mapper.llm;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.LlmRecord;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 情绪分析表 Mapper 接口
 * <AUTHOR>
 */
@Mapper
public interface LlmRecordMapper extends BaseMapper<LlmRecord> {

    /**
     * 按聊天id选择
     *
     * @param requestId 聊天id
     * @param status    状态
     * @param bizType   我们类型
     * @return {@link List }<{@link LlmRecord }>
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<LlmRecord> selectByRequestId(String requestId, String status,String bizType);
}
