package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.QuantityStatistics;
import com.faw.work.ais.model.QuantityStatisticsDayValueRes;
import com.faw.work.ais.model.QuantityStatisticsTotalTp;
import com.faw.work.ais.model.QuantityStatisticsTotalValue;
import com.faw.work.ais.service.QuantityStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* 数量统计表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 14:12:35
*/
@Schema(description = "数量统计表")
@RestController
@Slf4j
public class QuantityStatisticsController {

    @Autowired
    private QuantityStatisticsService quantityStatisticsService;

    /**
    * 新增或修改数量统计表
    */
    @Operation(summary = "新增或修改数量统计表-job每天3.15 执行刷新前一天数据进 结果表", description = "[author:10236535]")
    @RequestMapping(value="/quantityStatistics/insertOrUpdateJob" , method = RequestMethod.POST)
    public Result<Integer> insertOrUpdate(@RequestBody QuantityStatistics quantityStatistics){
        return   quantityStatisticsService.insertOrUpdate(quantityStatistics);

    }

    /**
    * 新增数量统计表
    */
    @Operation(summary = "新增数量统计表-刷新歷史記錄", description = "[author:10236535]")
    @RequestMapping(value="/quantityStatistics/insertHis" , method = RequestMethod.POST)
    public Result<Integer> insert(@RequestBody QuantityStatistics quantityStatistics){
    return quantityStatisticsService.insert(quantityStatistics);

    }


    /**
    * 分页查询数量统计表
    */
    @Operation(summary = "分页查询数量统计表", description = "[author:10236535]")
    @RequestMapping(value="/quantityStatistics/getQuantityStatisticsList" , method = RequestMethod.POST)
    public Result<List<QuantityStatisticsDayValueRes>> getQuantityStatisticsList(@RequestBody QuantityStatistics quantityStatistics){
    return    quantityStatisticsService.getQuantityStatisticsList(quantityStatistics);

   }

    /**
     * 分页查询数量统计表
     */
    @Operation(summary = "智能审核任务数", description = "[author:10236535]")
    @RequestMapping(value="/quantityStatistics/getAiQuantityStatistics" , method = RequestMethod.POST)
    public Result<QuantityStatisticsTotalValue> getAiQuantityStatistics(@RequestBody QuantityStatistics quantityStatistics){
        return    quantityStatisticsService.getAiQuantityStatistics(quantityStatistics);

    }
    /**
     * 分页查询数量统计表
     */
    @Operation(summary = "人工审核准确数", description = "[author:10236535]")
    @RequestMapping(value="/quantityStatistics/getHumQuantityStatistics" , method = RequestMethod.POST)
    public Result<QuantityStatisticsTotalValue> getHumQuantityStatistics(@RequestBody QuantityStatistics quantityStatistics){
        return    quantityStatisticsService.getHumQuantityStatistics(quantityStatistics);

    }
    /**
     * 分页查询数量统计表
     */
    @Operation(summary = "人工审核tp,fp准确数", description = "[author:10236535]")
    @RequestMapping(value="/quantityStatistics/getHumTpStatistics" , method = RequestMethod.POST)
    public Result<QuantityStatisticsTotalTp> getHumTpStatistics(@RequestBody QuantityStatistics quantityStatistics){
        return    quantityStatisticsService.getHumTpStatistics(quantityStatistics);

    }


}

