package com.faw.work.ais.aic.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自动向量化保存DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoEmbeddingDTO {
    /**
     * ID
     */
    private String id;
    
    /**
     * 业务信息
     */
    private String bizInfo;
    
    /**
     * 内容，用于生成向量
     */
    private String content;

    
    /**
     * 创建一个完整的实例
     * 
     * @param id ID
     * @param content 内容
     * @param bizInfo 业务信息
     * @return AutoEmbeddingDTO实例
     */
    public static AutoEmbeddingDTO create(String id, String content, String bizInfo) {
        return AutoEmbeddingDTO.builder()
                .id(id)
                .content(content)
                .bizInfo(bizInfo)
                .build();
    }
} 