package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 知识库文档解绑请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库文档解绑请求")
public class RagKnowledgeDocumentUnbindRequest {

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long ragKnowledgeId;

    @NotEmpty(message = "文档ID列表不能为空")
    @Schema(description = "文档ID列表", required = true)
    private List<Long> documentIds;
}
