package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "机器人聊天请求")
public class FaqChatRequest {

    @NotBlank(message = "机器人ID不能为空")
    @Schema(description = "机器人ID")
    private String robotId;

    @NotBlank(message = "环境不能为空")
    @Schema(description = "环境（test或prod）", example = "test")
    private String env;

    @NotBlank(message = "问题不能为空")
    @Schema(description = "用户的问题")
    private String query;

    @NotBlank(message = "调试模式不能为空")
    @Schema(description = "是否开启调试模式（open或close）", example = "close")
    private String debugMode;

    @NotNull(message = "相似度阈值不能为空")
    @Schema(description = "似度阈值")
    private float similarityThreshold;
} 