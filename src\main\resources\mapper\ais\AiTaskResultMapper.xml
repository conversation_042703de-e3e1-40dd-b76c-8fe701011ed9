<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiTaskResultMapper">

    <resultMap id="BaseResultMap" type="com.faw.work.ais.model.AiTaskResult">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="fileRawList" column="file_raw_list" jdbcType="VARCHAR"/>
            <result property="fileId" column="file_id" jdbcType="BIGINT"/>
            <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
            <result property="taskResult" column="task_result" jdbcType="VARCHAR"/>
            <result property="contentType" column="content_type" jdbcType="INTEGER"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="systemId" column="system_id" jdbcType="VARCHAR"/>
            <result property="taskStatus" column="task_status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="bizId" column="biz_id" jdbcType="VARCHAR"/>
            <result property="rawMessage" column="raw_message" jdbcType="VARCHAR"/>
            <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
            <result property="success" column="success" jdbcType="VARCHAR"/>
            <result property="givenInfoJson" column="given_info_json" jdbcType="VARCHAR"/>
            <result property="traceId" column="trace_id" jdbcType="VARCHAR"/>
            <result property="batchId" column="batch_id" jdbcType="VARCHAR"/>
            <result property="aiResult" column="ai_result" jdbcType="VARCHAR"/>
            <result property="aiExplain" column="ai_explain" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,file_raw_list,file_id,
        task_type,task_result,content_type,
        version,system_id,task_status,
        create_time,update_time,biz_id,
        raw_message, success, givenInfoJson, trace_id,
        batch_id, ai_result, ai_explain
    </sql>

    <select id="getResult" resultType="com.faw.work.ais.model.AiTaskResult">
        SELECT
            t.id,
            t.file_raw_list fileRawList,
            t.file_id fileId,
            t.task_type taskType,
            t.task_result taskResult,
            t.content_type contentType,
            t.version version,
            t.system_id systemId,
            t.task_status taskStatus,
            t.create_time createTime,
            t.update_time updateTime,
            t.biz_id bizId,
            t.raw_message rawMessage,
            t.biz_type bizType,
            t.success,
            t.given_info_json givenInfoJson,
            t.trace_id traceId,
            t.ai_result aiResult,
            t.ai_explain aiExplain,
            t.batch_id batchId
        FROM ai_task_result t
        <where>
            <if test="param.fileId !=null and param.fileId != ''">
                AND t.file_id = #{param.fileId}
            </if>
            <if test="param.contentType !=null and param.contentType != ''">
                AND t.content_type = #{param.contentType}
            </if>
            <if test="param.version !=null and param.version != ''">
                AND t.version = #{param.version}
            </if>
            <if test="param.systemId !=null and param.systemId != ''">
                AND t.system_id = #{param.systemId}
            </if>
            <if test="param.bizId !=null and param.bizId != ''">
                AND t.biz_id = #{param.bizId}
            </if>
            <if test="param.batchId !=null and param.batchId != ''">
                AND t.batch_id = #{param.batchId}
            </if>
            <if test="param.traceId !=null and param.traceId != ''">
                AND t.trace_id = #{param.traceId}
            </if>
        </where>
    </select>

    <select id="selectAiTaskResultPage" resultType="com.faw.work.ais.entity.vo.ai.AiTaskResultVO">
        SELECT
        t.id ,
        t.file_raw_list fileRawList,
        t.file_id fileId,
        t.task_type taskType,
        t.task_result taskResult,
        t.content_type contentType,
        t.version,
        t.system_id systemId,
        t.task_status taskStatus,
        t.create_time createTime,
        t.biz_id bizId,
        t.trace_id traceId,
        t.batch_id batchId,
        t.ai_result,
        t.ai_explain
        FROM ai_task_result t
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        ORDER BY t.create_time desc
    </select>

    <select id="healthMonitorSql" resultType="java.lang.Integer">
        select count(1) from system_call_back_url
    </select>

    <select id="getFileIdsByTraceId" resultType="java.lang.String">
        SELECT
            t.file_id
        FROM ai_task_result t
        WHERE
            t.trace_id = #{traceId}
    </select>

    <select id="getTopData" resultType="com.faw.work.ais.entity.vo.ai.KanbanTopDataVO">
        SELECT
        count(tt.batchId) aiCheckBillNum,
        count(tt.batchId) hunmanCheckBillNum,
        (SUM(case when tt.traceIdNum = tt.aiPassNum AND tt.humanResultByBill = '1' then 1 else 0 end) +
        SUM(case when tt.traceIdNum <![CDATA[<>]]> tt.aiPassNum AND tt.humanResultByBill = '0' then 1 else 0 end)) aiPassHumanPassSumAiBackHumanBac,
        SUM(case when tt.traceIdNum = tt.aiPassNum then 1 else 0 end) as aiPassNum,
        IF (
        SUM( CASE WHEN tt.traceIdNum = tt.aiPassNum THEN 1 ELSE 0 END ) = 0,
        NULL,
        ROUND(
        SUM( CASE WHEN tt.traceIdNum = tt.aiPassNum AND tt.humanResultByBill = '1' THEN 1 ELSE 0 END ) / SUM( CASE WHEN tt.traceIdNum = tt.aiPassNum THEN 1 ELSE 0 END ) * 100,
        2
        )) aiPassRate,
        SUM(case when tt.traceIdNum = tt.aiPassNum AND tt.humanResultByBill = '1' then 1 else 0 end) aiPassHunmanCheckPassNum,
        SUM(case when tt.traceIdNum = tt.aiPassNum AND tt.humanResultByBill = '0' then 1 else 0 end) aiPassHunmanCheckRackBackNum,

        SUM(case when tt.traceIdNum = tt.aiPassNum then 0 else 1 end) aiBackNum,
        IF( SUM(case when tt.traceIdNum = tt.aiPassNum then 0 else 1 end) = 0,
        NULL,
        ROUND( SUM(case when tt.traceIdNum <![CDATA[<>]]> tt.aiPassNum AND tt.humanResultByBill = '0' then 1 else 0 end)/SUM(case when tt.traceIdNum = tt.aiPassNum then 0 else 1 end) *100,
        2
        )) aiBackRate,
        SUM(case when tt.traceIdNum <![CDATA[<>]]> tt.aiPassNum AND tt.humanResultByBill = '1' then 1 else 0 end) aiBackHumanPassNum,
        SUM(case when tt.traceIdNum <![CDATA[<>]]> tt.aiPassNum AND tt.humanResultByBill = '0' then 1 else 0 end) aiBackHumanBackNum,
        IF( (SUM(case when tt.traceIdNum = tt.aiPassNum AND tt.humanResultByBill = '1' then 1 else 0 end) +
        SUM(case when tt.traceIdNum <![CDATA[<>]]> tt.aiPassNum AND tt.humanResultByBill = '0' then 1 else 0 end) ) = 0,
        NULL,
        ROUND((SUM(case when tt.traceIdNum = tt.aiPassNum AND tt.humanResultByBill = '1' then 1 else 0 end) +
        SUM(case when tt.traceIdNum <![CDATA[<>]]> tt.aiPassNum AND tt.humanResultByBill = '0' then 1 else 0 end) )
        / count(tt.batchId) *100
        , 2)) billCheckRightRate,
        ''	ruleCheckRightRate
        FROM
        (
            SELECT
            atr.batch_id batchId,
            count(atr.trace_id) traceIdNum,
            SUM( CASE WHEN atr.ai_result = 'true' THEN 1 ELSE 0 END ) aiPassNum,
            SUM( CASE WHEN atr.ai_result = 'true' THEN 0 ELSE 1 END ) aiUnPassNum,
            hr.human_check_result_final humanResultByBill
            FROM
            ai_task_result atr
            INNER JOIN human_result_new hr ON atr.trace_id = hr.trace_id
            AND atr.system_id = hr.system_id AND hr.batch_id = atr.batch_id
            <where>
                <if test="param.systemId != null and param.systemId != ''">
                    AND atr.system_id = #{param.systemId}
                </if>
                <if test="param.aiCheckTimeStart != null and param.aiCheckTimeStart != '' and param.aiCheckTimeEnd != null and param.aiCheckTimeEnd != ''">
                    AND atr.create_time between #{param.aiCheckTimeStart} and #{param.aiCheckTimeEnd}
                </if>
                <if test="param.humanCheckTimeStart != null and param.humanCheckTimeStart != '' and param.humanCheckTimeEnd != null and param.humanCheckTimeEnd != ''">
                    AND hr.human_check_time between #{param.humanCheckTimeStart} and #{param.humanCheckTimeEnd}
                </if>
                <if test="param.batchId != null and param.batchId != ''">
                    AND hr.batch_id = #{param.batchId}
                </if>
                <if test="param.traceId != null and param.traceId != ''">
                    AND atr.trace_id = #{param.traceId}
                </if>
                <if test="param.taskType != null and param.taskType != ''">
                    AND atr.task_type = #{param.taskType}
                </if>
            </where>
            GROUP BY
        atr.batch_id,hr.human_check_result_final
        ) AS tt
    </select>

    <select id="getTopDataRule" resultType="com.faw.work.ais.entity.vo.ai.KanBanTopDataRuleVO">
        SELECT
            COUNT(atr.trace_id) aiCheckAllNum,
            SUM( CASE WHEN atr.ai_result = 'true' THEN 1 ELSE 0 END ) aiPassNum,
            SUM( CASE WHEN atr.ai_result = 'true' THEN 0 ELSE 1 END ) aiUnpassNum,
            SUM( CASE WHEN atr.ai_result = 'true' AND hr.human_check_result_single = '1' THEN 	1 ELSE 0 END ) aiPassHumanPassNum,
            SUM( CASE WHEN atr.ai_result = 'true' AND hr.human_check_result_single = '0' THEN 	1 ELSE 0 END ) aiPassHumanUnpassNum,
            SUM( CASE WHEN atr.ai_result = 'false' AND hr.human_check_result_single = '0' THEN 	1 ELSE 0 END ) aiUnpassHumanUnpassNum,
            SUM( CASE WHEN atr.ai_result = 'false' AND hr.human_check_result_single = '1' THEN 	1 ELSE 0 END ) aiUnpassHumanUnpassNum,
            IF( COUNT(atr.trace_id) = 0,
                NULL,
                ROUND( (SUM( CASE WHEN atr.ai_result = 'true' AND hr.human_check_result_single = '1' THEN 	1 ELSE 0 END )
                    + SUM( CASE WHEN atr.ai_result = 'false' AND hr.human_check_result_single = '0' THEN 	1 ELSE 0 END ))
                           / COUNT(atr.trace_id) * 100
                    , 2)) ruleRate
        FROM
            ai_task_result atr
                INNER JOIN human_result_new hr ON atr.trace_id = hr.trace_id
                AND atr.system_id = hr.system_id AND hr.batch_id = atr.batch_id
        <where>
            <if test="param.systemId != null and param.systemId != ''">
                AND atr.system_id = #{param.systemId}
            </if>
            <if test="param.aiCheckTimeStart != null and param.aiCheckTimeStart != '' and param.aiCheckTimeEnd != null and param.aiCheckTimeEnd != ''">
                AND atr.create_time between #{param.aiCheckTimeStart} and #{param.aiCheckTimeEnd}
            </if>
            <if test="param.humanCheckTimeStart != null and param.humanCheckTimeStart != '' and param.humanCheckTimeEnd != null and param.humanCheckTimeEnd != ''">
                AND hr.human_check_time between #{param.humanCheckTimeStart} and #{param.humanCheckTimeEnd}
            </if>
            <if test="param.batchId != null and param.batchId != ''">
                AND hr.batch_id = #{param.batchId}
            </if>
            <if test="param.traceId != null and param.traceId != ''">
                AND hr.trace_id = #{param.traceId}
            </if>
            <if test="param.taskType != null and param.taskType != ''">
                AND hr.task_type = #{param.taskType}
            </if>
        </where>
    </select>

    <select id="getAiResultSingle" resultType="java.lang.String">
        SELECT
            t.ai_result AS aiResult
        FROM ai_task_result_new t
        WHERE t.batch_id = #{batchId} AND t.trace_id = #{traceId}
    </select>

    <select id="getAiResultAndCreateTime" resultType="java.lang.String">
        SELECT
            t.create_time
        FROM ai_task_result_new t
        WHERE t.batch_id = #{batchId} AND t.trace_id = #{traceId}
    </select>
    <select id="getAiResultFinal" resultType="java.lang.String">
        SELECT
            CASE
                WHEN
                    tt.aiResult = tt.traceCount THEN
                    1 ELSE 0
                END AS finalResult
        FROM
            (
                SELECT
                    SUM( t.ai_result ) AS aiResult,
                    COUNT( t.trace_id ) AS traceCount
                FROM
                    ai_task_result_new t
                WHERE
                    t.batch_id = #{batchId}
            ) tt
    </select>

    <select id="getBillList" resultType="com.faw.work.ais.entity.vo.ai.KanBanBillListVO">
        SELECT
            hr.system_id sysTemId,
            sys.system_name sysTemName,
            hr.biz_id bizId,
            hr.batch_id batchId,
            hr.human_check_result_final humanCheckStatus,
            hr.ai_result_final aiCheckStatus
        FROM
            human_result_new hr
            LEFT JOIN system_call_back_url sys ON hr.system_id = sys.system_id
            INNER JOIN ai_task_result atr ON hr.system_id = atr.system_id AND hr.batch_id = atr.batch_id AND hr.trace_id = atr.trace_id
        <where>
            <if test="param.systemId != null and param.systemId != ''">
                AND hr.system_id = #{param.systemId}
            </if>
            <if test="param.aiCheckTimeStart != null and param.aiCheckTimeStart != '' and param.aiCheckTimeEnd != null and param.aiCheckTimeEnd != ''">
                AND atr.create_time between #{param.aiCheckTimeStart} and #{param.aiCheckTimeEnd}
            </if>
            <if test="param.humanCheckTimeStart != null and param.humanCheckTimeStart != '' and param.humanCheckTimeEnd != null and param.humanCheckTimeEnd != ''">
                AND hr.human_check_time between #{param.humanCheckTimeStart} and #{param.humanCheckTimeEnd}
            </if>
            <if test="param.billRightRateFlag != null and param.billRightRateFlag != '' and param.billRightRateFlag == 1">
                AND hr.human_check_result_final <![CDATA[<>]]> hr.ai_result_final
            </if>
            <if test="param.ruleCheckRateFlag != null and param.ruleCheckRateFlag != '' and param.ruleCheckRateFlag == 1">
                AND hr.human_check_result_single <![CDATA[<>]]> hr.ai_result_single
            </if>
            <if test="param.batchId != null and param.batchId != ''">
                AND hr.batch_id = #{param.batchId}
            </if>
            <if test="param.traceId != null and param.traceId != ''">
                AND hr.trace_id = #{param.traceId}
            </if>
            <if test="param.taskType != null and param.taskType != ''">
                AND hr.task_type = #{param.taskType}
            </if>
        </where>
        GROUP BY
            hr.system_id,
            sys.system_name,
            hr.biz_id,
            hr.batch_id,
            hr.human_check_result_final,
            hr.ai_result_final
    </select>
    <select id="getRuleList" resultType="com.faw.work.ais.entity.vo.ai.KanBanRuleListVO">
        SELECT
            atr.trace_id traceId,
            atr.task_type ruleId,
            atr.ai_result ruleAiCheckStatus,
            tr.task_name ruleName,
            hr.human_check_result_single ruleHumanCheckStatus,
            atr.ai_explain aiCheckReason,
            hr.human_refuse_reason humanCheckReason,
            atr.create_time aiCheckTime,
            hr.human_check_time humanCheckTime
        FROM ai_task_result atr
                 INNER JOIN human_result_new hr ON hr.system_id = atr.system_id
            AND hr.batch_id = atr.batch_id AND hr.trace_id = atr.trace_id
                 LEFT JOIN task_rule tr ON atr.task_type = tr.task_type
        <where>
            AND atr.batch_id = #{batchId}
            <if test="traceId != null and traceId != ''">
                AND hr.trace_id = #{traceId}
            </if>
            <if test="taskType != null and taskType != ''">
                AND hr.task_type = #{taskType}
            </if>
        </where>
        GROUP BY traceId, ruleId,ruleAiCheckStatus, ruleName, ruleHumanCheckStatus, aiCheckReason, humanCheckReason, aiCheckTime, humanCheckTime
    </select>

    <select id="getAiRate" resultType="java.lang.String">
        SELECT
            IF( COUNT(hr.ai_result_final) = 0,
                NULL,
                ROUND((SUM(CASE WHEN hr.ai_result_final = hr.human_check_result_final THEN 1 ELSE 0 END) / COUNT(hr.ai_result_final)) * 100
                , 2)
              )
             AS rightRate
        FROM human_result_new hr
        <where>
            <if test="beginDate != '' and beginDate != null and endDate != '' and endDate != null">
                AND hr.human_check_time between #{beginDate} and #{endDate}
            </if>
            <if test="systemId != '' and systemId != null">
                AND hr.system_id = #{systemId}
            </if>
        </where>
    </select>

    <update id="updateResultStatusByTraceId">
        UPDATE ai_task_result t set t.task_status = 3
        WHERE t.trace_id = #{traceId}
    </update>
</mapper>
