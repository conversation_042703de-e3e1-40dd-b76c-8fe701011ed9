package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper;
import com.faw.work.ais.aic.model.domain.FaqHitLogPO;
import com.faw.work.ais.aic.model.response.FaqHitLogCleanResponse;
import com.faw.work.ais.aic.service.FaqHitLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * FAQ命中日志Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqHitLogServiceImpl implements FaqHitLogService {

    @Autowired
    private FaqHitLogMapper faqHitLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FaqHitLogCleanResponse cleanOldHitLogs() {
        log.info("开始清理一个月前的FAQ命中日志数据");

        LocalDateTime cleanTime = LocalDateTime.now();
        int cleanCount = faqHitLogMapper.cleanOldHitLogs();

        log.info("FAQ命中日志清理完成，共清理{}条记录", cleanCount);

        return FaqHitLogCleanResponse.builder()
                .cleanCount(cleanCount)
                .cleanTime(cleanTime)
                .description("成功清理一个月前的FAQ命中日志数据")
                .build();
    }

    @Override
    public List<FaqHitLogPO> findHitLogsByCondition(String robotId, String dataSource,
                                                   LocalDateTime startTime, LocalDateTime endTime) {
        if (StrUtil.isBlank(robotId)) {
            throw new IllegalArgumentException("机器人ID不能为空");
        }
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTimeStr = startTime.format(formatter);
        String endTimeStr = endTime.format(formatter);

        List<FaqHitLogPO> hitLogs = faqHitLogMapper.findByCondition(robotId, dataSource, startTimeStr, endTimeStr);
        log.info("查询FAQ命中日志数据，机器人ID: {}, 数据来源: {}, 时间范围: {} ~ {}, 查询数量: {}",
                robotId, dataSource, startTimeStr, endTimeStr, hitLogs.size());

        return hitLogs;
    }
}
