package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数字员工看板返回值")
@Data
public class NumberEmployeeVO {

    @Schema(description = "系统id的代码")
    private String systemId;

    @Schema(description = "系统id的名称")
    private String systemName;

    @Schema(description = "AI覆盖角色数")
    private String aiCoverRoleNum;

    @Schema(description = "AI覆盖业务单元数")
    private String aiCoverBizNum;

    @Schema(description = "AI覆盖规则数")
    private String aiCoverRuleNum;

    @Schema(description = "AI覆盖验收场景数")
    private String aiCoverSceneNum;

    @Schema(description = "AI触发任务数")
    private String aiTriggerTaskNum;

    @Schema(description = "AI触发任务数-通过率")
    private String aiTriggerTaskRate;

    @Schema(description = "AI触发任务数-通过数量")
    private String aiTriggerTaskPassNum;

    @Schema(description = "AI触发规则")
    private String aiTriggerRuleNum;

    @Schema(description = "AI触发规则通过数量")
    private String aiTriggerRulePassNum;

    @Schema(description = "AI触发规则-通过率")
    private String aiTriggerRulePassRate;

    @Schema(description = "抽检任务数(单据)")
    private String sampleTaskNum;

    @Schema(description = "抽检准确率(单据)")
    private String sampleTaskRate;

    @Schema(description = "抽检任务数(规则)")
    private String sampleTaskRuleNum;

    @Schema(description = "抽检准确率(规则)")
    private String sampleTaskRuleRate;

    @Schema(description = "抽检结果和人工结果一致数量-不展示")
    private String sampleTaskPassNum;

    @Schema(description = "AI节省工时")
    private String aiSaveTime;

    @Schema(description = "AI节省费用")
    private String aiSaveCost;

    @Schema(description = "折线图")
    List<RateTrendChartVO> trendLine;

    @Schema(description = "人工抽检准确率饼图（单据）")
    private HumanSampleRightRateVO humanSampleRightRate;

    @Schema(description = "人工抽检准确率饼图（规则）")
    private HumanSampleRightRateRuleVO humanSampleRightRateRule;

    @Schema(description = "折线图-ai节省工时")
    List<RateTrendChartVO> trendLineSaveTime;

    @Schema(description = "折线图-ai节省费用")
    List<RateTrendChartVO> trendLineSaveCost;

}
