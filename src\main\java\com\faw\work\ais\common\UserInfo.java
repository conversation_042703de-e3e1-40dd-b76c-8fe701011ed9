package com.faw.work.ais.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class UserInfo {


    /**
     * iwork用户id/登录账号
     */
    private String userName;

    /**
     * iwork用户真实姓名
     */
    private String realName;

    private Long created;
    @JsonProperty("idmid")
    private String idmId;

    public UserInfo(String userName, String idmId) {
        this.userName = userName;
        this.idmId = idmId;
    }

    @JsonProperty("upkid")
    private String upkId;


}
