package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.enums.EffectiveTypeEnum;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.common.enums.EnvEnum;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.listener.FaqKnowledgeAliExcelListener;
import com.faw.work.ais.aic.listener.FaqKnowledgeExcelListener;
import com.faw.work.ais.aic.mapper.faq.*;
import com.faw.work.ais.aic.model.domain.*;
import com.faw.work.ais.aic.model.dto.*;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.*;
import com.faw.work.ais.aic.model.response.FaqChatResponse.FaqChatResponseBuilder;
import com.faw.work.ais.aic.service.*;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.faw.work.ais.aic.config.MilvusPoolConfig.FAQ_TENANT_CLIENT_KEY;

/**
 * FAQ知识Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqKnowledgeServiceImpl extends ServiceImpl<FaqKnowledgeMapper, FaqKnowledgePO> implements FaqKnowledgeService {

    public static final String XLSX = ".xlsx";
    public static final String XLS = ".xls";
    public static final String OPEN = "open";
    public static final String ORIGINAL = "original";
    public static final String ORIGIN = "origin";
    public static final String SIMILAR = "similar";
    @Autowired
    FaqRobotService faqRobotService;
    @Autowired
    private FaqRobotKnowledgeJoinsService faqRobotKnowledgeJoinsService;
    @Autowired
    private FaqRobotKnowledgeJoinsMapper faqRobotKnowledgeJoinsMapper;

    @Autowired
    private AsyncService asyncService;
    @Autowired
    private FaqHitLogMapper faqHitLogMapper;
    @Autowired
    private EmbeddingService embeddingService;
    @Autowired
    private MilvusService milvusService;
    @Autowired
    private FaqKnowledgeMapper faqKnowledgeMapper;

    @Autowired
    private RagCategoryService ragCategoryService;
    @Autowired
    private FaqCategoryService faqCategoryService;

    @Lazy
    @Autowired
    private FaqSimilarKnowledgeService faqSimilarKnowledgeService;

    @Lazy
    @Autowired
    private FaqKnowledgeService faqKnowledgeService;

    @Autowired
    private FaqSimilarKnowledgeMapper faqSimilarKnowledgeMapper;

    @Autowired
    private FaqRobotKnowledgeJoinsProdMapper faqRobotKnowledgeJoinsProdMapper;

    @Autowired
    private FaqKnowledgeProdService faqKnowledgeProdService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createKnowledge(FaqKnowledgeCreateRequest request) {
        String categoryId = request.getCategoryId();

        // 校验类目是否是最后一级
        LambdaQueryWrapper<FaqCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqCategoryPO::getParentId, categoryId);
        long childCount = faqCategoryService.count(queryWrapper);
        if (childCount > 0) {
            throw new BizException("只能在最后一级类目下创建知识");
        }

        // 设置创建和更新信息
        LocalDateTime now = LocalDateTime.now();
        String realName = UserThreadLocalUtil.getRealName();

        // 1. 创建并保存知识库记录
        FaqKnowledgePO knowledge = new FaqKnowledgePO();
        knowledge.setQuestion(request.getQuestion());
        knowledge.setAnswer(request.getAnswer());
        knowledge.setCategoryId(categoryId);
        knowledge.setEffectiveType(request.getEffectiveType());
        knowledge.setEffectiveStartTime(request.getEffectiveStartTime());
        knowledge.setEffectiveEndTime(request.getEffectiveEndTime());
        knowledge.setAnswerType(request.getAnswerType());
        knowledge.setHitCount(0L);
        knowledge.setPublishStatus("00");
        knowledge.setCreatedAt(now);
        knowledge.setUpdatedAt(now);
        knowledge.setCreatedBy(realName);
        knowledge.setUpdatedBy(realName);
        save(knowledge);
        String knowledgeId = knowledge.getId();

        // 2. 保存相似问列表
        List<FaqSimilarKnowledgePO> similarList = new ArrayList<>();

        List<String> similarQuestionList = request.getSimilarQuestionList();
        if (!CollectionUtils.isEmpty(similarQuestionList)) {
            for (String similarQuestion : similarQuestionList) {
                if (StringUtils.isBlank(similarQuestion)) {
                    throw new BizException("相似问不能为空");
                }
                FaqSimilarKnowledgePO similarKnowledge = new FaqSimilarKnowledgePO();
                similarKnowledge.setQuestion(request.getQuestion());
                similarKnowledge.setAnswer(request.getAnswer());
                similarKnowledge.setKnowledgeId(knowledgeId);
                similarKnowledge.setSimilarQuestion(similarQuestion);
                similarKnowledge.setCategoryId(categoryId);
                similarKnowledge.setCreatedAt(now);
                similarKnowledge.setUpdatedAt(now);
                similarKnowledge.setCreatedBy(realName);
                similarKnowledge.setUpdatedBy(realName);
                similarList.add(similarKnowledge);
            }
            faqSimilarKnowledgeService.saveBatch(similarList);
        }

        // 3. 查找绑定了该类目的机器人
        List<String> robotIds = faqRobotKnowledgeJoinsMapper.selectRobotIdsByCategoryId(categoryId);

        faqRobotService.syncTestRobotKnowledge(robotIds);

        return knowledgeId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateKnowledge(FaqKnowledgeUpdateRequest request) {
        // 0. 校验请求参数
        if (request == null || request.getId() == null) {
            throw new BizException("更新请求不能为空");
        }

        String knowledgeId = request.getId();
        FaqKnowledgePO oldKnowledge = getById(knowledgeId);
        if (oldKnowledge == null) {
            throw new BizException("知识不存在");
        }

        LocalDateTime now = LocalDateTime.now();
        String realName = UserThreadLocalUtil.getRealName();
        String newCategoryId = request.getCategoryId();
        // 记录旧类目ID（关键修改）
        String oldCategoryId = oldKnowledge.getCategoryId();

        // 1. 更新原始知识
        oldKnowledge.setQuestion(request.getQuestion());
        oldKnowledge.setAnswer(request.getAnswer());
        oldKnowledge.setPublishStatus(EffectiveTypeEnum.PERMANENT.getCode());
        oldKnowledge.setCategoryId(newCategoryId);
        oldKnowledge.setAnswerType(request.getAnswerType());
        oldKnowledge.setEffectiveType(request.getEffectiveType());
        if (EffectiveTypeEnum.TEMPORARY.getCode().equals(request.getEffectiveType())) {
            oldKnowledge.setEffectiveStartTime(request.getEffectiveStartTime());
            oldKnowledge.setEffectiveEndTime(request.getEffectiveEndTime());
        }
        oldKnowledge.setUpdatedBy(realName);
        oldKnowledge.setUpdatedAt(now);
        this.updateById(oldKnowledge);

        // 2. 处理相似问（增量更新和删除，没有进行删除在插入是考虑到后续对milvus数据进行处理，不然id会变）
        List<FaqSimilarKnowledgePO> oldSimilarList = faqSimilarKnowledgeMapper.selectByOriginalId(knowledgeId);
        Map<String, FaqSimilarKnowledgePO> oldSimilarMap = oldSimilarList.stream()
                .collect(Collectors.toMap(FaqSimilarKnowledgePO::getId, Function.identity()));

        // 收集需要保留/更新的ID（用户传入的ID）
        Set<String> inputIds = request.getSimilarQuestionList().stream()
                .map(FaqSimilarKnowledgeUpdateRequest::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 需要删除的ID：数据库中存在但用户未传入的ID
        List<String> deleteIds = oldSimilarList.stream()
                .map(FaqSimilarKnowledgePO::getId)
                .filter(id -> !inputIds.contains(id))
                .collect(Collectors.toList());

        // 构建保存/更新的数据列表
        List<FaqSimilarKnowledgePO> saveOrUpdateList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(request.getSimilarQuestionList())) {
            for (FaqSimilarKnowledgeUpdateRequest similarQuestion : request.getSimilarQuestionList()) {
                if (StringUtils.isNotBlank(similarQuestion.getSimilarQuestion())) {
                    FaqSimilarKnowledgePO po;
                    if (similarQuestion.getId() != null && oldSimilarMap.containsKey(similarQuestion.getId())) {
                        // 更新已有记录（复用数据库对象）
                        po = oldSimilarMap.get(similarQuestion.getId());
                        po.setQuestion(request.getQuestion());
                        po.setAnswer(request.getAnswer());
                        po.setCategoryId(newCategoryId);
                        po.setSimilarQuestion(similarQuestion.getSimilarQuestion());
                        po.setUpdatedAt(now);
                        po.setUpdatedBy(realName);
                    } else {
                        // 新增记录
                        po = new FaqSimilarKnowledgePO();
                        po.setQuestion(request.getQuestion());
                        po.setAnswer(request.getAnswer());
                        po.setKnowledgeId(knowledgeId);
                        po.setSimilarQuestion(similarQuestion.getSimilarQuestion());
                        po.setCategoryId(newCategoryId);
                        po.setCreatedAt(now);
                        po.setUpdatedAt(now);
                        po.setCreatedBy(realName);
                        po.setUpdatedBy(realName);
                    }
                    saveOrUpdateList.add(po);
                }
            }
        }

        // 批量保存/更新
        if (!saveOrUpdateList.isEmpty()) {
            faqSimilarKnowledgeService.saveOrUpdateBatch(saveOrUpdateList);
        }

        // 删除多余记录
        if (!deleteIds.isEmpty()) {
            faqSimilarKnowledgeService.removeBatchByIds(deleteIds);
        }

        // 3. 重新同步机器人知识（知识类目发生变更可能导致机器人知识同步，这里为了寻找所有潜在变更知识导致变化的机器人）
        Set<String> robotIdsToSync = new HashSet<>();

        // 同步旧类目的机器人（知识移出）
        if (!Objects.equals(oldCategoryId, newCategoryId)) {
            List<String> oldRobotIds = faqRobotKnowledgeJoinsMapper.selectRobotIdsByCategoryId(oldCategoryId);
            robotIdsToSync.addAll(oldRobotIds);
        }

        // 同步新类目的机器人（知识移入）
        List<String> newRobotIds = faqRobotKnowledgeJoinsMapper.selectRobotIdsByCategoryId(newCategoryId);
        robotIdsToSync.addAll(newRobotIds);

        // 同步当前知识的机器人（内容变更）
        List<String> currentRobotIds = faqRobotKnowledgeJoinsMapper.selectDistinctRobotId(knowledgeId);
        robotIdsToSync.addAll(currentRobotIds);

        if (!robotIdsToSync.isEmpty()) {
            faqRobotService.syncTestRobotKnowledge(new ArrayList<>(robotIdsToSync));
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteKnowledge(String knowledgeId) {
        List<FaqSimilarKnowledgePO> oldSimilarList = faqSimilarKnowledgeMapper.selectByOriginalId(knowledgeId);
        List<String> oldSimilarIds = oldSimilarList.stream().map(FaqSimilarKnowledgePO::getId).toList();
        List<String> joinKnowledgeIds = new ArrayList<>(oldSimilarIds);
        joinKnowledgeIds.add(knowledgeId);
        List<FaqRobotKnowledgeJoinsPO> joins = faqRobotKnowledgeJoinsMapper.selectByKnowledgeIds(joinKnowledgeIds);
        List<String> oldJoinIds = joins.stream().map(FaqRobotKnowledgeJoinsPO::getId).toList();
        // 1.删除中间表数据，原始知识和相似问题
        faqRobotKnowledgeJoinsService.removeBatchByIds(oldJoinIds);
        faqSimilarKnowledgeService.removeBatchByIds(oldSimilarIds);
        faqKnowledgeService.removeById(knowledgeId);
        // 2.删除中间表ids对应的milvus数据
        milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, oldJoinIds, null);
        //    TODO：删除正式环境数据
    }

    @Override
    public FaqKnowledgePO getKnowledgeDetail(String id, String env) {
        FaqKnowledgePO result;
        if (EnvEnum.TEST.getCode().equals(env)) {
            result = getById(id);
        } else if (EnvEnum.PROD.getCode().equals(env)) {
            // TODO: 正式环境数据
            result = faqKnowledgeMapper.selectProdKnowledgeById(id);
        } else {
            throw new BizException("环境类型不正确");
        }
        return result;
    }

    @Override
    public IPage<FaqKnowledgePO> pageKnowledge(FaqKnowledgeQueryRequest request) {
        // 使用 PageHelper 进行分页
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        String env = request.getEnv();
        List<FaqKnowledgePO> list;

        if (EnvEnum.TEST.getCode().equals(env)) {
            list = baseMapper.selectPageWithKeywordByPageHelper(request.getText(), request.getType(), request.getCategoryIds(), request.getRobotId());
        } else if (EnvEnum.PROD.getCode().equals(env)) {
            list = baseMapper.selectProdPageWithKeywordByPageHelper(request.getText(), request.getType(), request.getCategoryIds(), request.getRobotId());
        } else {
            throw new BizException("环境类型不正确");
        }

        // 使用 PageInfo 包装分页结果
        PageInfo<FaqKnowledgePO> pageInfo = new PageInfo<>(list);

        // 转换为 IPage 格式
        Page<FaqKnowledgePO> result = new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal());
        result.setRecords(pageInfo.getList());

        return result;
    }


    @Override
    public List<FaqKnowledgePO> searchByQuestion(String question, int topK) {
        if (question == null || question.trim().isEmpty()) {
            throw new BizException("查询问题不能为空");
        }

        log.info("开始搜索相似问题: {}", question);

        try {
            // 1. 获取问题的向量表示
            float[] embedding = embeddingService.getEmbedding(question);

            // 2. 在Milvus中搜索相似向量（默认返回最相似的1个结果）
            List<VectorSearchResult> searchResults = milvusService.searchByEmbedding(
                    MilvusPoolConfig.FAQ_COLLECTION_NAME,
                    embedding,
                    topK,
                    0.6f,
                    null
            );


            List<FaqKnowledgePO> knowledgeList = new ArrayList<>();
            // 3. 从数据库中获取对应的知识
            for (VectorSearchResult result : searchResults) {
                FaqKnowledgePO knowledge = this.getById(result.getId());
                if (knowledge == null) {
                    log.warn("向量库中找到匹配但数据库中无对应记录: id={}", result.getId());
                    return null;
                }

                // 4. 设置相似度分数并增加命中次数
                knowledge.setScore(result.getScore());

                faqKnowledgeMapper.incrementHitCount(knowledge.getId());

                knowledgeList.add(knowledge);
                log.info("找到匹配知识: id={}, 问题={}, 相似度={}",
                        knowledge.getId(), knowledge.getQuestion(), result.getScore());
            }


            return knowledgeList;
        } catch (Exception e) {
            log.error("搜索相似问题失败: {}", question, e);
            return Collections.emptyList();
        }
    }

    @Override
    public ExcelFaqImportResultResponse importExcel(MultipartFile file) {

        // 校验文件
        if (file == null || file.isEmpty()) {
            throw new BizException("上传文件不能为空");
        }

        // 校验文件类型
        String originalFilename = file.getOriginalFilename();
        if (!originalFilename.endsWith(XLSX) && !originalFilename.endsWith(XLS)) {
            throw new BizException("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }

        try {
            // 创建Excel监听器
            FaqKnowledgeExcelListener listener = new FaqKnowledgeExcelListener(this, ragCategoryService);

            // 读取Excel
            ExcelReader excelReader = EasyExcel.read(file.getInputStream(), FaqKnowledgeExcelDataDTO.class, listener).build();
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
            excelReader.finish();

            // 构建导入结果
            ExcelFaqImportResultResponse result = ExcelFaqImportResultResponse.builder()
                    .total(listener.getSuccessCount() + listener.getFailCount())
                    .successCount(listener.getSuccessCount())
                    .failCount(listener.getFailCount())
                    .errorMessages(listener.getErrorMessages())
                    .build();

            log.info("Excel批量导入FAQ知识完成，成功导入{}条，失败{}条", result.getSuccessCount(), result.getFailCount());
            return result;
        } catch (IOException e) {
            log.error("Excel批量导入FAQ知识失败", e);
            throw new BizException("Excel导入失败：" + e.getMessage());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSaveKnowledgeWithVector(List<FaqKnowledgeRequest> requestList) {
        if (requestList == null || requestList.isEmpty()) {
            return 0;
        }

        log.info("开始批量保存知识，数量: {}", requestList.size());

        try {
            // 1. 批量保存知识到数据库
            List<FaqKnowledgePO> knowledgeList = new ArrayList<>(requestList.size());
            List<String> questionList = new ArrayList<>(requestList.size());

            // 准备知识数据和问题列表
            LocalDateTime now = LocalDateTime.now();
            String realName = UserThreadLocalUtil.getRealName();

            for (FaqKnowledgeRequest request : requestList) {
                if (StringUtils.isBlank(request.getCategoryName())) {
                    throw new BizException("类目不能为空");
                }

                FaqKnowledgePO knowledge = new FaqKnowledgePO();
                BeanUtils.copyProperties(request, knowledge);
                knowledge.setHitCount(0L);
                knowledge.setCreatedAt(now);
                knowledge.setUpdatedAt(now);
                knowledge.setCreatedBy(realName);
                knowledge.setUpdatedBy(realName);

                knowledgeList.add(knowledge);
                questionList.add(request.getQuestion());
            }

            // 批量保存到数据库
            this.saveBatch(knowledgeList);

            // 2. 批量获取向量表示
            List<EmbeddingPropertyDTO> embeddingProperties = embeddingService.getEmbeddingList(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, questionList);

            // 3. 准备批量存储到Milvus的数据
            List<MilvusRow> milvusRows = new ArrayList<>(requestList.size());

            for (int i = 0; i < knowledgeList.size(); i++) {
                FaqKnowledgePO knowledge = knowledgeList.get(i);
                FaqKnowledgeRequest request = requestList.get(i);
                EmbeddingPropertyDTO embeddingProperty = embeddingProperties.get(i);

                List<MilvusField> properties = MilvusField.buildProperties(
                        "biz_info", request.getCategoryId(),
                        "content", request.getQuestion()
                );

                MilvusRow milvusRow = new MilvusRow();
                milvusRow.setVectorId(knowledge.getId());
                milvusRow.setProperties(properties);
                milvusRow.setEmbedding(embeddingProperty.getEmbedding());

                milvusRows.add(milvusRow);
            }

            // 4. 批量存储向量到Milvus
            if (!milvusRows.isEmpty()) {
                milvusService.saveEmbeddingBatch(FAQ_TENANT_CLIENT_KEY, MilvusPoolConfig.FAQ_COLLECTION_NAME, milvusRows);
            }

            log.info("批量保存知识及其向量完成，成功数量: {}", knowledgeList.size());
            return knowledgeList.size();
        } catch (Exception e) {
            log.error("批量保存知识失败", e);
            throw new BizException("批量保存知识失败: " + e.getMessage());
        }
    }

    @Override
    public FaqKnowledgePO getByQuestion(String question, String categoryId) {
        if (StringUtils.isBlank(question)) {
            return null;
        }

        LambdaQueryWrapper<FaqKnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqKnowledgePO::getQuestion, question);
        queryWrapper.eq(FaqKnowledgePO::getCategoryId, categoryId);
        queryWrapper.last("LIMIT 1");

        return this.getOne(queryWrapper);
    }

    @Override
    public List<FaqKnowledgeResponse> searchByRobotId(FaqSearchByRobotRequest request) {
        String robotId = request.getRobotId();
        FaqRobotPO robot = faqRobotService.getById(robotId);

        if (robot == null) {
            throw new BizException("机器人不存在");
        }
        String query = request.getQuery();

        // 先使用request中的配置（如果存在）
        Integer topK = request.getTopK();
        Float similarityThreshold = request.getSimilarityThreshold();
        String env = request.getEnv();

        // robot配置优先级更高，覆盖request配置
        if (robot.getTopK() != null) {
            topK = robot.getTopK();
        }
        if (robot.getSimilarityThreshold() != null) {
            similarityThreshold = robot.getSimilarityThreshold();
        }

        if (StringUtils.isNotBlank(request.getEnv())) {
            env = request.getEnv();
        }

        // 1. 获取问题的向量表示
        float[] embedding;
        try {
            embedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, query);
        } catch (Exception e) {
            log.error("获取问题的向量表示失败: query={}", query, e);
            // 如果QPS过高，使用v2模型兜底
            embedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V2, query);
        }

        // 构建过滤条件
        String filterString = String.format("biz_info == '%s'", robotId);

        // 2. 在Milvus中搜索相似向量
        String collectionName;
        if (EnvEnum.TEST.getCode().equals(env)) {
            collectionName = MilvusPoolConfig.FAQ_COLLECTION_NAME;
        } else if (EnvEnum.PROD.getCode().equals(env)) {
            collectionName = MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD;
        } else {
            throw new BizException("环境类型不正确");
        }

        List<VectorSearchResult> searchResults = milvusService.searchByEmbedding(
                collectionName,
                embedding,
                topK,
                similarityThreshold,
                filterString
        );

        List<FaqKnowledgeResponse> responseList = new ArrayList<>();

        if (searchResults.isEmpty()) {
            // 未找到匹配也要记录日志
            asyncService.logFaqHitAsync(request, responseList);
            return responseList;
        }

        // 3. 获取结果ID列表
        List<String> resultIds = searchResults.stream()
                .map(VectorSearchResult::getId)
                .collect(Collectors.toList());

        // 4. 从数据库中同时查询faq_knowledge和faq_similar_knowledge
        responseList = faqKnowledgeMapper.selectFromFaqKnowledgeAndFaqSimilarQuestionByIds(
                resultIds,
                robotId,
                env
        );

        if (responseList.isEmpty()) {
            log.error("向量库中找到匹配但数据库中无对应记录: robotId={}, ids={}", robotId, resultIds);
            // 数据库中无对应记录也要记录日志
            asyncService.logFaqHitAsync(request, responseList);
            return responseList;
        }

        // 5. 创建一个ID到相似度分数的映射
        Map<String, Float> idScoreMap = new HashMap<>(searchResults.size());
        for (VectorSearchResult result : searchResults) {
            idScoreMap.put(result.getId(), result.getScore());
        }

        // 6. 为每个响应对象设置相似度分数
        for (FaqKnowledgeResponse response : responseList) {
            String searchId = response.getJoinId();
            response.setScore(idScoreMap.get(searchId));
        }

        // 7. 根据相似度分数从高到低排序
        responseList.sort((a, b) -> Float.compare(b.getScore(), a.getScore()));

        // 8. 增加命中次数
        for (FaqKnowledgeResponse response : responseList) {
            if ("original".equals(response.getSource())) {
                String id = response.getId();
                faqKnowledgeMapper.incrementHitCount(id);
                FaqKnowledgePO knowledge = faqKnowledgeMapper.selectById(id);
                response.setAnswerType(knowledge.getAnswerType());
            } else {
                String knowledgeId = response.getKnowledgeId();
                FaqKnowledgePO knowledge = faqKnowledgeMapper.selectById(knowledgeId);
                response.setAnswerType(knowledge.getAnswerType());
            }
            //  添加类目名称
            response.setCategoryName(faqCategoryService.getById(response.getCategoryId()).getName());
        }

        log.info("找到匹配知识: robotId={}, 数量={}", robotId, responseList.size());

        // 找到匹配结果时也记录日志
        asyncService.logFaqHitAsync(request, responseList);

        return responseList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExcelFaqImportResultResponse importAliExcel(MultipartFile file) {
        // 校验文件
        if (file == null || file.isEmpty()) {
            throw new BizException("上传文件不能为空");
        }

        // 校验文件类型
        String originalFilename = file.getOriginalFilename();
        if (!originalFilename.endsWith(XLSX) && !originalFilename.endsWith(XLS)) {
            throw new BizException("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }

        try {
            // 创建Excel监听器
            FaqKnowledgeAliExcelListener listener = new FaqKnowledgeAliExcelListener(this, faqCategoryService, faqSimilarKnowledgeService);

            // 读取Excel
            ExcelReader excelReader = EasyExcel.read(file.getInputStream(), FaqKnowledgeAliExcelDataDTO.class, listener).build();
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
            excelReader.finish();

            // 构建导入结果
            ExcelFaqImportResultResponse result = ExcelFaqImportResultResponse.builder()
                    .total(listener.getSuccessCount() + listener.getFailCount())
                    .successCount(listener.getSuccessCount())
                    .failCount(listener.getFailCount())
                    .errorMessages(listener.getErrorMessages())
                    .build();

            log.info("阿里Excel批量导入FAQ知识完成，成功导入{}条，失败{}条", result.getSuccessCount(), result.getFailCount());
            return result;
        } catch (IOException e) {
            log.error("阿里Excel批量导入FAQ知识失败", e);
            throw new BizException("Excel导入失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferKnowledge(FaqKnowledgeTransferRequest request) {
        List<String> knowledgeIds = request.getKnowledgeIds();
        String targetCategoryId = request.getTargetCategoryId();
        String currentUser = UserThreadLocalUtil.getCurrentName();
        LocalDateTime now = LocalDateTime.now();

        // 目标类目只能是最底层的类目
        // 校验类目是否是最后一级
        LambdaQueryWrapper<FaqCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqCategoryPO::getParentId, targetCategoryId);
        long childCount = faqCategoryService.count(queryWrapper);
        if (childCount > 0) {
            throw new BizException("只能将知识转移到最后一级类目下");
        }
        // 1. 更新知识的类目ID
        List<FaqKnowledgePO> knowledgeList = this.listByIds(knowledgeIds);
        for (FaqKnowledgePO knowledge : knowledgeList) {
            knowledge.setPublishStatus("00");
            knowledge.setCategoryId(targetCategoryId);
            knowledge.setUpdatedBy(currentUser);
            knowledge.setUpdatedAt(now);
        }
        this.updateBatchById(knowledgeList);

        // 2. 更新相似问的类目ID
        List<FaqSimilarKnowledgePO> similarList = faqSimilarKnowledgeService.listByOriginalIds(knowledgeIds);
        for (FaqSimilarKnowledgePO similar : similarList) {
            similar.setCategoryId(targetCategoryId);
            similar.setUpdatedBy(currentUser);
            similar.setUpdatedAt(now);
        }
        faqSimilarKnowledgeService.updateBatchById(similarList);

        // 3. 查找绑定了该类目的机器人
        List<String> robotIds = faqRobotKnowledgeJoinsMapper.selectRobotIdsByCategoryId(targetCategoryId);


        faqRobotService.syncTestRobotKnowledge(robotIds);

    }

    @Override
    public FaqChatResponse chatWithRobot(FaqChatRequest request) {
        long startTime = System.currentTimeMillis();

        float similarityThreshold = request.getSimilarityThreshold();
        int topK = 10;

        // 1. 获取问题的向量表示
        float[] embedding;
        try {
            embedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, request.getQuery());
        } catch (Exception e) {
            log.error("获取问题的向量表示失败: query={}", request.getQuery(), e);
            embedding = embeddingService.getEmbeddingByModel(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V2, request.getQuery());
        }

        // 构建过滤条件
        String filterString = String.format("biz_info == '%s'", request.getRobotId());

        // 2. 在Milvus中搜索相似向量
        String collectionName;
        if (EnvEnum.TEST.getCode().equals(request.getEnv())) {
            collectionName = MilvusPoolConfig.FAQ_COLLECTION_NAME;
        } else if (EnvEnum.PROD.getCode().equals(request.getEnv())) {
            collectionName = MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD;
        } else {
            throw new BizException("环境类型不正确");
        }

        List<VectorSearchResult> searchResults = milvusService.searchByEmbedding(
                collectionName, embedding, topK, similarityThreshold, filterString);

        if (searchResults.isEmpty()) {
            log.info("未找到匹配的知识: robotId={}, query={}", request.getRobotId(), request.getQuery());
            return FaqChatResponse.builder().answer("抱歉，我还没有对应的知识哦，暂时无法回答这个问题。").build();
        }

        // 3. 从数据库中查询知识详情
        List<String> resultIds = searchResults.stream().map(VectorSearchResult::getId).collect(Collectors.toList());
        List<FaqKnowledgeResponse> responseList = faqKnowledgeMapper.selectFromFaqKnowledgeAndFaqSimilarQuestionByIds(
                resultIds, request.getRobotId(), request.getEnv());

        if (responseList.isEmpty()) {
            log.error("向量库中找到匹配但数据库中无对应记录: robotId={}, ids={}", request.getRobotId(), resultIds);
            return FaqChatResponse.builder().answer("抱歉，知识库出现了一点问题，请稍后再试。").build();
        }

        // 4. 处理并排序结果
        Map<String, Float> idScoreMap = searchResults.stream()
                .collect(Collectors.toMap(VectorSearchResult::getId, VectorSearchResult::getScore));
        for (FaqKnowledgeResponse response : responseList) {
            response.setScore(idScoreMap.get(response.getJoinId()));
        }
        responseList.sort((a, b) -> Float.compare(b.getScore(), a.getScore()));

        // 5. 准备Top1答案和建议列表
        FaqKnowledgeResponse topKnowledgeInfo = responseList.get(0);

        long timeTaken = System.currentTimeMillis() - startTime;
        FaqChatResponseBuilder responseBuilder = FaqChatResponse.builder().answer(topKnowledgeInfo.getAnswer());

        // 如果开启调试模式，填充详细信息和建议列表
        if (OPEN.equalsIgnoreCase(request.getDebugMode())) {
            // 填充Top1的详细信息
            enrichFaqChatResponseWithDetails(responseBuilder, topKnowledgeInfo, timeTaken);

            // 填充建议列表（包含Top1）
            List<FaqChatSuggestionResponse> suggestions = new ArrayList<>();
            for (FaqKnowledgeResponse knowledgeResponse : responseList) {
                suggestions.add(createSuggestionFromKnowledge(knowledgeResponse));
            }
            responseBuilder.suggestions(suggestions);
        }

        return responseBuilder.build();
    }

    @Override
    public IPage<FaqKnowledgeListResponse> listReportKnowledge(FaqKnowledgeListRequest request) {
        // 构建查询请求
        FaqKnowledgeQueryRequest tempRequest = new FaqKnowledgeQueryRequest();
        tempRequest.setType("00");
        tempRequest.setText(request.getKnowledgeName());
        tempRequest.setPageNum(request.getPageNum());
        tempRequest.setPageSize(request.getPageSize());
        tempRequest.setEnv(request.getEnv());
        tempRequest.setRobotId(request.getRobotId());

        // 查询知识库分页数据
        IPage<FaqKnowledgePO> result = faqKnowledgeService.pageKnowledge(tempRequest);

        // 转换为响应分页对象
        IPage<FaqKnowledgeListResponse> responsePage = new Page<>();

        // 复制分页基本信息
        responsePage.setCurrent(result.getCurrent());
        responsePage.setSize(result.getSize());
        responsePage.setTotal(result.getTotal());
        responsePage.setPages(result.getPages());

        // 处理每条记录
        List<FaqKnowledgeListResponse> responseList = result.getRecords().stream().map(record -> {
            // 查询命中次数
            String id = record.getId();
            long count = faqHitLogMapper.countByKnowledgeId(id);
            record.setHitCount(count);

            // 转换为响应对象
            FaqKnowledgeListResponse response = new FaqKnowledgeListResponse();
            response.setKnowledgeId(record.getId());
            response.setKnowledgeName(record.getQuestion()); // 假设知识名称就是question字段
            response.setCategoryName(record.getCategoryName());
            response.setHitCount(record.getHitCount());
            return response;
        }).collect(Collectors.toList());

        // 设置响应记录列表
        responsePage.setRecords(responseList);

        return responsePage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dogKnowledgeInsert(FaqDogKnowledgeInsertRequest request) {
        if (request == null || CollUtil.isEmpty(request.getKnowledgeList())) {
            throw new BizException("请求参数不能为空");
        }

        String robotId = request.getRobotId();
        // 根据机器人id查询绑定类目
        List<String> categoryIds = faqRobotKnowledgeJoinsMapper.selectCategoryIdsByRobotId(robotId);
        if (CollUtil.isEmpty(categoryIds)) {
            throw new BizException("养狗机器人未绑定类目");
        }
        if (categoryIds.size() > 1) {
            throw new BizException("养狗机器人绑定类目超过1个");
        }
        List<FaqDogKnowledgeInsertRequest.KnowledgeItem> knowledgeList = request.getKnowledgeList();

        log.info("开始处理养狗话术知识入库，机器人ID: {}, 知识数量: {}", robotId, knowledgeList.size());

        try {
            // 1. 提取所有问题进行批量embedding
            List<String> questionList = knowledgeList.stream()
                    .map(FaqDogKnowledgeInsertRequest.KnowledgeItem::getQuestion)
                    .collect(Collectors.toList());

            log.info("开始批量获取问题向量，问题数量: {}", questionList.size());
            List<EmbeddingPropertyDTO> embeddingList = embeddingService.getEmbeddingList(
                    EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, questionList);

            // 2. 使用batchSearch批量查询相似问题
            log.info("开始批量查询相似问题");
            List<List<Float>> embeddingFloatList = embeddingList.stream()
                    .map(embedding -> {
                        float[] floatArray = embedding.getEmbedding();
                        List<Float> floatList = new ArrayList<>(floatArray.length);
                        for (float f : floatArray) {
                            floatList.add(f);
                        }
                        return floatList;
                    })
                    .collect(Collectors.toList());

            String filterString = String.format("biz_info == '%s'", robotId);
            SearchResp batchSearchResp = milvusService.batchSearch(
                    MilvusPoolConfig.FAQ_COLLECTION_NAME,
                    embeddingFloatList,
                    5,
                    filterString
            );

            // 3. 处理批量搜索结果，收集需要删除的中间表ID
            Set<String> joinIdsToDelete = new HashSet<>();
            if (batchSearchResp != null && batchSearchResp.getSearchResults() != null) {
                List<List<SearchResp.SearchResult>> searchResultsList = batchSearchResp.getSearchResults();

                for (int i = 0; i < searchResultsList.size(); i++) {
                    List<SearchResp.SearchResult> searchResults = searchResultsList.get(i);
                    String currentQuestion = questionList.get(i);
                    log.debug("处理问题: {}", currentQuestion);

                    if (CollUtil.isNotEmpty(searchResults)) {
                        for (SearchResp.SearchResult result : searchResults) {
                            if (result.getScore() >= 0.9f) {
                                String joinIdToDelete = result.getId().toString();
                                joinIdsToDelete.add(joinIdToDelete);
                                log.info("发现高相似度问题，标记删除: joinId={}, score={}, 问题={}",
                                        joinIdToDelete, result.getScore(), currentQuestion);
                            }
                        }
                    }
                }
            }

            // 4. 批量删除高相似度的旧知识
            if (CollUtil.isNotEmpty(joinIdsToDelete)) {
                log.info("批量删除高相似度旧知识，中间表ID数量: {}", joinIdsToDelete.size());
                List<String> joinIdsToDeleteList = new ArrayList<>(joinIdsToDelete);

                List<FaqRobotKnowledgeJoinsPO> joinsToDelete = faqRobotKnowledgeJoinsMapper.selectBatchIds(joinIdsToDeleteList);
                List<String> knowledgeIdsToDelete = joinsToDelete.stream()
                        .map(FaqRobotKnowledgeJoinsPO::getKnowledgeId)
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(knowledgeIdsToDelete)) {
                    this.removeByIds(knowledgeIdsToDelete);
                    faqKnowledgeProdService.removeBatchByIds(knowledgeIdsToDelete);
                    log.info("删除faq_knowledge和faq_knowledge_prod知识记录，数量: {}", knowledgeIdsToDelete.size());
                }

                faqRobotKnowledgeJoinsMapper.deleteBatchIds(joinIdsToDeleteList);
                faqRobotKnowledgeJoinsProdMapper.deleteBatchIds(joinIdsToDeleteList);
                log.info("删除中间表记录，数量: {}", joinIdsToDeleteList.size());

                milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, joinIdsToDeleteList, null);
                milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD, joinIdsToDeleteList, null);
                log.info("删除Milvus向量记录，数量: {}", joinIdsToDeleteList.size());
            }

            // 5. 创建新的知识记录
            List<FaqKnowledgePO> knowledgeToInsert = new ArrayList<>();
            for (FaqDogKnowledgeInsertRequest.KnowledgeItem item : knowledgeList) {
                FaqKnowledgePO knowledge = new FaqKnowledgePO();
                knowledge.setQuestion(item.getQuestion());
                knowledge.setAnswer(item.getAnswer());
                knowledge.setHitCount(0L);
                knowledge.setCreatedBy("system");
                knowledge.setCreatedAt(LocalDateTime.now());
                knowledge.setUpdatedBy("system");
                knowledge.setUpdatedAt(LocalDateTime.now());

                // 【Bug 1 修复】: 从每个知识项中获取版本号并设置到PO中
                // 假设 FaqKnowledgePO 中有 setKnowledgeVersion(Integer) 方法
                knowledge.setKnowledgeVersion(item.getKnowledgeVersion());

                knowledge.setEffectiveType("00");
                knowledge.setPublishStatus("00");
                knowledge.setAnswerType("00");
                knowledge.setCategoryId(categoryIds.get(0));
                knowledgeToInsert.add(knowledge);
            }

            // 6. 批量保存新知识到数据库
            if (CollUtil.isNotEmpty(knowledgeToInsert)) {
                log.info("批量保存新知识到数据库，数量: {}", knowledgeToInsert.size());
                this.saveBatch(knowledgeToInsert);

                // 6.1 创建 faq_robot_knowledge_joins 中间表记录
                List<String> newKnowledgeIds = knowledgeToInsert.stream()
                        .map(FaqKnowledgePO::getId)
                        .collect(Collectors.toList());

                int joinResult = faqRobotKnowledgeJoinsMapper.bindKnowledge(robotId, newKnowledgeIds, "original");
                log.info("创建中间表关联记录，机器人ID: {}, 知识数量: {}, 创建结果: {}", robotId, newKnowledgeIds.size(), joinResult);

                // 6.2 查询新创建的中间表记录，获取joins表的ID
                List<FaqRobotKnowledgeJoinsPO> newJoinsRecords = faqRobotKnowledgeJoinsMapper.selectByRobotIdAndKnowledgeIds(robotId, newKnowledgeIds);
                log.info("查询到新创建的中间表记录数量: {}", newJoinsRecords.size());

                // 7. 批量保存向量到Milvus
                List<MilvusRow> milvusRows = new ArrayList<>();
                for (int i = 0; i < knowledgeToInsert.size(); i++) {
                    FaqKnowledgePO knowledge = knowledgeToInsert.get(i);
                    EmbeddingPropertyDTO embeddingProperty = embeddingList.get(i);

                    FaqRobotKnowledgeJoinsPO joinRecord = newJoinsRecords.stream()
                            .filter(join -> join.getKnowledgeId().equals(knowledge.getId()))
                            .findFirst()
                            .orElseThrow(() -> new BizException("未找到对应的中间表记录: " + knowledge.getId()));

                    List<MilvusField> properties = MilvusField.buildProperties(
                            "biz_info", robotId,
                            "content", knowledge.getQuestion()
                    );

                    MilvusRow milvusRow = new MilvusRow();
                    milvusRow.setVectorId(joinRecord.getId());
                    milvusRow.setProperties(properties);
                    milvusRow.setEmbedding(embeddingProperty.getEmbedding());
                    milvusRows.add(milvusRow);
                }

                log.info("批量保存向量到Milvus，数量: {}", milvusRows.size());
                milvusService.saveEmbeddingBatch(FAQ_TENANT_CLIENT_KEY, MilvusPoolConfig.FAQ_COLLECTION_NAME, milvusRows);
            }

            // 8. 发布机器人
            log.info("开始准备发布机器人: robotId={}", robotId);
            boolean loadSuccess = milvusService.flushAndLoad(FAQ_TENANT_CLIENT_KEY, MilvusPoolConfig.FAQ_COLLECTION_NAME);
            if (loadSuccess) {
                // 【Bug 2 修复】: 使用事务同步管理器，确保在当前事务成功提交后，才执行数据迁移
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            log.info("事务已提交, 开始为机器人 {} 进行数据迁移", robotId);
                            // 此处调用会开启一个新事务，读取到的是已提交的最新数据
                            faqRobotKnowledgeJoinsService.migrateAllData(List.of(robotId));
                            log.info("机器人 {} 数据迁移任务已触发", robotId);
                        } catch (InterruptedException e) {
                            log.error("机器人 {} 的数据迁移过程被中断", robotId, e);
                            Thread.currentThread().interrupt();
                        } catch (Exception e) {
                            log.error("机器人 {} 的数据迁移过程中发生未知错误", robotId, e);
                            // 可根据业务需求在此处添加失败处理逻辑，如更新机器人状态
                        }
                    }
                });

                log.info("已为机器人 {} 安排数据迁移任务（将在事务成功提交后执行）", robotId);
                log.info("养狗话术知识入库主流程处理完成，成功处理数量: {}", knowledgeToInsert.size());

            } else {
                log.error("养狗话术知识入库处理失败，Milvus加载失败: robotId={}", robotId);
                // Milvus加载失败是关键步骤，应抛出异常以回滚整个数据库事务
                throw new BizException("Milvus加载失败，无法完成知识入库，操作已回滚");
            }

        } catch (Exception e) {
            log.error("养狗话术知识入库处理失败", e);
            // 向上抛出运行时异常，确保@Transactional可以捕获并回滚
            if (e instanceof BizException) {
                throw e;
            }
            throw new BizException("养狗话术知识入库处理失败: " + e.getMessage());
        }
    }


    /**
     * 使用知识库信息丰富FaqChatResponse的详细信息
     */
    private void enrichFaqChatResponseWithDetails(FaqChatResponseBuilder builder, FaqKnowledgeResponse knowledgeInfo, long timeTaken) {
        String originalKnowledgeId;
        String originalQuestion = null;

        if (ORIGINAL.equals(knowledgeInfo.getSource())) {
            originalKnowledgeId = knowledgeInfo.getId();
            originalQuestion = knowledgeInfo.getQuestion();
        } else {
            originalKnowledgeId = knowledgeInfo.getKnowledgeId();
            if (originalKnowledgeId != null) {
                FaqKnowledgePO originalKnowledge = faqKnowledgeMapper.selectById(originalKnowledgeId);
                if (originalKnowledge != null) {
                    originalQuestion = originalKnowledge.getQuestion();
                }
            }
        }

        String categoryName = faqCategoryService.getById(knowledgeInfo.getCategoryId()).getName();

        builder.conversationId(UUID.randomUUID().toString())
                .originalKnowledgeId(originalKnowledgeId)
                .originalQuestion(originalQuestion)
                .categoryName(categoryName)
                .timeTaken(timeTaken)
                .score(knowledgeInfo.getScore());
    }

    /**
     * 从知识库信息创建建议对象
     */
    private FaqChatSuggestionResponse createSuggestionFromKnowledge(FaqKnowledgeResponse knowledgeInfo) {
        String originalKnowledgeId = null;
        String originalQuestion = null;
        String matchedQuestion = null;

        if (ORIGIN.equals(knowledgeInfo.getSource())) {
            // 如果是原始知识
            originalKnowledgeId = knowledgeInfo.getId();
            originalQuestion = knowledgeInfo.getQuestion();
            matchedQuestion = knowledgeInfo.getQuestion();
        } else if (SIMILAR.equals(knowledgeInfo.getSource())) {
            // 如果是相似问
            originalKnowledgeId = knowledgeInfo.getKnowledgeId();

            // 对于相似问，matchedQuestion应该是similar_question字段的值
            if (StringUtils.isNotBlank(knowledgeInfo.getSimilarQuestion())) {
                matchedQuestion = knowledgeInfo.getSimilarQuestion();
            } else {
                // 如果similar_question为空，使用question字段作为兜底
                matchedQuestion = knowledgeInfo.getQuestion();
                log.warn("相似问的similar_question字段为空，使用question字段作为兜底: id={}", knowledgeInfo.getId());
            }

            // 查询原始知识的question
            if (originalKnowledgeId != null) {
                FaqKnowledgePO originalKnowledge = faqKnowledgeMapper.selectById(originalKnowledgeId);
                if (originalKnowledge != null) {
                    originalQuestion = originalKnowledge.getQuestion();
                }
            }
        }

        // 最终防护逻辑，确保matchedQuestion不为空
        if (StringUtils.isBlank(matchedQuestion)) {
            log.warn("matchedQuestion仍为空，使用originalQuestion作为最终兜底: knowledgeInfo={}", knowledgeInfo);
            matchedQuestion = originalQuestion;
        }

        String categoryName = faqCategoryService.getById(knowledgeInfo.getCategoryId()).getName();

        return FaqChatSuggestionResponse.builder()
                .originalKnowledgeId(originalKnowledgeId)
                .originalQuestion(originalQuestion)
                .matchedQuestion(matchedQuestion)
                .categoryName(categoryName)
                .score(knowledgeInfo.getScore())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyKnowledge(FaqKnowledgeCopyRequest request) {
        if (request == null || CollUtil.isEmpty(request.getSourceCategoryIds()) || StrUtil.isBlank(request.getTargetCategoryId())) {
            throw new BizException("请求参数不能为空");
        }

        log.info("开始复制知识和相似问: sourceCategoryIds={}, targetCategoryId={}",
                request.getSourceCategoryIds(), request.getTargetCategoryId());

        // 1. 验证目标类目是否存在
        FaqCategoryPO targetCategory = faqCategoryService.getById(request.getTargetCategoryId());
        if (ObjectUtil.isNull(targetCategory)) {
            throw new BizException("目标类目不存在");
        }

        // 2. 收集源类目及其所有子类目的ID
        Set<String> allSourceCategoryIds = new HashSet<>();
        for (String sourceCategoryId : request.getSourceCategoryIds()) {
            allSourceCategoryIds.add(sourceCategoryId);
            // 递归收集所有子类目ID
            List<String> childCategoryIds = findAllChildrenCategoryIds(sourceCategoryId);
            allSourceCategoryIds.addAll(childCategoryIds);
        }

        log.info("收集到的所有源类目ID（包含子类目）: {}", allSourceCategoryIds);

        // 3. 查询源类目下的所有知识
        List<FaqKnowledgePO> sourceKnowledges = faqKnowledgeMapper.selectByCategoryIds(new ArrayList<>(allSourceCategoryIds));
        log.info("查询到源知识数量: {}", sourceKnowledges.size());

        // 4. 查询源类目下的所有相似问
        List<FaqSimilarKnowledgePO> sourceSimilarKnowledges = faqSimilarKnowledgeMapper.selectByCategoryIds(new ArrayList<>(allSourceCategoryIds));
        log.info("查询到源相似问数量: {}", sourceSimilarKnowledges.size());

        // 5. 复制知识
        int copiedKnowledgeCount = 0;
        Map<String, String> oldToNewKnowledgeIdMap = new HashMap<>(16);

        if (CollUtil.isNotEmpty(sourceKnowledges)) {
            List<FaqKnowledgePO> newKnowledges = new ArrayList<>();
            String currentUser = UserThreadLocalUtil.getCurrentName();
            LocalDateTime now = LocalDateTime.now();

            for (FaqKnowledgePO sourceKnowledge : sourceKnowledges) {
                FaqKnowledgePO newKnowledge = new FaqKnowledgePO();
                newKnowledge.setQuestion(sourceKnowledge.getQuestion());
                newKnowledge.setAnswer(sourceKnowledge.getAnswer());
                newKnowledge.setHitCount(0L);
                newKnowledge.setCategoryId(request.getTargetCategoryId());
                newKnowledge.setCreatedBy(currentUser);
                newKnowledge.setCreatedAt(now);
                newKnowledge.setUpdatedBy(currentUser);
                newKnowledge.setUpdatedAt(now);
                newKnowledge.setEffectiveType(sourceKnowledge.getEffectiveType());
                newKnowledge.setEffectiveStartTime(sourceKnowledge.getEffectiveStartTime());
                newKnowledge.setEffectiveEndTime(sourceKnowledge.getEffectiveEndTime());
                newKnowledge.setPublishStatus("00");
                newKnowledge.setAnswerType(sourceKnowledge.getAnswerType());
                newKnowledge.setKnowledgeVersion(sourceKnowledge.getKnowledgeVersion());

                newKnowledges.add(newKnowledge);
            }

            // 批量保存新知识
            this.saveBatch(newKnowledges);
            copiedKnowledgeCount = newKnowledges.size();

            // 建立旧知识ID到新知识ID的映射关系
            for (int i = 0; i < sourceKnowledges.size(); i++) {
                oldToNewKnowledgeIdMap.put(sourceKnowledges.get(i).getId(), newKnowledges.get(i).getId());
            }

            log.info("成功复制知识数量: {}", copiedKnowledgeCount);
        }

        // 6. 复制相似问
        int copiedSimilarKnowledgeCount = 0;
        if (CollUtil.isNotEmpty(sourceSimilarKnowledges)) {
            List<FaqSimilarKnowledgePO> newSimilarKnowledges = new ArrayList<>();
            String currentUser = UserThreadLocalUtil.getCurrentName();
            LocalDateTime now = LocalDateTime.now();

            for (FaqSimilarKnowledgePO sourceSimilar : sourceSimilarKnowledges) {
                // 查找对应的新知识ID
                String newKnowledgeId = oldToNewKnowledgeIdMap.get(sourceSimilar.getKnowledgeId());
                if (StrUtil.isNotBlank(newKnowledgeId)) {
                    FaqSimilarKnowledgePO newSimilar = new FaqSimilarKnowledgePO();
                    newSimilar.setQuestion(sourceSimilar.getQuestion());
                    newSimilar.setSimilarQuestion(sourceSimilar.getSimilarQuestion());
                    newSimilar.setCategoryId(request.getTargetCategoryId());
                    newSimilar.setAnswer(sourceSimilar.getAnswer());
                    newSimilar.setKnowledgeId(newKnowledgeId);
                    newSimilar.setCreatedBy(currentUser);
                    newSimilar.setCreatedAt(now);
                    newSimilar.setUpdatedBy(currentUser);
                    newSimilar.setUpdatedAt(now);

                    newSimilarKnowledges.add(newSimilar);
                }
            }

            // 批量保存新相似问
            if (CollUtil.isNotEmpty(newSimilarKnowledges)) {
                faqSimilarKnowledgeService.saveBatch(newSimilarKnowledges);
                copiedSimilarKnowledgeCount = newSimilarKnowledges.size();
            }

            log.info("成功复制相似问数量: {}", copiedSimilarKnowledgeCount);
        }

        // 7. 构建响应结果
        FaqKnowledgeCopyResponse response = FaqKnowledgeCopyResponse.builder()
                .knowledgeCount(copiedKnowledgeCount)
                .similarKnowledgeCount(copiedSimilarKnowledgeCount)
                .totalCount(copiedKnowledgeCount + copiedSimilarKnowledgeCount)
                .build();

        log.info("知识复制完成: {}", JSONUtil.toJsonStr(response));
    }

    /**
     * 递归查找所有子类目ID
     *
     * @param parentId 父类目ID
     * @return 所有子类目ID列表
     */
    private List<String> findAllChildrenCategoryIds(String parentId) {
        List<String> result = new ArrayList<>();

        // 查询直接子类目
        LambdaQueryWrapper<FaqCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqCategoryPO::getParentId, parentId);
        List<FaqCategoryPO> directChildren = faqCategoryService.list(queryWrapper);

        if (CollUtil.isEmpty(directChildren)) {
            return result;
        }

        // 获取直接子类目ID
        List<String> directChildrenIds = directChildren.stream()
                .map(FaqCategoryPO::getId).collect(Collectors.toList());
        result.addAll(directChildrenIds);

        // 递归查找每个子类目的子类目
        for (String childId : directChildrenIds) {
            List<String> grandChildrenIds = findAllChildrenCategoryIds(childId);
            if (CollUtil.isNotEmpty(grandChildrenIds)) {
                result.addAll(grandChildrenIds);
            }
        }

        return result;
    }
}

