package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.FaqRobotPO;
import com.faw.work.ais.aic.model.request.FaqRobotKnowledgeRequest;
import com.faw.work.ais.aic.model.request.FaqRobotQueryRequest;
import com.faw.work.ais.aic.model.request.FaqRobotSubmitRequest;
import com.faw.work.ais.aic.model.response.FaqRobotResponse;

import java.util.List;

/**
 * FAQ机器人Service接口
 */
public interface FaqRobotService extends IService<FaqRobotPO> {

    /**
     * 创建机器人
     *
     * @param request 机器人请求对象
     * @return 机器人ID
     */
    String createRobot(FaqRobotSubmitRequest request);

    /**
     * 更新机器人
     *
     * @param request 机器人请求对象
     */
    void updateRobot(FaqRobotSubmitRequest request);

    /**
     * 删除机器人
     *
     * @param id 机器人ID
     * @return 是否成功
     */
    boolean deleteRobot(String id);

    /**
     * 获取机器人详情
     *
     * @param id 机器人ID
     * @return 机器人详情
     */
    FaqRobotResponse getRobotDetail(String id, String env);

    /**
     * 分页查询机器人列表
     *
     * @param request 查询请求对象
     * @return 分页结果
     */
    IPage<FaqRobotPO> pageRobot(FaqRobotQueryRequest request);

    /**
     * 更新机器人状态
     *
     * @param id     机器人ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(String id, Integer status);

    /**
     * 发布机器人
     *
     * @param robotId 机器人id
     * @return boolean
     * @throws InterruptedException 中断异常
     */
    boolean publishRobot(String robotId) throws InterruptedException;

    void resetBindKnowledgeJoinsAndMilvusByCategoryIds(FaqRobotKnowledgeRequest request);


    void syncTestRobotKnowledge(List<String> robotIds);

    /**
     * 根据机器人ID删除关联
     *
     * @param robotId 机器人ID
     */
    void deleteByRobotId(String robotId);
}

