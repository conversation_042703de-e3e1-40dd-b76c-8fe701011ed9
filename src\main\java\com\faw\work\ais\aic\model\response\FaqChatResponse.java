package com.faw.work.ais.aic.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "机器人聊天响应")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FaqChatResponse {

    @Schema(description = "答案")
    private String answer;

    @Schema(description = "对话ID")
    private String conversationId;

    @Schema(description = "原始知识ID")
    private String originalKnowledgeId;

    @Schema(description = "原始知识的question")
    private String originalQuestion;

    @Schema(description = "类目名称")
    private String categoryName;

    @Schema(description = "耗时（毫秒）")
    private Long timeTaken;

    @Schema(description = "得分")
    private Float score;

    @Schema(description = "相关问题建议")
    private List<FaqChatSuggestionResponse> suggestions;
} 