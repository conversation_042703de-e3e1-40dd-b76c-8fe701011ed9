package com.faw.work.ais.mapper.bupcore;

import com.faw.work.ais.entity.vo.ai.BizUnitInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowInfoVO;
import com.faw.work.ais.model.AiScene;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TaskInfoMapper {

    /**
     * 获取AI覆盖角色数
     */
    Integer getAiCoverRoleNum(@Param("bizUnitInfos") List<BizUnitInfoVO> bizUnitInfos);

    /**
     * 获取AI覆盖业务单元数
     */
    Integer getAiCoverBizNum(@Param("dealName") String dealName);

    /**
     * 获取l3流程信息
     * @param dealName
     * @return
     */
    List<FlowInfoVO> getFlowInfos(@Param("dealName") String dealName);

    /**
     * 获取业务单元信息
     * @param dealCode 任务执行人code zhinengshenghe
     * @return
     */
    List<BizUnitInfoVO> getBizUnitInfos(@Param("dealCode") String dealCode);

    /**
     * 获取l3流程对的业务单元信息
     * @param dealName
     * @param flowId
     * @return
     */
    List<BizUnitInfoVO> getL3FlowBizUnitInfos(@Param("dealName") String dealName, @Param("flowId") String flowId);

    /**
     * 获取l3流程信息
     * @return 返回列表
     */
    List<AiScene> getFlowInfoList();

    /**
     * 根据流程code获取业务单元列表
     * @param vo 请求对象
     * @return 返回列表
     */
    List<BizUnitInfoVO> getUnitList(@Param("vo") FlowInfoVO vo);
}
