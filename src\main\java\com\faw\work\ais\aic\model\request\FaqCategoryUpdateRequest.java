package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 类目更新请求实体
 * <AUTHOR>
 */
@Data
@Schema(description = "类目更新请求")
public class FaqCategoryUpdateRequest {
    
    @Schema(description = "类目ID")
    @NotNull(message = "类目ID不能为空")
    private String id;
    
    @Schema(description = "类目名称")
    @NotBlank(message = "类目名称不能为空")
    @Size(max = 255, message = "类目名称长度不能超过255")
    private String name;
    
    @Schema(description = "更新人")
    private String updatedBy;
} 