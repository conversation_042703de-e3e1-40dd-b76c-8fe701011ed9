package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 灵小犀 请求参数类
 *
 * <AUTHOR>
 * @since 2025-04-03 15:03
 */
@Data
@Schema(description = "灵小犀 请求参数类")
public class AiChatRequest {

    /**
     * 应用id
     */
    @Schema(description = "应用id")
    private String appId;

    /**
     * 用户信息
     */
    @Schema(description = "用户信息")
    private UserInfo userInfo;

    /**
     * 会话id
     */
    @Schema(description = "会话id")
    private String sessionId;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String modelName;

    /**
     * 图片链接
     */
    @Schema(description = "图片链接")
    private List<String> imageUrls;

    /**
     * 问题
     */
    @Schema(description = "问题")
    private String question;

}
