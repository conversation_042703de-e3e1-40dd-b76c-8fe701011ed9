package com.faw.work.ais.service.impl;

import com.dcp.common.rest.Result;
import com.faw.work.ais.common.enums.ErrorMsgEnum;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.faw.work.ais.mapper.ais.AiSceneDao;
import com.faw.work.ais.model.AiAuditModel;
import com.faw.work.ais.model.AiScene;
import com.faw.work.ais.model.base.PageList;
import com.faw.work.ais.model.base.PageListUtils;
import com.faw.work.ais.service.AiSceneService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;


/**
 * ai场景配置表
 * Created  by Mr.hp
 *
 * <AUTHOR> Mr.hp
 * DateTime on 2025-05-08 10:57:36
 */
@Slf4j
@Service
public class AiSceneServiceImpl implements AiSceneService {

    @Resource
    private AiSceneDao aiSceneDao;

    /**
     * 新增或修改
     */
    @Override
    public Result<Integer> insertOrUpdate(AiScene aiScene) {
        Result<Integer> result;
        try {

            if (StringUtils.isEmpty(aiScene.getSceneName())) {
                return Result.failed("场景名称不能为空");
            }
            if (StringUtils.isEmpty(aiScene.getSceneCode())) {
                return Result.failed("场景编码不能为空");
            }

            List<AiScene> codeList = aiSceneDao.getExitCodeAiSceneList(aiScene);
            if (CollectionUtils.isNotEmpty(codeList)) {
                return Result.failed("场景编码已存在");
            }
            List<AiScene> nameList = aiSceneDao.getExitNameAiSceneList(aiScene);
            if (CollectionUtils.isNotEmpty(nameList)) {
                return Result.failed("场景名称已存在");
            }
            if (aiScene.getComparisonResults() == null) {
                return Result.failed("对比结果不能为空");
            } else {
                if (aiScene.getComparisonResults() == 0 && aiScene.getRejectNum() == null) {
                    return Result.failed("驳回转人工次数不能为空");
                }
            }

            if (null != aiScene.getId()) {
                //修改
                result = update(aiScene);
            } else { //新增
                result = insert(aiScene);
            }
        } catch (Exception e) {
            log.error("error.impl.insertOrUpdate", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return result;

    }

    /**
     * 新增
     */
    @Override
    public Result<Integer> insert(AiScene aiScene) {
        int result = 0;
        try {
            result = aiSceneDao.insert(aiScene);
        } catch (Exception e) {
            log.error("error.impl.insert", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);

    }

    /**
     * 删除
     */
    @Override
    public Result<Integer> delete(Long id) {
        int result = 0;
        try {
            result = aiSceneDao.delete(id);
        } catch (Exception e) {
            log.error("error.impl.delete", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);
    }

    /**
     * 修改
     */
    @Override
    public Result<Integer> update(AiScene aiScene) {
        int result = 0;
        try {
            result = aiSceneDao.update(aiScene);
        } catch (Exception e) {
            log.error("error.impl.update", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);
    }

    /**
     * 根据Id查询
     */
    @Override
    public Result<AiScene> getAiSceneById(Long id) {
        AiScene result;
        try {
            result = aiSceneDao.getAiSceneById(id);
        } catch (Exception e) {
            log.error("error.impl.getById", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);
    }

    /**
     * 分页全部查询
     */
    public Result<PageList<AiAuditModel>> getAiSceneList(AiScene aiScene) {


        PageList<AiAuditModel> pageList;

        PageHelper.startPage(aiScene.getPageNum(), aiScene.getPageSize());
        List<AiScene> list = aiSceneDao.getAiSceneList(aiScene);
        pageList = PageListUtils.convertToResult(list);
        return Result.success(pageList);


    }

    @Override
    public Result<AiScene> getAiSceneByCode(String sceneCode) {
        return Result.success(aiSceneDao.getAiSceneByCode(sceneCode));
    }

}

