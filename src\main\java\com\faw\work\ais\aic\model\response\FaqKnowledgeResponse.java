package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * FAQ知识响应对象
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "FAQ知识响应对象")
public class FaqKnowledgeResponse {
    
    @Schema(description = "知识ID")
    private String id;
    

    
    @Schema(description = "问题")
    private String question;
    
    @Schema(description = "答案")
    private String answer;
    
    @Schema(description = "相似问法")
    private String similarQuestion;
    
    @Schema(description = "来源类型")
    private String source;
    
    @Schema(description = "相似度分数")
    private Float score;
    @Schema(description = "关联id")
    private String joinId;

    @Schema(description = "分类名称")
    private String categoryName;
    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "答案类型 00-纯文本 01-富文本")
    private String answerType;
    @Schema(description = "原始知识ID")
    private String knowledgeId;

} 