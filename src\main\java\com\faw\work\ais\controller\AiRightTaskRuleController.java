package com.faw.work.ais.controller;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleVO;
import com.faw.work.ais.service.RightTaskRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Schema(description = "AI看板")
@Slf4j
@RestController("AiRightController")
@RequestMapping("/rightTaskRule")
public class AiRightTaskRuleController {

    @Autowired
    private RightTaskRuleService rightTaskRuleService;
    @Operation(summary = "获取正确率", description = "[author:10236535]")
    @PostMapping(value = "/get")
    public Response<HumanSampleRightRateRuleVO> getSampleTaskRuleInfo(@RequestBody @Valid NumberEmployeeDTO numberEmployeeDTO) {
        HumanSampleRightRateRuleVO humanSampleRightRateRuleVO = rightTaskRuleService.getSampleTaskRuleInfo(numberEmployeeDTO);
        return Response.success(humanSampleRightRateRuleVO);
    }
}
