package com.faw.work.ais.service;

import com.faw.work.ais.entity.dto.ai.AiKanBanDTO;
import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.vo.ai.*;

import java.util.List;

/**
 * AI运营看板Service
 */
public interface KanBanService {

    /**
     * 获取A已审核明细表
     * @return
     */
    public KanbanTopDataVO getAiCheckDetails(AiKanBanDTO aiKanBanDTO);

    /**
     * 获取系统信息
     * @return
     */
    public List<SystemVO> getSystemInfo();

    /**
     * 获取规则名称下拉列表
     * @return
     */
    public List<TaskRuleVO> getTaskRule();

    /**
     * 获取单次AI请求时使用的文件的下载地址
     * @return
     */
    public List<BeCheckFileInfoVO> getfileInfosByTraceId(FileDTO fileDTO);

    /**
     * 各个系统趋势图
     * @param aiKanBanDTO
     * @return
     */
    public List<RateTrendChartVO> getTrendChard(AiKanBanDTO aiKanBanDTO);

    /**
     * 根据traceId获取python入参
     * @param fileDTO
     * @return
     */
    public String getPythonInParamByTraceId(FileDTO fileDTO);
}
