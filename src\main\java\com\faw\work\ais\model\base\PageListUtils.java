package com.faw.work.ais.model.base;

import com.github.pagehelper.Page;

import java.util.List;

/**
 * Created by hp
 */
public class PageListUtils {
    public static PageList convertToResult(List<?> gridLists){
        PageList searchResult = null;
        if(null != gridLists){
            if(gridLists instanceof Page){
                Page<?>	page = (Page<?>) gridLists;
                searchResult = new PageList(page.getTotal(), page.getResult());
            }else{
                searchResult = new PageList(gridLists.size(), gridLists);
            }
        }else{
            searchResult = new PageList(0, null);
        }
        return searchResult;
    }
}
