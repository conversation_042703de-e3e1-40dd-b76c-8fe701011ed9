package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.mapper.faq.FaqCategoryMapper;
import com.faw.work.ais.aic.mapper.faq.FaqKnowledgeMapper;
import com.faw.work.ais.aic.mapper.faq.FaqRobotKnowledgeJoinsMapper;
import com.faw.work.ais.aic.mapper.faq.FaqSimilarKnowledgeMapper;
import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import com.faw.work.ais.aic.model.request.FaqCategoryRequest;
import com.faw.work.ais.aic.model.request.FaqKnowledgeConditionRequest;
import com.faw.work.ais.aic.model.response.FaqCategoryResponse;
import com.faw.work.ais.aic.service.FaqCategoryService;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.faw.work.ais.convert.FaqCategoryPOConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识类目服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqCategoryServiceImpl extends ServiceImpl<FaqCategoryMapper, FaqCategoryPO> implements FaqCategoryService {

    public static final String ZERO = "00";
    public static final String ZERO_ONE = "01";
    public static final String ZERO_TWO = "02";
    @Autowired
    private MilvusService milvusService;
    @Autowired
    private FaqKnowledgeMapper faqKnowledgeMapper;
    @Autowired
    private FaqCategoryPOConverter faqcategorypoconverter;

    @Autowired
    private FaqCategoryMapper faqCategoryMapper;

    @Autowired
    private FaqRobotKnowledgeJoinsMapper faqRobotKnowledgeJoinsMapper;

    @Autowired
    private FaqSimilarKnowledgeMapper faqSimilarKnowledgeMapper;


    @Override
    public List<FaqCategoryPO> getCategoryListByName(String categoryName) {
        return faqCategoryMapper.selectCategoryListByName(categoryName);
    }



    @Override
    public String getIdByName(String categoryName) {
        LambdaQueryWrapper<FaqCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqCategoryPO::getName, categoryName);
        queryWrapper.last("LIMIT 1");

        FaqCategoryPO category = getOne(queryWrapper);
        return category != null ? category.getId() : null;
    }

    @Override
    public String createCategory(String categoryName) {
        FaqCategoryPO category = new FaqCategoryPO();
        category.setName(categoryName);
        category.setCreatedBy(UserThreadLocalUtil.getRealName());
        category.setCreatedAt(LocalDateTime.now());
        category.setUpdatedAt(LocalDateTime.now());

        save(category);
        return category.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFlexibleCategory(FaqCategoryRequest request) {
        // 校验名称是否已存在
        LambdaQueryWrapper<FaqCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqCategoryPO::getName, request.getName());
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new RuntimeException("类目名称已存在");
        }

        // 创建类目实体
        FaqCategoryPO category = new FaqCategoryPO();
        category.setName(request.getName());
        category.setCreatedBy(UserThreadLocalUtil.getCurrentName());
        category.setCreatedAt(LocalDateTime.now());

        // 处理不同的创建场景
        String type = request.getType();
        String parentId = request.getParentId();

        if (ZERO.equals(type)) {
            // 创建一级类目，parentId为null
            category.setParentId(null);
        }

        if (ZERO_ONE.equals(type)) {
            // 创建平级类目，parentId为父类目ID
            category.setParentId(parentId);
        }

        if (ZERO_TWO.equals(type)) {
            // 创建下级类目，需要校验父类目是否存在知识
            FaqKnowledgeConditionRequest conditionRequest = new FaqKnowledgeConditionRequest();
            List<String> categoryIds = new ArrayList<>();
            categoryIds.add(parentId);
            conditionRequest.setCategoryIdList(categoryIds);
            List<FaqKnowledgePO> knowledgeList = faqKnowledgeMapper.selectAllByCondition(conditionRequest);
            if (!CollectionUtils.isEmpty(knowledgeList)) {
                throw new BizException("父类目下存在知识，无法创建子类目");
            }
            category.setParentId(parentId);
        }

        // 保存类目
        this.save(category);
        log.info("成功创建类目: id={}, name={}, parentId={}",
                category.getId(), category.getName(), category.getParentId());

        return category.getId();
    }


    @Override
    public List<FaqCategoryResponse> treeCategory(String env) {
        // 1. 查询名称匹配的顶级类目
        List<FaqCategoryPO> root = faqCategoryMapper.findAllRootCategoryList(env);

        if (CollectionUtils.isEmpty(root)) {
            return new ArrayList<>();
        }

        List<FaqCategoryResponse> rootResponses = root.stream()
                .map(faqcategorypoconverter::convert2FaqCategoryResponse)
                .collect(Collectors.toList());

        // 2. 为每个顶级类目递归加载其子类目（子类目也需要匹配名称）
        for (FaqCategoryResponse rootResponse : rootResponses) {
            loadChildrenRecursivelyByName(rootResponse, env);
        }

        return rootResponses;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllDataByCategoryId(String id) {
        log.info("开始删除类目及其关联数据, categoryId={}", id);


        List<String> categoryIds = new ArrayList<>();
        categoryIds.add(id);

        // 递归获取所有子类目ID
        List<String> childrenIds = this.findAllChildrenCategoryIds(id);
        if (!CollectionUtils.isEmpty(childrenIds)) {
            categoryIds.addAll(childrenIds);
        }

        log.info("需要删除的类目ID列表: {}", categoryIds);

        // 2. 删除知识库和Milvus中的相关数据
        List<FaqSimilarKnowledgePO> oldSimilarKnowledges = faqSimilarKnowledgeMapper.selectByCategoryIds(categoryIds);
        List<String> idsOne = oldSimilarKnowledges.stream().map(FaqSimilarKnowledgePO::getId).toList();

        List<FaqKnowledgePO> oldKnowledges = faqKnowledgeMapper.selectByCategoryIds(categoryIds);
        List<String> idsTwo = oldKnowledges.stream().map(FaqKnowledgePO::getId).toList();

        if (CollUtil.isNotEmpty(idsTwo)) {
            throw new BizException("该类目下存在知识库，无法删除");
        }

        List<String> allKnowlegeIds = new ArrayList<>(idsOne);
        allKnowlegeIds.addAll(idsTwo);


        // 2.1 删除faq_robot_knowledge_joins中的关联数据
        List<String> joinIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(allKnowlegeIds)) {
            joinIds = faqRobotKnowledgeJoinsMapper.selectByKnowledgeIds(allKnowlegeIds).stream().map(FaqRobotKnowledgeJoinsPO::getId).toList();
            faqRobotKnowledgeJoinsMapper.deleteByKnowledgeIds(allKnowlegeIds);
        }

        // 2.2 删除faq_similar_knowledge中的关联数据
        if (CollUtil.isNotEmpty(idsOne)) {
            faqSimilarKnowledgeMapper.deleteBatchIds(idsOne);
        }
        // 2.3 删除faq_knowledge中的关联数据
        if (CollUtil.isNotEmpty(idsTwo)) {
            faqKnowledgeMapper.deleteBatchIds(idsTwo);
        }

        // 3. 最后按照自底向上的顺序删除类目（先删子类目再删父类目）
        this.removeByIds(childrenIds);

        // 4. 删除当前类目
        this.removeById(id);

        // 从Milvus中删除向量数据
        milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, joinIds, null);

        log.info("成功删除类目及其关联数据, categoryId={}", id);
    }

    /**
     * 递归查找所有子类目ID
     *
     * @param parentId 父类目ID
     * @return 所有子类目ID列表
     */
    private List<String> findAllChildrenCategoryIds(String parentId) {
        List<String> result = new ArrayList<>();

        // 查询直接子类目
        LambdaQueryWrapper<FaqCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqCategoryPO::getParentId, parentId);
        List<FaqCategoryPO> directChildren = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(directChildren)) {
            return result;
        }

        // 获取直接子类目ID
        List<String> directChildrenIds = directChildren.stream()
                .map(FaqCategoryPO::getId).toList();
        result.addAll(directChildrenIds);

        // 递归查找每个子类目的子类目
        for (String childId : directChildrenIds) {
            List<String> grandChildrenIds = findAllChildrenCategoryIds(childId);
            if (!CollectionUtils.isEmpty(grandChildrenIds)) {
                result.addAll(grandChildrenIds);
            }
        }

        return result;
    }

    /**
     * 递归加载符合名称条件的子类目
     *
     * @param parentNode 父节点响应对象
     * @param env 环境名称
     */
    private void loadChildrenRecursivelyByName(FaqCategoryResponse parentNode, String env) {
        // 查询父节点下名称匹配的直接子类目
        List<FaqCategoryPO> children = faqCategoryMapper.findChildrenByParentId(parentNode.getId(), env);

        if (!CollectionUtils.isEmpty(children)) {
            List<FaqCategoryResponse> childrenResponses = children.stream()
                    .map(faqcategorypoconverter::convert2FaqCategoryResponse)
                    .collect(Collectors.toList());

            parentNode.setChildren(childrenResponses);

            // 为每个子类目递归加载其子类目
            for (FaqCategoryResponse childResponse : childrenResponses) {
                loadChildrenRecursivelyByName(childResponse, env);
            }
        } else {
            // 确保即使没有子节点，children属性也已初始化为空列表，符合API契约
            parentNode.setChildren(new ArrayList<>());
        }
    }

} 