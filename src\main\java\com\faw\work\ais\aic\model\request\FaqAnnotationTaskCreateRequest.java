package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * FAQ标注任务创建请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ标注任务创建请求对象")
public class FaqAnnotationTaskCreateRequest {

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务名称不能为空")
    @Size(max = 50, message = "任务名称不能超过50字")
    private String taskName;

    @Schema(description = "机器人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "机器人ID不能为空")
    private String robotId;

    @Schema(description = "数据来源：all-全部，prod-正式环境，test-测试环境", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据来源不能为空")
    private String dataSource;

    @Schema(description = "通话类型：all-全部通话，unanswered-仅答非所问", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "通话类型不能为空")
    private String callType;

    @Schema(description = "抽取开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "抽取开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "抽取结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "抽取结束时间不能为空")
    private LocalDateTime endTime;

}
