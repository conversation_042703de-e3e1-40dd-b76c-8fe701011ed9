package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 智联APP 请求参数类
 *
 * <AUTHOR>
 * @since 2025-06-25 11:03
 */
@Data
@Builder
@Schema(description = "智联APP 请求参数类")
public class AppRequest {

    /**
     * 车辆vin
     */
    @Schema(description = "车辆vin")
    private String vin;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String aid;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String cityCode;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String lon;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String lat;

}
