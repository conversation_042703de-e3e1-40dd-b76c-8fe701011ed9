package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文档类目表
 *
 * <AUTHOR>
 */
@Data
@TableName("rag_category")
@Schema(description = "文档类目实体")
public class RagCategoryPO {

    @TableId(value = "id")
    @Schema(description = "类目ID")
    private Long id;

    @TableField("name")
    @Schema(description = "类目名称")
    private String name;

    @TableField("tenant_id")
    @Schema(description = "租户ID，用于多租户隔离")
    private Long tenantId;

    @TableField("parent_id")
    @Schema(description = "父类目ID，用于构建类目树结构")
    private Long parentId;

    @TableField("created_by")
    @Schema(description = "创建人")
    private String createdBy;

    @TableField("created_at")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField("updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @TableField("updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 