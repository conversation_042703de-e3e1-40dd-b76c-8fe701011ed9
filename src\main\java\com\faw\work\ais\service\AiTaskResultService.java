package com.faw.work.ais.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.entity.dto.ai.AiTaskQueryDTO;
import com.faw.work.ais.entity.vo.ai.AiTaskResultVO;
import com.faw.work.ais.model.AiTaskResult;

/**
* <AUTHOR>
* @description 针对表【ai_task_result(ai任务结果记录表)】的数据库操作Service
* @createDate 2024-04-07 21:40:12
*/
public interface AiTaskResultService extends IService<AiTaskResult> {
    /**
     * 获取ai处理返回结果
     * @param aiTaskQueryDTO
     * @return
     */
    Page<AiTaskResultVO> getAiResult(AiTaskQueryDTO aiTaskQueryDTO);

    /**
     *
     * @param batchId
     * @return
     */
//    AiTaskResult getAiResultByBatchId(String batchId);
}
