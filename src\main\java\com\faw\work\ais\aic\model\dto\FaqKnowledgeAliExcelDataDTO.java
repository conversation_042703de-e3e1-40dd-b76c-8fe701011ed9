package com.faw.work.ais.aic.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * FAQ知识阿里Excel导入数据模型
 * <AUTHOR>
 */
@Data
public class FaqKnowledgeAliExcelDataDTO {
    
    /**
     * 类目名称
     */
    @ExcelProperty("类目名称")
    private String categoryName;
    
    /**
     * 知识标题（问题）
     */
    @ExcelProperty("知识标题")
    private String question;
    
    /**
     * 相似问法
     */
    @ExcelProperty("相似问法")
    private String similarQuestion;
    
    /**
     * 纯文本答案
     */
    @ExcelProperty("答案（默认)【纯文本】")
    private String plainTextAnswer;
    
    /**
     * 富文本答案
     */
    @ExcelProperty("答案（默认)【富文本】")
    private String richTextAnswer;
} 