package com.faw.work.ais.common.enums;

/**
 * @Auther:         hp
 * @Date:数据类型（0-AI审核任务数，1-AI审核任务通过数，2-AI审核材料数，3-AI审核材料通过数，
 * 4-人工审核任务数，5-人工审核任务通过数，
 * 6-人工复审材料数，7-人工复审材料准确数，
 * 8-TP数，9-FP数，10-TN数，11-FN数）
 * @Description:
 */

public enum DataTypeEnum {
    AI_ZERO(0,"AI审核任务数:batid 一个算条"),
    AI_ONE(1,"AI审核任务通过数：batid 都通过 算一条"),
    AI_TWO(2,"AI审核材料数：每一条记录算一条"),
    AI_THREE(3,"AI审核材料通过数： 每一条 通过算一条"),

    HUM_FOUR(4,"人工审核任务数:BATID 一个算条"),
    HUM_FIVE(5,"人工审核任务通过数：BATID 都通过 算一条"),
    HUM_SIX(6,"人工复审材料数：每一条记录"),
    HUM_SEVEN(7,"人工复审材料准确数： 每一条 通过算一条"),

    TP(8,"TP数：ai √ ，人工 √"),
    FP(9,"FP数：ai √ ，人工 ×"),
    TN(10,"TN数：ai × ，人工 ×"),
    FN(11,"FN数：ai × ，人工 √"),
    ;


    private final Integer code;
    private final String message;


    DataTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private Integer code() {
        return this.code;
    }

    private String message() {
        return this.message;
    }



    /**
     * 根据key获取value
     *
     * @param code
     * @return
     */
    public static String getValue(Integer code) {
        DataTypeEnum[] LevelingStatusEnums = values();
        for (DataTypeEnum LevelingStatusEnum : LevelingStatusEnums) {
            if (LevelingStatusEnum.code().equals(code)) {
                return LevelingStatusEnum.message();
            }
        }
        return null;
    }



    /**
     * 根据value获取key
     *
     * @param message
     * @return
     */
    public static Integer getCode(String message) {
        DataTypeEnum[] LevelingStatusEnums = values();
        for (DataTypeEnum LevelingStatusEnum : LevelingStatusEnums) {
            if (LevelingStatusEnum.message().equals(message)) {
                return LevelingStatusEnum.code();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
