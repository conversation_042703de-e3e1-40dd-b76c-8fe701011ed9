package com.faw.work.ais.aic.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Milvus文档属性
 * 用于动态指定Milvus文档的属性和值
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MilvusField {
    
    /**
     * 属性名
     */
    private String name;
    
    /**
     * 属性值
     */
    private Object value;


    /**
     * 使用键值对构建多个MilvusProperty实例
     *
     * @param kvs 可变参数列表，必须为键值对形式，即k1, v1, k2, v2...
     * @return 包含MilvusProperty对象的列表
     */
    public static List<MilvusField> buildProperties(Object... kvs) {
        if (kvs.length % 2 != 0) {
            throw new IllegalArgumentException("参数必须是键值对形式");
        }
        List<MilvusField> properties = new ArrayList<>();
        for (int i = 0; i < kvs.length; i += 2) {
            MilvusField property = new MilvusField();
            property.setName(kvs[i].toString());
            property.setValue(kvs[i + 1]);
            properties.add(property);
        }
        return properties;
    }


    /**
     * 将 Map 转换为 MilvusProperty 列表
     *
     * @param map 键值对形式的 Map
     * @return 包含 MilvusProperty 对象的列表
     */
    public static List<MilvusField> buildProperties(Map<String, Object> map) {
        List<MilvusField> properties = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            MilvusField property = new MilvusField();
            property.setName(entry.getKey());
            property.setValue(entry.getValue());
            properties.add(property);
        }
        return properties;
    }
} 