package com.faw.work.ais.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PrdRuleTestExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PrdRuleTestExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUrlIsNull() {
            addCriterion("url is null");
            return (Criteria) this;
        }

        public Criteria andUrlIsNotNull() {
            addCriterion("url is not null");
            return (Criteria) this;
        }

        public Criteria andUrlEqualTo(String value) {
            addCriterion("url =", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualTo(String value) {
            addCriterion("url <>", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThan(String value) {
            addCriterion("url >", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualTo(String value) {
            addCriterion("url >=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThan(String value) {
            addCriterion("url <", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualTo(String value) {
            addCriterion("url <=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLike(String value) {
            addCriterion("url like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotLike(String value) {
            addCriterion("url not like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlIn(List<String> values) {
            addCriterion("url in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotIn(List<String> values) {
            addCriterion("url not in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlBetween(String value1, String value2) {
            addCriterion("url between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotBetween(String value1, String value2) {
            addCriterion("url not between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNull() {
            addCriterion("task_type is null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNotNull() {
            addCriterion("task_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeEqualTo(String value) {
            addCriterion("task_type =", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotEqualTo(String value) {
            addCriterion("task_type <>", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThan(String value) {
            addCriterion("task_type >", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThanOrEqualTo(String value) {
            addCriterion("task_type >=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThan(String value) {
            addCriterion("task_type <", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThanOrEqualTo(String value) {
            addCriterion("task_type <=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLike(String value) {
            addCriterion("task_type like", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotLike(String value) {
            addCriterion("task_type not like", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIn(List<String> values) {
            addCriterion("task_type in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotIn(List<String> values) {
            addCriterion("task_type not in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeBetween(String value1, String value2) {
            addCriterion("task_type between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotBetween(String value1, String value2) {
            addCriterion("task_type not between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("system_id is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("system_id is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(String value) {
            addCriterion("system_id =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(String value) {
            addCriterion("system_id <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(String value) {
            addCriterion("system_id >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(String value) {
            addCriterion("system_id >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(String value) {
            addCriterion("system_id <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(String value) {
            addCriterion("system_id <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLike(String value) {
            addCriterion("system_id like", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotLike(String value) {
            addCriterion("system_id not like", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<String> values) {
            addCriterion("system_id in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<String> values) {
            addCriterion("system_id not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(String value1, String value2) {
            addCriterion("system_id between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(String value1, String value2) {
            addCriterion("system_id not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andTraceidIsNull() {
            addCriterion("traceid is null");
            return (Criteria) this;
        }

        public Criteria andTraceidIsNotNull() {
            addCriterion("traceid is not null");
            return (Criteria) this;
        }

        public Criteria andTraceidEqualTo(String value) {
            addCriterion("traceid =", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotEqualTo(String value) {
            addCriterion("traceid <>", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidGreaterThan(String value) {
            addCriterion("traceid >", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidGreaterThanOrEqualTo(String value) {
            addCriterion("traceid >=", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidLessThan(String value) {
            addCriterion("traceid <", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidLessThanOrEqualTo(String value) {
            addCriterion("traceid <=", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidLike(String value) {
            addCriterion("traceid like", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotLike(String value) {
            addCriterion("traceid not like", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidIn(List<String> values) {
            addCriterion("traceid in", values, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotIn(List<String> values) {
            addCriterion("traceid not in", values, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidBetween(String value1, String value2) {
            addCriterion("traceid between", value1, value2, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotBetween(String value1, String value2) {
            addCriterion("traceid not between", value1, value2, "traceid");
            return (Criteria) this;
        }

        public Criteria andHumanResultIsNull() {
            addCriterion("human_result is null");
            return (Criteria) this;
        }

        public Criteria andHumanResultIsNotNull() {
            addCriterion("human_result is not null");
            return (Criteria) this;
        }

        public Criteria andHumanResultEqualTo(String value) {
            addCriterion("human_result =", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultNotEqualTo(String value) {
            addCriterion("human_result <>", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultGreaterThan(String value) {
            addCriterion("human_result >", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultGreaterThanOrEqualTo(String value) {
            addCriterion("human_result >=", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultLessThan(String value) {
            addCriterion("human_result <", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultLessThanOrEqualTo(String value) {
            addCriterion("human_result <=", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultLike(String value) {
            addCriterion("human_result like", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultNotLike(String value) {
            addCriterion("human_result not like", value, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultIn(List<String> values) {
            addCriterion("human_result in", values, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultNotIn(List<String> values) {
            addCriterion("human_result not in", values, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultBetween(String value1, String value2) {
            addCriterion("human_result between", value1, value2, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanResultNotBetween(String value1, String value2) {
            addCriterion("human_result not between", value1, value2, "humanResult");
            return (Criteria) this;
        }

        public Criteria andHumanScoreIsNull() {
            addCriterion("human_score is null");
            return (Criteria) this;
        }

        public Criteria andHumanScoreIsNotNull() {
            addCriterion("human_score is not null");
            return (Criteria) this;
        }

        public Criteria andHumanScoreEqualTo(String value) {
            addCriterion("human_score =", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreNotEqualTo(String value) {
            addCriterion("human_score <>", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreGreaterThan(String value) {
            addCriterion("human_score >", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreGreaterThanOrEqualTo(String value) {
            addCriterion("human_score >=", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreLessThan(String value) {
            addCriterion("human_score <", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreLessThanOrEqualTo(String value) {
            addCriterion("human_score <=", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreLike(String value) {
            addCriterion("human_score like", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreNotLike(String value) {
            addCriterion("human_score not like", value, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreIn(List<String> values) {
            addCriterion("human_score in", values, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreNotIn(List<String> values) {
            addCriterion("human_score not in", values, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreBetween(String value1, String value2) {
            addCriterion("human_score between", value1, value2, "humanScore");
            return (Criteria) this;
        }

        public Criteria andHumanScoreNotBetween(String value1, String value2) {
            addCriterion("human_score not between", value1, value2, "humanScore");
            return (Criteria) this;
        }

        public Criteria andAiResultIsNull() {
            addCriterion("ai_result is null");
            return (Criteria) this;
        }

        public Criteria andAiResultIsNotNull() {
            addCriterion("ai_result is not null");
            return (Criteria) this;
        }

        public Criteria andAiResultEqualTo(String value) {
            addCriterion("ai_result =", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultNotEqualTo(String value) {
            addCriterion("ai_result <>", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultGreaterThan(String value) {
            addCriterion("ai_result >", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultGreaterThanOrEqualTo(String value) {
            addCriterion("ai_result >=", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultLessThan(String value) {
            addCriterion("ai_result <", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultLessThanOrEqualTo(String value) {
            addCriterion("ai_result <=", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultLike(String value) {
            addCriterion("ai_result like", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultNotLike(String value) {
            addCriterion("ai_result not like", value, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultIn(List<String> values) {
            addCriterion("ai_result in", values, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultNotIn(List<String> values) {
            addCriterion("ai_result not in", values, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultBetween(String value1, String value2) {
            addCriterion("ai_result between", value1, value2, "aiResult");
            return (Criteria) this;
        }

        public Criteria andAiResultNotBetween(String value1, String value2) {
            addCriterion("ai_result not between", value1, value2, "aiResult");
            return (Criteria) this;
        }

        public Criteria andRawResultIsNull() {
            addCriterion("raw_result is null");
            return (Criteria) this;
        }

        public Criteria andRawResultIsNotNull() {
            addCriterion("raw_result is not null");
            return (Criteria) this;
        }

        public Criteria andRawResultEqualTo(String value) {
            addCriterion("raw_result =", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultNotEqualTo(String value) {
            addCriterion("raw_result <>", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultGreaterThan(String value) {
            addCriterion("raw_result >", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultGreaterThanOrEqualTo(String value) {
            addCriterion("raw_result >=", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultLessThan(String value) {
            addCriterion("raw_result <", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultLessThanOrEqualTo(String value) {
            addCriterion("raw_result <=", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultLike(String value) {
            addCriterion("raw_result like", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultNotLike(String value) {
            addCriterion("raw_result not like", value, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultIn(List<String> values) {
            addCriterion("raw_result in", values, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultNotIn(List<String> values) {
            addCriterion("raw_result not in", values, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultBetween(String value1, String value2) {
            addCriterion("raw_result between", value1, value2, "rawResult");
            return (Criteria) this;
        }

        public Criteria andRawResultNotBetween(String value1, String value2) {
            addCriterion("raw_result not between", value1, value2, "rawResult");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}