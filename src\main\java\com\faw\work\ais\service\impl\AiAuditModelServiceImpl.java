package com.faw.work.ais.service.impl;

import com.dcp.common.rest.Result;
import com.faw.work.ais.common.UserInfo;
import com.faw.work.ais.common.enums.ErrorMsgEnum;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.faw.work.ais.mapper.ais.*;
import com.faw.work.ais.model.*;
import com.faw.work.ais.model.base.PageList;
import com.faw.work.ais.model.base.PageListUtils;
import com.faw.work.ais.service.AiAuditModelService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;


/**
 * ai审核模型配置表
 * Created  by Mr.hp
 *
 * <AUTHOR> Mr.hp
 * DateTime on 2025-05-08 10:36:35
 */
@Slf4j
@Service
public class AiAuditModelServiceImpl implements AiAuditModelService {

    @Resource
    private AiAuditModelDao aiAuditModelDao;
    @Resource
    private AiTechnologyModelDao aiTechnologyModelDao;
    @Resource
    private AiAuditModelPointDao aiAuditModelPointDao;
    @Resource
    private AiAuditModelSceneDao aiAuditModelSceneDao;
    @Resource
    private AiAuditModelTechnologyDao aiAuditModelTechnologyDao;

    /**
     * 新增或修改
     */
    @Override
    public Result<Integer> insertOrUpdate(AiAuditModel aiAuditModel) {
        int result;
        try {
            if (StringUtils.isEmpty(aiAuditModel.getAuditModelName())) {
                return Result.failed("模型名称不能为空");
            }
           // String userName = UserThreadLocalUtil.getCurrentName();

            List<AiAuditModel> nameList = aiAuditModelDao.getExitNameAiAuditModelList(aiAuditModel);
            if (CollectionUtils.isNotEmpty(nameList)) {
                return Result.failed("AI审核模型不允许重复");
            }
            if (aiAuditModel.getId() != null) {
                result = aiAuditModelDao.update(aiAuditModel);
            } else {
                result = aiAuditModelDao.insert(aiAuditModel);
            }
            if (CollectionUtils.isNotEmpty(aiAuditModel.getAiAuditModelSceneList())) {
                aiAuditModel.getAiAuditModelSceneList().forEach(kk -> {
                    kk.setAuditId(aiAuditModel.getId());
                });
                aiAuditModelSceneDao.delete(aiAuditModel.getId());
                aiAuditModelSceneDao.insertBatch(aiAuditModel.getAiAuditModelSceneList());
            }
            if (CollectionUtils.isNotEmpty(aiAuditModel.getAiAuditModelPointList())) {
                aiAuditModel.getAiAuditModelPointList().forEach(kk -> {
                    kk.setAuditId(aiAuditModel.getId());
                });
                aiAuditModelPointDao.delete(aiAuditModel.getId());
                aiAuditModelPointDao.insertBatch(aiAuditModel.getAiAuditModelPointList());

            }
            if (CollectionUtils.isNotEmpty(aiAuditModel.getAiAuditModelTechnologyList())) {
                aiAuditModel.getAiAuditModelTechnologyList().forEach(kk -> {
                    kk.setAuditId(aiAuditModel.getId());
                });
                aiAuditModelTechnologyDao.delete(aiAuditModel.getId());
                aiAuditModelTechnologyDao.insertBatch(aiAuditModel.getAiAuditModelTechnologyList());
            }


        } catch (Exception e) {
            log.error("error.impl.insertOrUpdate", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);

    }

    /**
     * 新增
     */
    @Override
    public Result<Integer> insert(AiAuditModel aiAuditModel) {
        int result = 0;
        try {
            result = aiAuditModelDao.insert(aiAuditModel);
        } catch (Exception e) {
            log.error("error.impl.insert", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);

    }

    /**
     * 删除
     */
    @Override
    public Result<Integer> delete(Long id) {
        int result = 0;
        try {
            result = aiAuditModelDao.delete(id);
        } catch (Exception e) {
            log.error("error.impl.delete", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);
    }

    /**
     * 修改
     */
    @Override
    public Result<Integer> update(AiAuditModel aiAuditModel) {
        int result = 0;
        try {
            result = aiAuditModelDao.update(aiAuditModel);
        } catch (Exception e) {
            log.error("error.impl.update", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);
    }

    /**
     * 根据Id查询
     */
    @Override
    public Result<AiAuditModel> getAiAuditModelById(Long id) {
        AiAuditModel result;
        try {
            result = aiAuditModelDao.getAiAuditModelById(id);
            result.setAiAuditModelPointList(aiAuditModelPointDao.getAiAuditModelPointList(new AiAuditModelPoint().setAuditId(id)));
            result.setAiAuditModelSceneList(aiAuditModelSceneDao.getAiAuditModelSceneList( new AiAuditModelScene().setAuditId(id)));
            result.setAiAuditModelTechnologyList(aiAuditModelTechnologyDao.getAiAuditModelTechnologyList(new AiAuditModelTechnology().setAuditId(id)));
        } catch (Exception e) {
            log.error("error.impl.getById", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
        return Result.success(result);
    }

    /**
     * 分页全部查询
     */
    @Override
    public Result<PageList<AiAuditModel>> getAiAuditModelList(AiAuditModel aiAuditModel) {
        PageList<AiAuditModel> pageList;

        PageHelper.startPage(aiAuditModel.getPageNum(), aiAuditModel.getPageSize());
        List<AiAuditModel> list = aiAuditModelDao.getAiAuditModelList(aiAuditModel);
        if(CollectionUtils.isNotEmpty(list)) {
            list.forEach(kk -> {
                kk.setAiAuditModelPointList(aiAuditModelPointDao.getAiAuditModelPointList(new AiAuditModelPoint().setAuditId(kk.getId())));
                kk.setAiAuditModelSceneList(aiAuditModelSceneDao.getAiAuditModelSceneList(new AiAuditModelScene().setAuditId(kk.getId())));
                kk.setAiAuditModelTechnologyList(aiAuditModelTechnologyDao.getAiAuditModelTechnologyList(new AiAuditModelTechnology().setAuditId(kk.getId())));

            });
        }
        pageList = PageListUtils.convertToResult(list);
        return Result.success(pageList);

    }

    /**
     * 分页全部查询
     */
    @Override
    public Result<List<AiTechnologyModel>> getAiTechnologyModelList(AiTechnologyModel aiTechnologyModel) {

        List<AiTechnologyModel> list = aiTechnologyModelDao.getAiTechnologyModelList(aiTechnologyModel);

        return Result.success(list);

    }

}

