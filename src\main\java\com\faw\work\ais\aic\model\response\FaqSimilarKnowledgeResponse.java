package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * FAQ相似知识响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "FAQ相似知识响应对象")
public class FaqSimilarKnowledgeResponse {
    
    @Schema(description = "知识ID")
    private String id;
    
    @Schema(description = "原问题")
    private String question;
    
    @Schema(description = "相似问题")
    private String similarQuestion;
    
    @Schema(description = "答案")
    private String answer;
    
    @Schema(description = "类目ID")
    private String categoryId;
    
    @Schema(description = "类目名称")
    private String categoryName;
    
    @Schema(description = "原知识ID")
    private String knowledgeId;
    
    @Schema(description = "创建人")
    private String createdBy;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "向量化状态（01-未向量化，02-向量化成功 10-向量化失败）")
    private String vectorStatus;
    
    @Schema(description = "图片路径")
    private String imagePath;
    
    @Schema(description = "图片名称")
    private String imageName;
    
    @Schema(description = "图片描述")
    private String imageDescription;
} 