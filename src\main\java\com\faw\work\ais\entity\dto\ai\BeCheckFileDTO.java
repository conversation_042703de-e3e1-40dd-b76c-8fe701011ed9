package com.faw.work.ais.entity.dto.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 被校验文件信息
 */
@Schema(description = "被校验文件信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BeCheckFileDTO {

    @Schema(description = "文件可访问的url")
    private String fileUrl;

    @Schema(description = "文件的ContentType;0表示文档1表示图片 ")
    private String fileContentType;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "文件序号")
    private String fileIndex;

    @Schema(description = "文件描述")
    private String fileDesc;

    @Schema(description = "文件cosKey-给大模型服务发送消息时，获取文件地址用；在上传cos时产生")
    private String cosKey;
}
