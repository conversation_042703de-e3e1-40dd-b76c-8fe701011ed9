package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 同步知识中心文档请求
 *
 * <AUTHOR>
 */
@Data
@Validated
@Schema(description = "同步知识中心文档请求")
public class SyncKnowledgeDocumentsRequest {

    @Schema(description = "知识类型")
    private Integer knowledgeType;

    @Schema(description = "知识子类型")
    private Integer knowledgeSubType;

    @Schema(description = "文档知识库id")
    @NotNull(message = "文档知识库id不能为空")
    private Long ragKnowledgeId;

    @Schema(description = "文档类目id")
    @NotNull(message = "文档类目id不能为空")
    private Long ragCategoryId;

    @Schema(description = "分段最大长度(tokens)")
    @NotNull(message = "分段最大长度(tokens)不能为空")
    private Integer chunkLength;


    @Schema(description = "分段重叠长度(tokens)")
    @NotNull(message = "分段重叠长度(tokens)不能为空")
    private Integer chunkOverlap;


}
