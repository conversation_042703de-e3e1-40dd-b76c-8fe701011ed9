package com.faw.work.ais.controller;

import com.faw.work.ais.common.dto.chat.AiAgentRequest;
import com.faw.work.ais.common.dto.chat.AiChatResponse;
import com.faw.work.ais.service.AiAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

/**
 * AI智能体 控制类
 *
 * <AUTHOR>
 * @since 2025-06-23 11:14
 */
@Schema(description = "AI智能体 控制类")
@RestController
@RequestMapping("/aiAgent")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiAgentController {

    private final AiAgentService aiAgentService;


    @Operation(summary = "获取智能体响应（字符串）", description = "[author:10236535]")
    @PostMapping(value = "/getAgentResponse")
    public String getAgentResponse(@RequestBody AiAgentRequest request) {
        return aiAgentService.getAgentResponse(request);
    }

    @Operation(summary = "获取智能体响应（流式对象）", description = "[author:10236535]")
    @PostMapping(value = "/getAgentFluxResponse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<AiChatResponse> getAgentFluxResponse(@RequestBody AiAgentRequest request) {
        return aiAgentService.getAgentFluxResponse(request);
    }

}
