package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("faq_robot_publish_record")
public class FaqRobotPublishPO {

    private String id;
    private String publisher;
    private LocalDateTime publishTime;
    private String status;
    private Integer version;
    private String robotId;
    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
