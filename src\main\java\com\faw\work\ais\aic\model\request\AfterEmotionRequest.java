package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 售后接待情绪价值模型数据接收请求
 * <AUTHOR>
 */
@Data
@Schema(description = "售后接待情绪价值模型数据接收请求")
public class AfterEmotionRequest {

    @Schema(description = "请求ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请求ID不能为空")
    private String requestId;

    @Schema(description = "用户输入内容",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户输入内容不能为空")
    private String userInput;

    @Schema(description = "语音的腾讯云cos桶url")
    private String audioUrl;

    @Schema(description = "售后阶段 P01-服务接待 P02-需求确认 P03-交车检查 P04-结算送行 ")
    private String phaseCode;
}
