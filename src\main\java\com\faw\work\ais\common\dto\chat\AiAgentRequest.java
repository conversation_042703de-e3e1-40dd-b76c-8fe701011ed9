package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * AI智能体 请求体
 *
 * <AUTHOR>
 * @since 2025-06-23 10:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "AI智能体 请求体")
public class AiAgentRequest {

    /**
     * 场景编码
     */
    @Schema(description = "场景编码")
    private String sceneCode;

    /**
     * 用户信息
     */
    @Schema(description = "用户信息")
    private UserInfo userInfo;

    /**
     * 缓存id
     */
    @Schema(description = "缓存id")
    private String cacheId;

    /**
     * 聊天id
     */
    @Schema(description = "聊天id")
    private String chatId;

    /**
     * 图片链接
     */
    @Schema(description = "图片链接")
    private List<String> imageUrls;

    /**
     * 用户问题
     */
    @Schema(description = "用户问题")
    private String question;

}
