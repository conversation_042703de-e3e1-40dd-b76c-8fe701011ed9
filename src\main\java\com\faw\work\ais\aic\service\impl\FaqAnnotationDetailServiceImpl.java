package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.mapper.faq.FaqAnnotationDetailMapper;
import com.faw.work.ais.aic.mapper.faq.FaqAnnotationTaskMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import com.faw.work.ais.aic.model.request.FaqHitLogDetailQueryRequest;
import com.faw.work.ais.aic.model.response.FaqHitLogDetailResponse;
import com.faw.work.ais.aic.service.FaqAnnotationDetailService;
import com.faw.work.ais.aic.service.FaqAnnotationStatisticsService;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * FAQ标注详情Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqAnnotationDetailServiceImpl implements FaqAnnotationDetailService {

    @Autowired
    private FaqAnnotationDetailMapper faqAnnotationDetailMapper;

    @Autowired
    @Lazy
    private FaqAnnotationStatisticsService faqAnnotationStatisticsService;

    @Autowired
    private FaqAnnotationTaskMapper faqAnnotationTaskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createDetail(FaqAnnotationDetailPO detail) {
        if (ObjectUtil.isNull(detail)) {
            throw new IllegalArgumentException("标注详情信息不能为空");
        }

        detail.setCreatedAt(LocalDateTime.now());
        detail.setUpdatedAt(LocalDateTime.now());

        int result = faqAnnotationDetailMapper.insert(detail);
        if (result > 0) {
            log.info("成功创建标注详情，详情ID: {}", detail.getId());
            return detail.getId();
        } else {
            throw new RuntimeException("创建标注详情失败");
        }
    }

    @Override
    public CompletableFuture<Integer> batchCreateDetails(List<FaqAnnotationDetailPO> details) {
        if (ObjectUtil.isNull(details) || details.isEmpty()) {
            throw new IllegalArgumentException("标注详情列表不能为空");
        }

        LocalDateTime now = LocalDateTime.now();
        for (FaqAnnotationDetailPO detail : details) {
            detail.setCreatedAt(now);
            detail.setUpdatedAt(now);
            if (ObjectUtil.isNull(detail.getIsLocked())) {
                detail.setIsLocked(false);
            }
        }

        int result = faqAnnotationDetailMapper.batchInsert(details);
        log.info("批量创建标注详情，数量: {}", result);
        return CompletableFuture.completedFuture(result);
    }

    @Override
    public FaqAnnotationDetailPO getDetailById(String detailId) {
        if (StrUtil.isBlank(detailId)) {
            throw new IllegalArgumentException("详情ID不能为空");
        }

        return faqAnnotationDetailMapper.selectById(detailId);
    }

    @Override
    public PageInfo<FaqHitLogDetailResponse> getTaskDetails(FaqHitLogDetailQueryRequest request) {
        if (StrUtil.isBlank(request.getAnnotationTaskId())) {
            throw new IllegalArgumentException("标注任务ID不能为空");
        }

        // 使用 PageHelper 启动分页
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        // 查询数据
        List<FaqAnnotationDetailPO> details = faqAnnotationDetailMapper.findByTaskId(
                request.getAnnotationTaskId(),
                request.getAnnotationType(),
                request.getIsLocked()
        );

        // 先创建 PageInfo（这样能获取到正确的分页信息）
        PageInfo<FaqAnnotationDetailPO> pageInfo = new PageInfo<>(details);

        // 转换为响应对象
        List<FaqHitLogDetailResponse> responses = new ArrayList<>();
        for (FaqAnnotationDetailPO detail : details) {
            FaqHitLogDetailResponse response = new FaqHitLogDetailResponse();
            BeanUtils.copyProperties(detail, response);

            // 设置匹配类型
            response.setMatchType(detail.getMatchType());
            response.setDetailId(detail.getId());
            response.setUserQuestion(detail.getUserQuestion());
            response.setMatchedAnswer(detail.getMatchedContent());
            response.setFaqTitle(detail.getFaqTitle());
            response.setMatchScore(detail.getMatchScore());
            response.setAnnotationType(detail.getAnnotationType());
            response.setAnnotationSubtype(detail.getAnnotationSubtype());
            response.setIsLocked(detail.getIsLocked());
            response.setAnnotatorId(detail.getAnnotatorId());
            response.setAnnotatorName(detail.getAnnotatorName());
            response.setAnnotatedAt(detail.getAnnotatedAt());
            response.setCreatedAt(detail.getCreatedAt());
            response.setUpdatedAt(detail.getUpdatedAt());

            responses.add(response);
        }

        // 创建新的 PageInfo 并复制分页信息
        PageInfo<FaqHitLogDetailResponse> result = new PageInfo<>(responses);
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        result.setPageSize(pageInfo.getPageSize());
        result.setPageNum(pageInfo.getPageNum());
        result.setSize(pageInfo.getSize());
        result.setStartRow(pageInfo.getStartRow());
        result.setEndRow(pageInfo.getEndRow());
        result.setIsFirstPage(pageInfo.isIsFirstPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        result.setHasPreviousPage(pageInfo.isHasPreviousPage());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setNavigatePages(pageInfo.getNavigatePages());
        result.setNavigatepageNums(pageInfo.getNavigatepageNums());
        result.setNavigateFirstPage(pageInfo.getNavigateFirstPage());
        result.setNavigateLastPage(pageInfo.getNavigateLastPage());

        return result;
    }



    @Override
    public int countByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqAnnotationDetailMapper.countByTaskId(taskId);
    }

    @Override
    public int countAnnotatedByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqAnnotationDetailMapper.countAnnotatedByTaskId(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markAnnotation(String detailId, String annotationType, String annotationSubtype) {
        if (StrUtil.isBlank(detailId)) {
            throw new IllegalArgumentException("详情ID不能为空");
        }
        if (StrUtil.isBlank(annotationType)) {
            throw new IllegalArgumentException("标注类型不能为空");
        }

        // 获取详情信息以便更新统计
        FaqAnnotationDetailPO detail = faqAnnotationDetailMapper.selectById(detailId);
        if (ObjectUtil.isNull(detail)) {
            throw new IllegalArgumentException("标注详情不存在");
        }

        // 如果之前已经标注过，需要减少旧的统计
        if (StrUtil.isNotBlank(detail.getAnnotationType())) {
            faqAnnotationStatisticsService.decrementStatistics(detail.getTaskId(),
                    detail.getAnnotationType(), detail.getAnnotationSubtype());
        }

        int updateCount = faqAnnotationDetailMapper.updateAnnotation(detailId, annotationType,
                annotationSubtype, UserThreadLocalUtil.getCurrentName(), UserThreadLocalUtil.getRealName());

        if (updateCount > 0) {
            // 增加新的统计
            faqAnnotationStatisticsService.incrementStatistics(detail.getTaskId(),
                    annotationType, annotationSubtype);

        }
        // 更新任务的已标注数量
        Integer nowCount = faqAnnotationDetailMapper.selectMarkedCountByTaskId(detail.getTaskId());
        faqAnnotationTaskMapper.updateAnnotatedCount(detail.getTaskId(), nowCount);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unlockAnnotation(String detailId) {
        if (StrUtil.isBlank(detailId)) {
            throw new IllegalArgumentException("详情ID不能为空");
        }

        // 获取详情信息以便更新统计
        FaqAnnotationDetailPO detail = faqAnnotationDetailMapper.selectById(detailId);
        if (ObjectUtil.isNull(detail)) {
            throw new IllegalArgumentException("标注详情不存在");
        }

        // 如果之前已经标注过，需要减少统计
        if (StrUtil.isNotBlank(detail.getAnnotationType())) {
            faqAnnotationStatisticsService.decrementStatistics(detail.getTaskId(),
                    detail.getAnnotationType(), detail.getAnnotationSubtype());
        }

        int updateCount = faqAnnotationDetailMapper.unlockAnnotation(detailId);
        if (updateCount > 0) {
            log.info("成功解锁标注数据，详情ID: {}", detailId);
            return true;
        }

        return false;
    }

    @Override
    public List<FaqAnnotationDetailPO> getAnnotationDataForExport(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqAnnotationDetailMapper.findAllByTaskId(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        // 由于外键约束，删除任务时会自动删除相关的标注详情
        // 这里主要是为了记录日志
        int count = countByTaskId(taskId);
        log.info("删除任务的标注详情，任务ID: {}, 数量: {}", taskId, count);
        return true;
    }

}
