package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Faq答案实体类
 *
 * <AUTHOR>
 * @since 2025-07-02 13:30
 */
@Data
@Schema(description = "faq答案实体类")
public class FaqAnswerEntity {

    /**
     * 类型
     */
    @Schema(description = "类型")
    private String type;

    /**
     * 思考
     */
    @Schema(description = "思考")
    private String think;

    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;

}
