package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库文档关联分页请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "知识库文档关联分页请求")
public class RagKnowledgeDocumentJoinsPageRequest {

    @Schema(description = "知识库id")
    private Long baseId;

    @Schema(description = "文档id")
    private Long documentId;
    
    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页数量", defaultValue = "10")
    private Integer pageSize = 10;
}