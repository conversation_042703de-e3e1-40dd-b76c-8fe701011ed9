package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 图片解析 请求参数类
 *
 * <AUTHOR>
 * @since 2025-04-22 10:42
 */
@Data
@Schema(description = "图片解析 请求参数类")
public class PhotoAnalysisRequest {

    @Schema(description = "模型")
    private String model;

    @Schema(description = "消息")
    private List<MessageEntity> messages;

    @Schema(description = "温度系数")
    private String temperature;

}
