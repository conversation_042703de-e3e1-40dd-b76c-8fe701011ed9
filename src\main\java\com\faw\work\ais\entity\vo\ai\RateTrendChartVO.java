package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 趋势图返回参数
 */
@Schema(description = "趋势图返回参数")
@Data
public class RateTrendChartVO {

    @Schema(description = "系统id的代码")
    private String systemId;

    @Schema(description = "系统id的名称")
    private String systemName;

    @Schema(description = "费率和日期投票列表")
    List<RateAndDateVO> rateAndDateVOList;
}
