package com.faw.work.ais.model.base;




import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Created by hp on
 */
@Schema(name= "分页列表")
public class PageList<T> implements Serializable {
    @Schema(name= "版本")
    private static final long serialVersionUID = 1L;

    public PageList(long total, List<T> list) {
        this.totalCount = total;
        this.list = list;
    }
    @Schema(name= "总数")
    private long totalCount;
    @Schema(name= "数据集合")
    private List<T> list;

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getList() {
        return list;
    }
    public void setList(List<T> list) {
        this.list = list;
    }
}
