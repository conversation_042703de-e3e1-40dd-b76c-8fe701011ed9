package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 养狗话术知识入库请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "养狗话术知识查询请求对象")
public class FaqDogKnowledgeQueryRequest {

    @NotBlank(message = "机器人ID不能为空")
    @Schema(description = "机器人ID")
    private String robotId;

    @NotNull(message = "知识版本号不能为空")
    @Schema(description =  "知识版本号")
    private Integer knowledgeVersion;

}
