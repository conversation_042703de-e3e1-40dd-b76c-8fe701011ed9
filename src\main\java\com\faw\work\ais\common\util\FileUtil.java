package com.faw.work.ais.common.util;

import com.alibaba.fastjson.JSON;
import com.dcp.common.files.service.FileStorageService;
import com.dcp.common.files.vo.FileInfoVO;
import com.dcp.common.rest.Result;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.dto.ai.CosInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FileUtil {

    private final FileStorageService fileStorageService;


    private static final String CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";


    /**
     * 推送文件到Cos服务器，如果失败重试1次
     *
     * @param cosInfoDTO 推送文件信息
     */
    public FileInfoVO uploadToCos(CosInfoDTO cosInfoDTO) {
        log.info("[FileUtil][uploadToCos][entrance] cosInfoDTO: {}", JSON.toJSONString(cosInfoDTO));
        log.info("[FileUtil][uploadToCos] -----------开始向cos上传文件-------------");

        InputStream inputStream = null;
        // 最大重试次数
        int maxRetries = 1;
        // 当前重试次数
        int retryCount = 0;

        try {
            while (retryCount <= maxRetries) {
                try {
                    String key = cosInfoDTO.getKey();
                    HttpEntity result = HttpUtil.sendGet(cosInfoDTO.getLocalFile(), null, null);
                    inputStream = result.getContent();

                    MockMultipartFile multipartFile = new MockMultipartFile(key, key, CONTENT_TYPE, inputStream);

                    Result<FileInfoVO> fileResult = fileStorageService.huaweiObsUpload(multipartFile, key);
                    log.info("[FileUtil][uploadToCos] fileResult: {}", JSON.toJSONString(fileResult));

                    log.info("[FileUtil][uploadToCos] -----------向cos上传文件结束-------------");
                    return fileResult.getData();
                } catch (Exception ex) {
                    // 增加重试次数
                    retryCount++;
                    log.warn("[FileUtil][uploadToCos] ----上传cos服务失败，正在重试（{}）----", retryCount);
                }
            }
        } catch (Exception ex) {
            log.warn("[FileUtil][uploadToCos] ----上传cos服务出错（最终）----错误信息: ", ex);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.warn("[FileUtil][uploadToCos] ----关闭文件流异常----错误信息: ", e);
            }
        }

        throw new BizException("上传cos服务失败，已达到最大重试次数");
    }

}
