package com.faw.work.ais.entity.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "编码OCR DTO")
@Data
public class OcrDTO {
    @Schema(description = "网址")
    private String url;
    @Schema(description = "编码base64 str")
    private String base64Str;
    @Schema(description = "证书类型")
    private String certType;
    @Schema(description = "FRONT 为行驶证主页正面（有红色印章的一面）， BACK 为行驶证副页正面（有号码号牌的一面）， DOUBLE 为行驶证主页正面和副页正面。 默认值为：FRONT。")
    private String cardSide;//前后 FRONT,BACK
    @Schema(description = "识别的票据类型")
    private List<Integer> types;// 识别的票据类型
}
