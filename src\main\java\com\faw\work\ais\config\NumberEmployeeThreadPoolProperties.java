package com.faw.work.ais.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-09-08
 * @description
 */
@Component
@ConfigurationProperties(prefix = "numberemployee-thread-pool")
@Data
public class NumberEmployeeThreadPoolProperties {
    @Value("${async.executor.numberEmployeeThread.core_pool_size}")
    private int corePoolSize; // 核心线程数

    @Value("${async.executor.numberEmployeeThread.max_pool_size}")
    private int maxPoolSize; // 最大线程数

    @Value("${async.executor.numberEmployeeThread.alive_seconds}")
    private long keepAliveTime; // 空闲线程存活时间

    @Value("${async.executor.numberEmployeeThread.queue_capacity}")
    private int queueCapacity; // 工作队列
}
