package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文档片段表
 *
 * <AUTHOR>
 */
@Data
@TableName("rag_document_split")
@Schema(description = "文档片段实体")
public class RagDocumentSplitPO {

    @TableId(value = "id")
    @Schema(description = "ID")
    private Long id;

    @TableField("tenant_id")
    @Schema(description = "租户ID")
    private Long tenantId;

    @TableField("document_id")
    @Schema(description = "文档ID")
    private Long documentId;

    @TableField("content")
    @Schema(description = "内容")
    private String content;

    @TableField("word_count")
    @Schema(description = "字数")
    private Integer wordCount;

    @TableField("keywords")
    @Schema(description = "关键词")
    private String keywords;

    @TableField("created_by")
    @Schema(description = "创建人")
    private String createdBy;

    @TableField("created_at")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField("updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @TableField("updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;


    @TableField(exist = false)
    @Schema(description = "得分")
    private Float score;
} 