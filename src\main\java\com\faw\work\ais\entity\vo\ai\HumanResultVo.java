package com.faw.work.ais.entity.vo.ai;

import com.faw.work.ais.common.enums.SystemIdEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "人工返回结果接口响应")
@Data
public class HumanResultVo {

    @ApiModelProperty("项目")
    private String sysTemName;

    @ApiModelProperty("系统id")
    private String sysTemId;

    @ApiModelProperty("业务ID")
    private String bizId;

    @ApiModelProperty("一整单单据id")
    private String batchId;

    @ApiModelProperty("单据AI审核状态;0-驳回；1-通过")
    private Integer aiResultFinal;

    @ApiModelProperty("单据AI审核状态;0-驳回；1-通过")
    private String aiResultFinalStr;

    @ApiModelProperty("单据人工审核状态；0-驳回；1-通过")
    private Integer humanCheckResultFinal;

    @ApiModelProperty("单据人工审核状态；0-驳回；1-通过")
    private String humanCheckResultFinalStr;

    @ApiModelProperty("规则名称")
    private String taskName;

    @ApiModelProperty("规则code")
    private String taskType;

    @ApiModelProperty("规则Id")
    private String traceId;

    @ApiModelProperty("规则AI审核状态;0-驳回；1-通过；")
    private Integer aiResultSingle;

    @ApiModelProperty("规则AI审核状态;0-驳回；1-通过；")
    private String aiResultSingleStr;

    @ApiModelProperty("规则人工审核状态；0-驳回；1-通过")
    private Integer humanCheckResultSingle;

    @ApiModelProperty("规则人工审核状态；0-驳回；1-通过")
    private String humanCheckResultSingleStr;

    @ApiModelProperty("AI审核原因")
    private String aiExplain;

    @ApiModelProperty("人工审核原因")
    private String humanRefuseReason;

    @ApiModelProperty("AI审核时间")
    private String aiResultTime;

    @ApiModelProperty("人工审核时间")
    private String humanCheckTime;

    public String getAiResultFinalStr() {
        if (this.aiResultFinal != null) {
            return this.aiResultFinal == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getHumanCheckResultFinalStr() {
        if (this.humanCheckResultFinal != null) {
            return this.humanCheckResultFinal == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getAiResultSingleStr() {
        if (this.aiResultSingle != null) {
            return this.aiResultSingle == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getHumanCheckResultSingleStr() {
        if (this.humanCheckResultSingle != null) {
            return this.humanCheckResultSingle == 0 ? "驳回" : "通过";
        }
        return "";
    }

    public String getSysTemName() {
        return SystemIdEnum.getValue(this.sysTemId);
    }

}
