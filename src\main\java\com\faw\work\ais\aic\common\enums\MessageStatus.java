package com.faw.work.ais.aic.common.enums;

/**
 * 消息状态枚举类
 * 定义了消息在处理过程中的不同状态。
 * <AUTHOR>
 */
public enum MessageStatus {
    /**
     * 消息未被处理。
     */
    UNPROCESSED(0),
    /**
     * 消息正在被处理。
     */
    PROCESSING(1),
    /**
     * 消息已被成功处理。
     */
    PROCESSED(2),

    /**
     * 消息处理失败。
     */
    FAILED(10);

    private final int code;

    /**
     * 构造函数，用于初始化消息状态的编码。
     * @param code 消息状态的编码值。
     */
    MessageStatus(int code) {
        this.code = code;
    }

    /**
     * 获取消息状态的编码值。
     * @return 消息状态的编码值。
     */
    public int getCode() {
        return code;
    }
}
