package com.faw.work.ais.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 提示词生成入参
 */
@Schema(description = "提示词生成入参")
@Data
public class OpsPromptGenerationDto{
    @Schema(description = "策略任务ID")
    private String actionId;

    @Schema(description = "话术模型版本")
    private Integer modelVersion;

    @Schema(description = "通话文本")
    private String callContent;

    @Schema(description = "是否有效")
    private String valid;

    @Schema(description = "是否排程")
    private String schedule;

    @Schema(description = "提示词生成话术模型推荐信息(话术属性标签及内容)")
    private List<OpsPromptRecommendDto> recommendList;

    @Schema(description = "单条评分")
    private BigDecimal recordScore;

    @Schema(description = "优质话术提取")
    private String highQualitySpeech;
}