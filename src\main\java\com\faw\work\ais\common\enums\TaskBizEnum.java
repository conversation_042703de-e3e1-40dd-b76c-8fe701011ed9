package com.faw.work.ais.common.enums;

public enum TaskBizEnum {
    COMPILATION_INPUT(0, "合辑录入"),
    COMPILATION_ACTIVITY_COLLECTION(1, "合辑活动征集"),
    ACTIVITY_PROJECT_COLLECTION(2, "活动项目征集"),
    COMPILATION_INTERGRATION(3, "合辑整合"),
    ACTIVITY_DESIGN(4, "活动设计");

    TaskBizEnum(Integer code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
