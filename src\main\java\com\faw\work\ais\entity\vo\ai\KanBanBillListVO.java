package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 看板列表-单据
 */
@Data
public class KanBanBillListVO {

    @Schema(description = "项目" )
    private String sysTemName;

    @Schema(description = "系统id" )
    private String sysTemId;

    @Schema(description = "业务ID" )
    private String bizId;

    @Schema(description = "一整单单据id" )
    private String batchId;

    @Schema(description = "单据AI审核状态;0-驳回；1-通过" )
    private String aiCheckStatus;

    @Schema(description = "单据人工审核状态；0-驳回；1-通过" )
    private String humanCheckStatus;

    @Schema(description = "看板规则明细列" )
    List<KanBanRuleListVO> ruleListVOList;

}
