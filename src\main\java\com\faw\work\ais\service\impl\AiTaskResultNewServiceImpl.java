package com.faw.work.ais.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.dcp.common.rest.Result;
import com.faw.work.ais.common.enums.ErrorMsgEnum;
import com.faw.work.ais.mapper.ais.AiTaskResultNewDao;
import com.faw.work.ais.mapper.ais.FileInfoNewDao;
import com.faw.work.ais.mapper.ais.FileMapper;
import com.faw.work.ais.model.AiTaskResultNew;
import com.faw.work.ais.model.File;
import com.faw.work.ais.model.FileInfoNew;
import com.faw.work.ais.service.AiTaskResultNewService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;


/**
* AI任务结果记录表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 11:20:13
*/
@Slf4j
@Service
public class AiTaskResultNewServiceImpl implements AiTaskResultNewService {

	@Resource
	private AiTaskResultNewDao aiTaskResultNewDao;
    @Resource
    private FileInfoNewDao fileInfoNewDao;
    @Resource
    private FileMapper fileMapper;
	/**
    * 新增或修改
    */
	@Override
	public Result<Integer> insertOrUpdate(AiTaskResultNew req) {
    Result<Integer> result ;
      try {
          if(StringUtils.isEmpty(req.getStartTime())){
              return Result.failed("开始时间不能为空");
          }
          if(StringUtils.isEmpty(req.getEndTime())){
              return Result.failed("结束时间不能为空");
          }
          if(req.getTaskStatus()!=null) {
              //删除上次执行数据
              List<AiTaskResultNew> thisList = aiTaskResultNewDao.getAiTaskResultThisList(req);
              if (CollectionUtils.isNotEmpty(thisList)) {
                  List<List<AiTaskResultNew>> thisPartition = Lists.partition(thisList, 1000);
                  thisPartition.forEach(k -> {
                      List<Long> resIds = k.stream().map(kk -> kk.getId()).collect(Collectors.toList());
                      List<FileInfoNew> fileInfoNewList = fileInfoNewDao.getFileInfoNewListByResultIds(resIds);
                      if (CollectionUtils.isNotEmpty(fileInfoNewList)) {
                          fileInfoNewDao.deleteByIds(fileInfoNewList.stream().map(FileInfoNew::getId).collect(Collectors.toList()));
                      }
                      aiTaskResultNewDao.deleteByIds(resIds);
                  });
                  thisList = null;
              }
          }
          List<AiTaskResultNew> oldList = aiTaskResultNewDao.getAiTaskResultOldList(req);
          //插入新数据
          List<List<AiTaskResultNew>> partition = Lists.partition(oldList, 1000);
          partition.forEach(k->{
               k.stream().forEach(k1->{
                   String str = k1.getAiResultStr();
                   if(StringUtils.isNotEmpty(str)&&"true".equals(str)){
                       k1.setAiResult(1);
                   }else {
                       k1.setAiResult(0);
                   }
               });
              aiTaskResultNewDao.insertBatchByCreateTime(k);
              List<FileInfoNew> filedInfoNewList = new ArrayList<>();
              for(int i= 0;i<k.size();i++){
                  AiTaskResultNew aiTaskResultNew = k.get(i);
                  String fieldUrls =  aiTaskResultNew.getFileRawList();
                  String fieldIds = aiTaskResultNew.getFileId();
                  String contentType = aiTaskResultNew.getContentType();
                  if(StringUtils.isNotEmpty(fieldUrls) && StringUtils.isNotEmpty(fieldIds)) {
                      int urlSize  =0;
                      int idSize  =0;
                      int contSize  =0;
                      String[] filedUrlList = fieldUrls.split(",");
                      urlSize = filedUrlList.length;
                      String[] filedIdList = fieldIds.split(",");
                      idSize = filedIdList.length;
                      String[] contList = contentType.split(",");
                      contSize = contList.length;

                      int size = urlSize<=idSize?urlSize:idSize;
                      size = size<=contSize?size:contSize;
                      for(int j=0;j<size;j++){
                          String fileId = filedIdList[j];
                          String fileUrl = filedUrlList[j];
                          String contType = contList[j];
                          if(StringUtils.isNotEmpty(fileId)&&
                          StringUtils.isNotEmpty(fileUrl) && StringUtils.isNotEmpty(contType)) {
                              File file = new File();
                              file.setId(fileId);
                              File fileInfo = fileMapper.getFileOldInfo(file);
                              FileInfoNew fileInfoNew = new FileInfoNew();
                              if( fileInfo != null){
                                  fileInfoNew.setFileIndex(fileInfo.getFileIndex());
                              }
                              fileInfoNew.setTraceId(aiTaskResultNew.getTraceId());
                              fileInfoNew.setFileUrl(fileUrl);
                              fileInfoNew.setFileType(Integer.parseInt(contType));
                              fileInfoNew.setFileId(fileId);
                              filedInfoNewList.add(fileInfoNew);
                          }
                      }
                      /*if(CollectionUtils.isNotEmpty(filedInfoNewList)) {
                          fileInfoNewDao.insertBatch(filedInfoNewList);
                      }*/
                  }
              }
              if(CollectionUtils.isNotEmpty(filedInfoNewList)) {
                  fileInfoNewDao.insertBatch(filedInfoNewList);
              }
              filedInfoNewList = null;
          });
          oldList = null;

      } catch (Exception e) {
        log.error("error.impl.insertOrUpdate", e);
         return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
      }
         return Result.success(1);

	}

	/**
    * 新增
    */
	@Override
	public Result<Integer> insert(AiTaskResultNew aiTaskResultNew) {
        int result = 0;
        try {
          result = aiTaskResultNewDao.insert(aiTaskResultNew);
        } catch (Exception e) {
          log.error("error.impl.insert", e);
          return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
          return Result.success(result);

    }


	/**
	* 修改
	*/
	@Override
	public Result<Integer> update(AiTaskResultNew aiTaskResultNew) {
        int result = 0;
        try {
            result = aiTaskResultNewDao.update(aiTaskResultNew);
        } catch (Exception e) {
            log.error("error.impl.update", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
            return Result.success(result);
    }

	/**
	* 根据Id查询
	*/
	@Override
	public Result<AiTaskResultNew> getAiTaskResultNewById(Long id) {
        AiTaskResultNew result;
        try{
            result = aiTaskResultNewDao.getAiTaskResultNewById(id);
        } catch (Exception e) {
            log.error("error.impl.getById", e);
            return Result.failed(ErrorMsgEnum.AGGREGATION_ERROR.getMessage());
        }
            return Result.success(result);
    }

 	/**
    * 分页全部查询
    */
    @Override
    public Result< List<AiTaskResultNew>>  getAiTaskResultNewList(AiTaskResultNew aiTaskResultNew){

			List<AiTaskResultNew> list = aiTaskResultNewDao.getAiTaskResultNewList(aiTaskResultNew);

            return Result.success(list);

	}



}

