<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiTechnologyModelDao">

    <resultMap id="AiTechnologyModel" type="com.faw.work.ais.model.AiTechnologyModel" >
        <result column="id" property="id" />
        <result column="technology_model_code" property="technologyModelCode" />
        <result column="technology_model_name" property="technologyModelName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `technology_model_code`,
        `technology_model_name`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_technology_model (
            `technology_model_code`,
            `technology_model_name`
        )
        VALUES(
            #{aiTechnologyModel.technologyModelCode},
            #{aiTechnologyModel.technologyModelName}
        )
    </insert>

    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM ai_technology_model
        where  id = #{id}

    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE ai_technology_model
         <set>
            <if test="aiTechnologyModel.technologyModelCode != null and aiTechnologyModel.technologyModelCode != '' " >
                technology_model_code = #{aiTechnologyModel.technologyModelCode},
            </if>
            <if test="aiTechnologyModel.technologyModelName != null and aiTechnologyModel.technologyModelName != '' " >
                technology_model_name = #{aiTechnologyModel.technologyModelName},
            </if>
            <if test="aiTechnologyModel.createTime != null and aiTechnologyModel.createTime != '' " >
                create_time = #{aiTechnologyModel.createTime},
            </if>
            <if test="aiTechnologyModel.updateTime != null and aiTechnologyModel.updateTime != '' " >
                update_time = #{aiTechnologyModel.updateTime},
            </if>
         </set>
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </update>


    <select id="getAiTechnologyModelList" resultMap="AiTechnologyModel">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_technology_model
        <where>
          <if test="aiTechnologyModel.id != null and aiTechnologyModel.id != '' " >
              AND  id = #{aiTechnologyModel.id}
          </if>
          <if test="aiTechnologyModel.technologyModelCode != null and aiTechnologyModel.technologyModelCode != '' " >
              AND  technology_model_code = #{aiTechnologyModel.technologyModelCode}
          </if>
          <if test="aiTechnologyModel.technologyModelName != null and aiTechnologyModel.technologyModelName != '' " >
              AND  technology_model_name = #{aiTechnologyModel.technologyModelName}
          </if>


        </where>
     </select>

    <select id="getAiTechnologyModelById" parameterType="java.util.Map" resultMap="AiTechnologyModel">
        SELECT <include refid="Base_Column_List" />
        FROM ai_technology_model
        where id = #{id}

    </select>



</mapper>

