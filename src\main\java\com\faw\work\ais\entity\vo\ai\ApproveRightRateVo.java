package com.faw.work.ais.entity.vo.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "审核结果准确率返回队形")
@Data
public class ApproveRightRateVo {

    /**
     * 分子
     */
    @ApiModelProperty("分子（被除数）")
    private BigDecimal dividend;

    /**
     * 分母
     */
    @ApiModelProperty("分母（除数）")
    private BigDecimal divisor;

    /**
     * 商
     */
    @ApiModelProperty("商")
    private BigDecimal quotient;

}
