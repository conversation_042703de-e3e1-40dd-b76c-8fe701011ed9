<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.FileMapper">
  <resultMap id="BaseResultMap" type="com.faw.work.ais.model.File">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="length" jdbcType="VARCHAR" property="length" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="raw_name" jdbcType="VARCHAR" property="rawName" />
    <result column="file_index" jdbcType="INTEGER" property="fileIndex" />
    <result column="file_desc" jdbcType="VARCHAR" property="fileDesc" />
    <result column="version" jdbcType="VARCHAR" property="version" />
  </resultMap>
  <insert id="insert" parameterType="com.faw.work.ais.model.File">
    insert into file (id, length, md5, create_time, raw_name, file_index, file_desc, version)
    values (#{param.id}, #{param.length}, #{param.md5}, sysdate(), #{param.rawName}, #{param.fileIndex}, #{param.fileDesc}, #{param.version}  )
  </insert>
  <update id="update" parameterType="com.faw.work.ais.model.File">

  </update>

  <select id="ifKeyExists" resultType="java.lang.Integer">
    SELECT
        COUNT(1)
    FROM file t
    WHERE t.md5 = #{key}
  </select>

  <select id="getFileInfo" resultType="com.faw.work.ais.model.File">
    select
      id,
      length,
      md5,
      create_time createTime,
      raw_name rawName
    FROM file
    <where>
      <if test="#{param.md5 != null and param.md5 != ''}">
        AND md5 = #{param.md5}
      </if>
    </where>
  </select>
  <select id="getFileOldInfo" resultType="com.faw.work.ais.model.File">
    select
     id as  `file_id`,
      `file_index`
    FROM file
    where
         id = #{param.id}

  </select>


</mapper>