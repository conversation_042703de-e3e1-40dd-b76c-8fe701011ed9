package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.AiTechnologyModel;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* ai技术模型配置表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 11:02:28
*/
@InterceptorIgnore(tenantLine = "true")
public interface AiTechnologyModelDao {

    /**
    * 新增
    */
    public int insert(@Param("aiTechnologyModel") AiTechnologyModel aiTechnologyModel);

    /**
    * 删除
    */
    public int delete(@Param("id") Long id);

    /**
    * 修改
    */
    public int update(@Param("aiTechnologyModel") AiTechnologyModel aiTechnologyModel);


    /**
    * 根据id查询 getAiTechnologyModelById
    */
    public AiTechnologyModel getAiTechnologyModelById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<AiTechnologyModel> getAiTechnologyModelList(@Param("aiTechnologyModel")AiTechnologyModel aiTechnologyModel);



}

