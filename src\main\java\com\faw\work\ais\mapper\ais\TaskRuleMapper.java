package com.faw.work.ais.mapper.ais;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.entity.vo.ai.TaskRuleVO;
import com.faw.work.ais.model.TaskRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.File;
import java.util.List;

@Mapper
public interface TaskRuleMapper extends BaseMapper<TaskRule> {

    int insert (@Param("param") File file);

    int update (@Param("param") File file);

    TaskRule getLastPromptBySystemId(@Param("systemId") String systemId, @Param("taskType") String taskType);

    String getCallBackUrl(@Param("systemId") String systemId);

    List<TaskRuleVO> getTaskRule();

    List<TaskRuleVO> getTaskRuleBySystemId(@Param("systemId") String systemId);
}
