package com.faw.work.ais.common.util;


import com.alibaba.dashscope.app.*;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;

public class DashScopeUtils {

    /**
     * 调用DashScope应用接口
     *
     * @param apiKey   API Key，建议从环境变量获取
     * @param appId    应用ID
     * @param query   提示文本
     * @return 返回调用结果
     * @throws ApiException            API异常
     * @throws NoApiKeyException       无API Key异常
     * @throws InputRequiredException  输入缺失异常
     */
    public static String callDashScopeApp(String workspace, String apiKey, String appId, String query)
            throws ApiException, NoApiKeyException, InputRequiredException {
        ApplicationParam param = ApplicationParam.builder()
                .workspace(workspace)
                .apiKey(apiKey)
                .appId(appId)
                .prompt(query)
                .build();

        Application application = new Application();
        ApplicationResult result = application.call(param);

        return result.getOutput().getText();
    }

    /**
     * 主方法示例
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            String workspace = "llm-wipob7d3ruuuhsx3"; // 替换为你的工作空间
            String apiKey = "sk-84d50a726d4f4253b04dcd47f35a4b08"; // 从环境变量获取API Key
            String appId = "9bda8937d51e43229dcccc41e0a993ce"; // 替换为你的应用ID
            String prompt = "天公05咋样啊哈哈哈我想试驾一下这个车你看看啥时候有时间好嘛"; // 替换为你的提示文本

            String response = callDashScopeApp(workspace,apiKey, appId, prompt);
            System.out.println("Response: " + response);
        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            System.err.println("Error: " + e.getMessage());
            System.out.println("请参考文档：https://help.aliyun.com/zh/model-studio/developer-reference/error-code");
        }
    }
}

