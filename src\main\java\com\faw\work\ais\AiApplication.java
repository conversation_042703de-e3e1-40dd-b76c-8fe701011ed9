package com.faw.work.ais;

import com.dcp.common.CommonConfig;
import com.taobao.api.internal.util.TaobaoLogger;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 服务启动类
 *
 * <AUTHOR>
 * @since 2024-04-02 15:42
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, CommonConfig.class})
@EnableFeignClients
@EnableAsync
@Slf4j
@ComponentScan({"com.dcp.common.*", "com.xxl.job.core", "com.faw.*"})
@MapperScan("com.faw.work.ais.**.mapper.**")
@EnableScheduling
public class AiApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(AiApplication.class, args);
        printContext(context);
        // 关闭钉钉日志
        TaobaoLogger.setNeedEnableLogger(false);
    }

    private static void printContext(ConfigurableApplicationContext context) {
        String address;

        try {
            address = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            address = "";
        }

        String str = """

                --------------------------------------------------------
                Application '{}' is running！ Access URLs:
                \t\
                Local:\t\t{}://localhost:{}
                \t\
                External:\t{}://{}:{}
                \t\
                Profile(s):\t{}\

                --------------------------------------------------------""";

        Environment env = context.getEnvironment();
        String name = env.getProperty("spring.application.name");
        String protocol = env.getProperty("server.ssl.key-store") != null ? "https" : "http";
        String port = env.getProperty("server.port");
        String[] profiles = env.getActiveProfiles();

        log.info(str, name, protocol, port, protocol, address, port, profiles);
    }

}
