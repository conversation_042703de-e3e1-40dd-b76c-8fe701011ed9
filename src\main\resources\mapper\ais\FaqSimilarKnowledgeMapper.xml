<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqSimilarKnowledgeMapper">
    

    <delete id="deleteByOriginalId">
        DELETE FROM faq_similar_knowledge WHERE knowledge_id = #{originalId}
    </delete>
    <delete id="deleteByKnowledgeCategoryId">
        DELETE FROM faq_similar_knowledge WHERE category_id = #{categoryId}
    </delete>

    <!-- 根据原问题ID查询相似问列表 -->
    <select id="selectByOriginalId" resultType="com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO">
        SELECT
        *
        FROM faq_similar_knowledge
        WHERE knowledge_id = #{originalId}
    </select>
    
    <!-- 根据类目ID查询相似问列表 -->
    <select id="selectByCategoryIds" resultType="com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO">
        SELECT
        *
        FROM faq_similar_knowledge
        WHERE category_id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>
    <select id="selectByOriginalIds" resultType="com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO">
        SELECT
        *
        FROM faq_similar_knowledge
        WHERE knowledge_id IN
        <foreach collection="originalKnowledgeIds" item="originalKnowledgeId" open="(" separator="," close=")">
            #{originalKnowledgeId}
        </foreach>
    </select>
    <select id="selectProdByOriginalId" resultType="com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO">
        SELECT
            *
        FROM faq_similar_knowledge_prod
        WHERE knowledge_id = #{originalId}
    </select>
    <select id="selectIdAndQuestionByIds" resultType="com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO">
         SELECT
            id,
        similar_question
        FROM faq_similar_knowledge
        WHERE knowledge_id IN
        <foreach collection="originalKnowledgeIds" item="originalKnowledgeId" open="(" separator="," close=")">
            #{originalKnowledgeId}
        </foreach>
    </select>
</mapper> 