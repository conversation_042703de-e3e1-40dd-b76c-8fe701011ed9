package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 图片解析 请求参数类
 *
 * <AUTHOR>
 * @since 2025-05-19 14:38
 */
@Schema(description = "大模型请求参数")
@Data
public class AnalysisRequest {

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "消息")
    private List<MessageEntity> messages;

    @Schema(description = "温度")
    private Float temperature;

    @Schema(description = "是否开启思考模式")
    private Boolean enable_thinking = false;

}
