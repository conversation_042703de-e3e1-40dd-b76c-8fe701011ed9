package com.faw.work.ais.aic.common.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DateUtils {

    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取当前时间的字符串，使用默认格式 (yyyy-MM-dd HH:mm:ss)。
     *
     * @return 当前时间的字符串
     */
    public static String getCurrentDateTimeString() {
        return getCurrentDateTimeString(DEFAULT_DATE_FORMAT);
    }

    /**
     * 获取当前时间的字符串，使用指定的格式。
     *
     * @param format 时间格式，例如 "yyyy-MM-dd HH:mm:ss", "yyyyMMdd", "HH:mm:ss" 等
     * @return 当前时间的字符串
     */
    public static String getCurrentDateTimeString(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date());
    }

    /**
     * 获取当前日期的字符串，使用默认格式 (yyyy-MM-dd)。
     *
     * @return 当前日期的字符串
     */
    public static String getCurrentDateString() {
        return getCurrentDateTimeString("yyyy-MM-dd");
    }

    /**
     * 获取当前时间的字符串，使用默认格式 (HH:mm:ss)。
     *
     * @return 当前时间的字符串
     */
    public static String getCurrentTimeString() {
        return getCurrentDateTimeString("HH:mm:ss");
    }

    /**
     * 将 Date 对象格式化为字符串，使用指定的格式。
     *
     * @param date   Date 对象
     * @param format 时间格式
     * @return 格式化后的字符串
     */
    public static String formatDate(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 将 Date 对象格式化为字符串，使用默认格式 (yyyy-MM-dd HH:mm:ss)。
     *
     * @param date Date 对象
     * @return 格式化后的字符串
     */
    public static String formatDate(Date date) {
        return formatDate(date, DEFAULT_DATE_FORMAT);
    }

    /**
     * 将毫秒时间转换为分秒格式
     * @param millisStr 毫秒时间字符串
     * @return 格式化后的时间字符串，如"2分30秒"
     */
    public static String convertMillisToMinuteSecond(String millisStr) {
        try {
            long millis = Long.parseLong(millisStr);
            long totalSeconds = millis / 1000;
            long minutes = totalSeconds / 60;
            long seconds = totalSeconds % 60;

            if (minutes > 0) {
                return minutes + "分" + seconds + "秒";
            } else {
                return seconds + "秒";
            }
        } catch (NumberFormatException e) {
            // 如果解析失败，返回原始字符串
            return millisStr;
        }
    }


    // 可以添加更多日期相关的实用方法，例如：
    // - 字符串转 Date
    // - 计算日期差
    // - 获取指定日期的年、月、日等
    // - 获取本周、本月、本年的开始/结束日期
    // - ...
}

