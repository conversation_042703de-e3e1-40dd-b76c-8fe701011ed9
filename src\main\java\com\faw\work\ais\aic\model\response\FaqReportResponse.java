package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * FAQ运营报表响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ运营报表响应")
public class FaqReportResponse {

    @Schema(description = "知识总数: 在查询日期区间的最后一天有效的知识总数")
    private Long totalKnowledgeCount;

    @Schema(description = "有效知识命中占比: (命中有效知识数 / 知识总数)")
    private String effectiveKnowledgeHitRate;

    @Schema(description = "知识命中占比: (直接命中知识的对话轮次 / 总对话轮次)")
    private String knowledgeHitSessionRate;

    @Schema(description = "热门榜-TOP10直接命中")
    private List<FaqHitRankItem> hotList;

    @Schema(description = "冷门榜-无访问知识")
    private List<FaqKnowledgePO> coldList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "FAQ命中排行榜项目")
    public static class FaqHitRankItem {
        @Schema(description = "知识名称（FAQ题目）")
        private String knowledgeName;

        @Schema(description = "命中次数")
        private Long hitCount;
    }
} 