package com.faw.work.ais.model;

import com.faw.work.ais.model.base.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * ai场景配置表
 * Created  by Mr.hp
 * DateTime on 2025-05-08 10:57:37
 *
 * <AUTHOR> Mr.hp
 * @ApiModel(value = "AiScene", description = "ai场景配置表")
 */
@Data
@NoArgsConstructor
@Schema(description = "人工智能ai场景配置表")
public class AiScene extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     *
     * @ApiModelProperty(value = "主键id")
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 场景编码
     *
     * @ApiModelProperty(value = "场景编码")
     */
    @Schema(description = "场景编码")
    private String sceneCode;

    /**
     * 场景名称
     *
     * @ApiModelProperty(value = "场景名称")
     */
    @Schema(description = "场景名称")
    private String sceneName;

    /**
     * 流程编码_l3
     *
     * @ApiModelProperty(value = "流程编码_l3")
     */
    @Schema(description = "流程编码_l3")
    private String l3ProcessCode;

    /**
     * 流程名称_l3
     *
     * @ApiModelProperty(value = "流程名称_l3")
     */
    @Schema(description = "流程名称_l3")
    private String l3ProcessName;

    /**
     * 流程编码_l4
     *
     * @ApiModelProperty(value = "流程编码_l4")
     */
    @Schema(description = "流程编码_l4")
    private String l4ProcessCode;

    /**
     * 流程名称_l4
     *
     * @ApiModelProperty(value = "流程名称_l4")
     */
    @Schema(description = "流程名称_l4")
    private String l4ProcessName;

    /**
     * 流程编码_l5
     *
     * @ApiModelProperty(value = "流程编码_l5")
     */
    @Schema(description = "流程编码_l5")
    private String l5ProcessCode;

    /**
     * 流程名称_l5
     *
     * @ApiModelProperty(value = "流程名称_l5")
     */
    @Schema(description = "流程名称_l5")
    private String l5ProcessName;

    /**
     * 业务单元编码
     *
     * @ApiModelProperty(value = "业务单元编码")
     */
    @Schema(description = "业务单元编码")
    private String bizUnitCode;

    /**
     * 业务单元名称
     *
     * @ApiModelProperty(value = "业务单元名称")
     */
    @Schema(description = "业务单元名称")
    private String bizUnitName;

    /**
     * 回调url
     *
     * @ApiModelProperty(value = "回调url")
     */
    @Schema(description = "回调url")
    private String callBackUrl;

    /**
     * 驳回转人工次数
     *
     * @ApiModelProperty(value = "驳回转人工次数")
     */
    @Schema(description = "驳回转人工次数")
    private Integer rejectNum;

    /**
     * 对比结果 1人工 0 ai
     *
     * @ApiModelProperty(value = "对比结果 1人工 0 ai")
     */
    @Schema(description = "对比结果 1人工 0 ai")
    private Integer comparisonResults;

    /**
     * 创建时间
     *
     * @ApiModelProperty(value = "创建时间")
     */
    @Schema(description = "创建时间")
    private String createTime;

    /**
     * 创建人编码
     *
     * @ApiModelProperty(value = "创建人编码")
     */
    @Schema(description = "创建人编码")
    private String createUserCode;

    /**
     * 创建人姓名
     *
     * @ApiModelProperty(value = "创建人姓名")
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新时间
     *
     * @ApiModelProperty(value = "更新时间")
     */
    @Schema(description = "更新时间")
    private String updateTime;

    /**
     * 更新人编码
     *
     * @ApiModelProperty(value = "更新人编码")
     */
    @Schema(description = "更新人编码")
    private String updateUserCode;

    /**
     * 更新人姓名
     *
     * @ApiModelProperty(value = "更新人姓名")
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;

    /**
     * 场景配置流程列表
     */
    @Schema(description = "场景配置流程列表")
    private List<AiScene> aiSceneList;


}
