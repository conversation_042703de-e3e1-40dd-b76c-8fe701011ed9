package com.faw.work.ais.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.AiTaskDTO;
import com.faw.work.ais.entity.dto.ai.AiTaskQueryDTO;
import com.faw.work.ais.entity.dto.python.AiTaskFormPythonDTO;
import com.faw.work.ais.entity.vo.ai.AiTaskResultVO;
import com.faw.work.ais.service.AiTaskAsyncService;
import com.faw.work.ais.service.AiTaskResultService;
import com.faw.work.ais.service.AiTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Schema(description = "AI 任务控制器")
@Slf4j
@RestController("AiTaskController")
@RequestMapping("/v1")
public class AiTaskController {

    @Autowired
    AiTaskService taskService;

    @Autowired
    AiTaskResultService aiTaskResultService;

    @Autowired
    AiTaskAsyncService aiTaskAsyncService;

    @Operation(summary = "异步Ai算法识别", description = "[author:10236535]")
    @PostMapping(value = "/asyncAiTask")
    public Response asyncAiTask(@RequestBody  @Valid AiTaskDTO aiTaskDTO) {
        return taskService.asyncAiTask(aiTaskDTO);
    }

    @Operation(summary = "异步Ai算法识别-批量-推送mq同步", description = "[author:10236535]")
    @PostMapping(value = "/asyncAiTaskBatch")
    public Response asyncAiTask(@RequestBody  @Valid List<AiTaskDTO> aiTaskDTO) {
        return taskService.asyncAiTaskBatch(aiTaskDTO);
    }

    @Operation(summary = "异步Ai算法识别-批量-推送mq异步", description = "[author:10236535]")
    @PostMapping(value = "/asyncPushToMq")
    public Response asyncPushToMq(@RequestBody  @Valid List<AiTaskDTO> aiTaskDTO) {
        return aiTaskAsyncService.asyncAiTaskBatchAsync(aiTaskDTO);
    }

    /**
     * ai返回结果查询
     */
    @Operation(summary = "智能返回结果查询", description = "[author:10236535]")
    @PostMapping("/query")
    public Response<IPage<AiTaskResultVO>> processManageDynamicTableWithPage(@RequestBody AiTaskQueryDTO aiTaskQueryDTO) {
        return Response.success(aiTaskResultService.getAiResult(aiTaskQueryDTO));
    }

    /**
     * 保存Python处理的Ai结果
     */
    @Operation(summary = "保存Python处理的Ai结果", description = "[author:10236535]")
    @PostMapping("/saveAiTaskFromPython")
    public Response saveAiTaskFromPython(@RequestBody AiTaskFormPythonDTO aiTaskFormPythonDTO) {
        return Response.success(taskService.saveAiTaskResultFromPython(aiTaskFormPythonDTO));
    }

}
