package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.util.HttpUtil;
import com.faw.work.ais.common.util.InputStreamMultipartFile;
import com.faw.work.ais.entity.dto.ai.OcrDTO;
import com.faw.work.ais.feign.IworkProdOpenApiFeignClient;
import com.faw.work.ais.service.OcrService;
import com.qcloud.cos.utils.StringUtils;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Base64;

@Slf4j
@Service
public class OcrServiceImpl implements OcrService {


    @Value("${tencent.ocr.secretId}")
    private String secretId;

    @Value("${tencent.ocr.secretKey}")
    private String secretKey;


    @Autowired
    private IworkProdOpenApiFeignClient iworkProdOpenApiFeignClient;

    @Override
    public String plateLicense(OcrDTO ocrDTO) {
        try {
            JSONObject param = paramHandle(ocrDTO);
            log.info("OCR识别车牌开始：" + param.toJSONString());
            Credential cred = new Credential(secretId, secretKey); // 创建实例对象
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com"); // 设置访问域名
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile); // 设置大区
            String params = param.toJSONString(); // 设置参数
            LicensePlateOCRRequest request = LicensePlateOCRRequest.fromJsonString(params, LicensePlateOCRRequest.class);
            LicensePlateOCRResponse resp = client.LicensePlateOCR(request);
            String response = LicensePlateOCRResponse.toJsonString(resp);
            log.info("OCR识别车牌结束：" + response);
            return response;
        } catch (Exception e) {
            log.error("OCR识别车牌异常：" + e.getMessage(), e);
            JSONObject res = new JSONObject();
            JSONObject error = new JSONObject();
            error.put("Message", e.getMessage());
            res.put("Error", error);
            return res.toJSONString();
        }
    }

    @Override
    public String vinLicense(OcrDTO ocrDTO) {
        try {
            JSONObject param = paramHandle(ocrDTO);
            log.info("OCR识别车辆vin码开始：" + param.toJSONString());
            Credential cred = new Credential(secretId, secretKey); // 创建实例对象
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com"); // 设置访问域名
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile); // 设置大区
            String params = param.toJSONString(); // 设置参数
            VinOCRRequest request = VinOCRRequest.fromJsonString(params, VinOCRRequest.class);
            VinOCRResponse vinOCRResponse = client.VinOCR(request);
            String response = VinOCRResponse.toJsonString(vinOCRResponse);
            log.info("OCR识别车辆vin码结束：" + response);
            return response;
        } catch (Exception e) {
            log.error("OCR识别车辆vin码异常：" + e.getMessage(), e);
            JSONObject res = new JSONObject();
            JSONObject error = new JSONObject();
            error.put("Message", e.getMessage());
            res.put("Error", error);
            return res.toJSONString();
        }
    }

    @Override
    public Response<TextVehicleFront> driverLicenseFront(OcrDTO ocrDTO) {
        try {
            JSONObject param = paramHandle(ocrDTO);
            log.info("OCR识别行驶证开始：" + param.toJSONString());
            Credential cred = new Credential(secretId, secretKey); // 创建实例对象
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com"); // 设置访问域名
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile); // 设置大区
            String params = param.toJSONString(); // 设置参数
            VehicleLicenseOCRRequest request = VehicleLicenseOCRRequest.fromJsonString(params, VehicleLicenseOCRRequest.class);
            VehicleLicenseOCRResponse vehicleLicenseOCRResponse = client.VehicleLicenseOCR(request);
            log.info("OCR识别行驶证结束：" + JSON.toJSONString(vehicleLicenseOCRResponse));
            // 正 反
            TextVehicleFront frontInfo = vehicleLicenseOCRResponse.getFrontInfo();
            if(frontInfo == null){
                return Response.fail("图片质量不合格");
            }
            return Response.success(frontInfo);
        } catch (Exception e) {
            log.error("OCR识别行驶证图片异常：" + e.getMessage(), e);
            return Response.fail("图片不是行驶证图片");
        }
    }


    @Override
    public Response<TextVehicleFront> driverLicenseFrontForCut(OcrDTO ocrDTO, feign.Response responseEntity, Boolean cutFlag) {
        try {
            JSONObject param = paramHandleForCut(ocrDTO, responseEntity, cutFlag);
            log.info("OCR识别行驶证开始：" + param.toJSONString());
            Credential cred = new Credential(secretId, secretKey); // 创建实例对象
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com"); // 设置访问域名
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile); // 设置大区
            String params = param.toJSONString(); // 设置参数
            VehicleLicenseOCRRequest request = VehicleLicenseOCRRequest.fromJsonString(params, VehicleLicenseOCRRequest.class);
            VehicleLicenseOCRResponse vehicleLicenseOCRResponse = client.VehicleLicenseOCR(request);
            log.info("OCR识别行驶证结束：" + JSON.toJSONString(vehicleLicenseOCRResponse));
            // 正 反
            TextVehicleFront frontInfo = vehicleLicenseOCRResponse.getFrontInfo();
            if(frontInfo == null){
                return Response.fail("图片质量不合格");
            }
            if(cutFlag){
                log.info("-----小模型优化结果为---------" + JSON.toJSONString(frontInfo));
            }
            return Response.success(frontInfo);
        } catch (Exception e) {
            //剪裁图片后重新送检ORC判断
            try {
                if(!cutFlag){
                    HttpEntity result = HttpUtil.sendGet(ocrDTO.getUrl(),null ,null );
                    InputStream content = result.getContent();
                    log.info("---调用小模型剪裁图片开始------");
                    // 通过开放API调用物理机
                    // 将 InputStream 转换为 MultipartFile
                    MultipartFile uploadFile = new InputStreamMultipartFile("uploadFile", "filename.txt", content);
                    feign.Response responseEntity1 = iworkProdOpenApiFeignClient.frontCut(uploadFile);
                    log.info("-----调用小模型剪裁图片结束------");
                    content.close();
                    return driverLicenseFrontForCut(ocrDTO,responseEntity1, true);
                }else {
                    return Response.fail("剪裁后送检后的图片不是行驶证图片");
                }
            }catch (IOException ex){
                log.error("剪裁图片异常：" + ex.getMessage(), ex);
                return Response.fail("驶证图片剪裁图片异常");
            }catch (Exception ex2){
                log.error("剪裁图片异常2：" + ex2.getMessage(), ex2);
                return Response.fail("驶证图片剪裁图片异常");
            }
        }
    }

    @Override
    public Response<TextVehicleBack> driverLicenseBack(OcrDTO ocrDTO) {
        try {
            JSONObject param = paramHandle(ocrDTO);
            log.info("OCR识别行驶证开始：" + param.toJSONString());
            Credential cred = new Credential(secretId, secretKey); // 创建实例对象
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com"); // 设置访问域名
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile); // 设置大区
            String params = param.toJSONString(); // 设置参数
            VehicleLicenseOCRRequest request = VehicleLicenseOCRRequest.fromJsonString(params, VehicleLicenseOCRRequest.class);
            VehicleLicenseOCRResponse vehicleLicenseOCRResponse = client.VehicleLicenseOCR(request);
            log.info("OCR识别行驶证结束：" + JSON.toJSONString(vehicleLicenseOCRResponse));
            // 正 反
            TextVehicleBack backInfo = vehicleLicenseOCRResponse.getBackInfo();
            if(backInfo == null){
                return Response.fail("图片质量不合格");
            }
            return Response.success(backInfo);
        } catch (Exception e) {
            log.error("OCR识别行驶证图片异常：" + e.getMessage(), e);
            return Response.fail("图片不是行驶证图片");
        }
    }

    public Response<TextVehicleBack> driverLicenseBackForCut(OcrDTO ocrDTO, feign.Response responseEntity, Boolean cutFlag) {
        try {
            JSONObject param = paramHandleForCut(ocrDTO, responseEntity, cutFlag);
            log.info("OCR识别行驶证开始：" + param.toJSONString());
            Credential cred = new Credential(secretId, secretKey); // 创建实例对象
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com"); // 设置访问域名
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile); // 设置大区
            String params = param.toJSONString(); // 设置参数
            VehicleLicenseOCRRequest request = VehicleLicenseOCRRequest.fromJsonString(params, VehicleLicenseOCRRequest.class);
            VehicleLicenseOCRResponse vehicleLicenseOCRResponse = client.VehicleLicenseOCR(request);
            log.info("OCR识别行驶证结束：" + JSON.toJSONString(vehicleLicenseOCRResponse));
            // 正 反
            TextVehicleBack backInfo = vehicleLicenseOCRResponse.getBackInfo();
            if(backInfo == null){
                return Response.fail("图片质量不合格");
            }
            if(cutFlag){
                log.info("-----小模型优化结果为---------" + JSON.toJSONString(backInfo));
            }
            return Response.success(backInfo);
        } catch (Exception e) {
            // 剪裁图片后再次送检ORC判断
            try {
                if(!cutFlag){
                    HttpEntity result = HttpUtil.sendGet(ocrDTO.getUrl(),null ,null );
                    InputStream content = result.getContent();
                    log.info("---调用小模型剪裁图片开始------");
                    // 将 InputStream 转换为 MultipartFile
                    MultipartFile uploadFile = new InputStreamMultipartFile("file", "filename.txt", content);
                    feign.Response responseEntity1 = iworkProdOpenApiFeignClient.frontCut(uploadFile);
                    log.info("-----调用小模型剪裁图片结束------");
                    content.close();
                    return driverLicenseBackForCut(ocrDTO,responseEntity1, true);
                }else{
                    return Response.fail("剪裁后送检后的图片不是行驶证图片！");
                }
            }catch (IOException ex){
                log.error("剪裁图片异常：" + e.getMessage(), e);
                return Response.fail("驶证图片剪裁图片异常");
            }catch (Exception ex){
                log.error("剪裁图片异常：" + e.getMessage(), e);
                return Response.fail("驶证图片剪裁图片异常");
            }
        }
//            log.error("OCR识别行驶证图片异常：" + e.getMessage(), e);
//            return Response.fail("图片不是行驶证图片");
    }


    @Override
    public Response<MixedInvoiceItem[]> motorVehicleSaleInvoice(OcrDTO ocrDTO) {
        try {
            ocrDTO.setTypes(Arrays.asList((12)));// 购车发票
            JSONObject param = paramHandle(ocrDTO);
//            log.info("OCR识别购车发票开始：" + param.toJSONString());
            Credential cred = new Credential(secretId, secretKey); // 创建实例对象
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com"); // 设置访问域名
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-beijing", clientProfile); // 设置大区
            String params = param.toJSONString(); // 设置参数
            MixedInvoiceOCRRequest request = MixedInvoiceOCRRequest.fromJsonString(params, MixedInvoiceOCRRequest.class);
            MixedInvoiceOCRResponse mixedInvoiceOCRResponse = client.MixedInvoiceOCR(request);
            log.info("OCR识别购车发票结束：" + JSON.toJSONString(mixedInvoiceOCRResponse));
            MixedInvoiceItem[] mixedInvoiceItems = mixedInvoiceOCRResponse.getMixedInvoiceItems();
            return Response.success(mixedInvoiceItems);
        } catch (Exception e) {
            log.error("OCR识别购车发票异常：" + e.getMessage(), e);
            return Response.fail("图片不是购车发票");
        }
    }

    private JSONObject paramHandleForCut(OcrDTO ocrDTO, feign.Response responseEntity, Boolean cutFlag) throws IOException {
        JSONObject param = new JSONObject();
        if(!cutFlag){
            if (StringUtils.isNullOrEmpty(ocrDTO.getUrl())) {
                throw new RuntimeException("网址为必传递参数");
            }
            if (!StringUtils.isNullOrEmpty(ocrDTO.getBase64Str())) {
                param.put("ImageBase64", ocrDTO.getBase64Str());
            }else{
                HttpEntity result = HttpUtil.sendGet(ocrDTO.getUrl(),null ,null );
                InputStream content = result.getContent();
                // 将InputStream转换为字节数组
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = content.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, length);
                }
                byte[] bytes = outputStream.toByteArray();

                // 使用Base64工具类进行编码
                String base64String = Base64.getEncoder().encodeToString(bytes);
                param.put("ImageBase64", base64String);
                content.close();
                outputStream.close();
            }
            if (!StringUtils.isNullOrEmpty(ocrDTO.getCardSide())) {
                param.put("CardSide", ocrDTO.getCardSide());
            }
        }else {
            InputStream inputStream = responseEntity.body().asInputStream();
            byte[] bytes = inputStream.readAllBytes();
            inputStream.close();
            String base64String = Base64.getEncoder().encodeToString(bytes);
            param.put("ImageBase64", base64String);

            if (!StringUtils.isNullOrEmpty(ocrDTO.getCardSide())) {
                param.put("CardSide", ocrDTO.getCardSide());
            }
            if (!StringUtils.isNullOrEmpty(ocrDTO.getCardSide())) {
                param.put("CardSide", ocrDTO.getCardSide());
            }
        }
        return param;
    }
    private JSONObject paramHandle(OcrDTO ocrDTO) throws IOException {
        JSONObject param = new JSONObject();
        if (StringUtils.isNullOrEmpty(ocrDTO.getUrl())) {
            throw new RuntimeException("网址为必传递参数");
        }
        if (!StringUtils.isNullOrEmpty(ocrDTO.getBase64Str())) {
            param.put("ImageBase64", ocrDTO.getBase64Str());
        }else{
            HttpEntity result = HttpUtil.sendGet(ocrDTO.getUrl(),null ,null );
            InputStream content = result.getContent();
            // 将InputStream转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = content.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            byte[] bytes = outputStream.toByteArray();

            // 使用Base64工具类进行编码
            String base64String = Base64.getEncoder().encodeToString(bytes);
            param.put("ImageBase64", base64String);
            content.close();
            outputStream.close();
        }
        if (!StringUtils.isNullOrEmpty(ocrDTO.getCardSide())) {
            param.put("CardSide", ocrDTO.getCardSide());
        }
        return param;
    }

    public static String gbEncoding(String url) {
        String resultURL = "";
        //遍历字符串
        for (int i = 0; i < url.length(); i++) {
            char charAt = url.charAt(i);
            //只对汉字处理
            if (isChineseChar(charAt)) {
                String encode = null;
                try {
                    encode = URLEncoder.encode(charAt + "", "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                resultURL += encode;
            } else {
                resultURL += charAt;
            }
        }
        return resultURL;
    }

    //判断汉字的方法,只要编码在\u4e00到\u9fa5之间的都是汉字
    public static boolean isChineseChar(char c) {
        return String.valueOf(c).matches("[\u4e00-\u9fa5]");
    }
}
