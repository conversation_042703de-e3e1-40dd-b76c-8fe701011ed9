package com.faw.work.ais.aic.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import com.faw.work.ais.aic.model.dto.FaqKnowledgeCustomExcelDataDTO;
import com.faw.work.ais.aic.model.request.FaqKnowledgeRequest;
import com.faw.work.ais.aic.service.FaqCategoryService;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.aic.service.FaqSimilarKnowledgeService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义FAQ知识Excel导入监听器
 * <AUTHOR>
 */
@Slf4j
public class FaqKnowledgeCustomExcelListener extends AnalysisEventListener<FaqKnowledgeCustomExcelDataDTO> {
    
    /**
     * 批量处理大小
     */
    private static final int BATCH_SIZE = 100;
    
    /**
     * 数据列表
     */
    private final List<FaqKnowledgeCustomExcelDataDTO> dataList = new ArrayList<>();
    
    /**
     * FAQ知识服务
     */
    private final FaqKnowledgeService faqKnowledgeService;
    
    /**
     * FAQ类目服务
     */
    private final FaqCategoryService faqCategoryService;
    
    /**
     * FAQ相似问服务
     */
    private final FaqSimilarKnowledgeService faqSimilarKnowledgeService;
    
    /**
     * 成功导入数量
     */
    @Getter
    private int successCount = 0;
    
    /**
     * 失败导入数量
     */
    @Getter
    private int failCount = 0;
    
    /**
     * 错误信息列表
     */
    @Getter
    private final List<String> errorMessages = new ArrayList<>();
    
    /**
     * 当前行号
     */
    private int currentRowNum = 0;
    
    /**
     * 构造函数
     *
     * @param faqKnowledgeService       FAQ知识服务
     * @param faqCategoryService        FAQ类目服务
     * @param faqSimilarKnowledgeService FAQ相似问服务
     */
    public FaqKnowledgeCustomExcelListener(FaqKnowledgeService faqKnowledgeService,
                                          FaqCategoryService faqCategoryService,
                                          FaqSimilarKnowledgeService faqSimilarKnowledgeService) {
        this.faqKnowledgeService = faqKnowledgeService;
        this.faqCategoryService = faqCategoryService;
        this.faqSimilarKnowledgeService = faqSimilarKnowledgeService;
    }
    
    @Override
    public void invoke(FaqKnowledgeCustomExcelDataDTO data, AnalysisContext context) {
        currentRowNum = context.readRowHolder().getRowIndex() + 1;
        if (data != null) {
            dataList.add(data);
            // 达到BATCH_SIZE，进行批量处理
            if (dataList.size() >= BATCH_SIZE) {
                saveData();
                dataList.clear();
            }
        }
    }
    
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余数据
        if (!dataList.isEmpty()) {
            saveData();
            dataList.clear();
        }
        log.info("Excel导入完成，成功：{}条，失败：{}条", successCount, failCount);
    }
    
    private void saveData() {
        for (FaqKnowledgeCustomExcelDataDTO data : dataList) {
            try {
                // 1. 数据校验
                if (StringUtils.isBlank(data.getCategoryName())) {
                    addErrorMessage("类目名称不能为空");
                    failCount++;
                    continue;
                }
                
                if (StringUtils.isBlank(data.getTitle())) {
                    addErrorMessage("知识标题不能为空");
                    failCount++;
                    continue;
                }
                
                // 答案必须存在一个
                if (StringUtils.isBlank(data.getPlainTextAnswer()) && StringUtils.isBlank(data.getRichTextAnswer())) {
                    addErrorMessage("答案不能为空");
                    failCount++;
                    continue;
                }
                
                // 2. 获取答案内容（优先使用富文本）
                String answer = StringUtils.isNotBlank(data.getRichTextAnswer()) 
                        ? data.getRichTextAnswer() 
                        : data.getPlainTextAnswer();
                
                // 3. 获取类目ID
                String categoryId = null;
                try {
                    categoryId = faqCategoryService.getIdByName(data.getCategoryName());
                    // 如果类目不存在，创建类目
                    if (categoryId == null) {
                        categoryId = faqCategoryService.createCategory(data.getCategoryName());
                    }
                } catch (Exception e) {
                    log.error("获取或创建类目失败: {}", data.getCategoryName(), e);
                    addErrorMessage("获取或创建类目失败: " + data.getCategoryName() + ", 错误: " + e.getMessage());
                    failCount++;
                    continue;
                }
                
                // 4. 检查知识是否已存在
                FaqKnowledgePO existingKnowledge = faqKnowledgeService.getByQuestion(data.getTitle(),categoryId);
                FaqKnowledgePO mainKnowledge;
                String mainKnowledgeId;
                
                // 5. 保存主知识
                if (existingKnowledge == null) {
                    // 创建新知识
                    FaqKnowledgeRequest knowledgeRequest = new FaqKnowledgeRequest();
                    knowledgeRequest.setCategoryId(categoryId);
                    knowledgeRequest.setCategoryName(data.getCategoryName());
                    knowledgeRequest.setQuestion(data.getTitle());
                    knowledgeRequest.setAnswer(answer);
                    
                    try {
                        // 1. 保存知识到数据库
                        FaqKnowledgePO knowledge = new FaqKnowledgePO();
                        BeanUtils.copyProperties(knowledgeRequest, knowledge);
                        knowledge.setHitCount(0L);
                        knowledge.setCreatedAt(LocalDateTime.now());
                        knowledge.setUpdatedAt(LocalDateTime.now());
                        faqKnowledgeService.save(knowledge);

                        mainKnowledgeId = knowledge.getId();
                        log.info("创建主知识成功: ID={}, 标题={}", mainKnowledgeId, data.getTitle());
                    } catch (Exception e) {
                        log.error("创建主知识失败: 标题={}", data.getTitle(), e);
                        addErrorMessage("创建主知识失败: " + data.getTitle() + ", 错误: " + e.getMessage());
                        failCount++;
                        continue;
                    }
                } else {
                    // 使用已有知识
                    mainKnowledgeId = existingKnowledge.getId();
                    log.info("使用已有主知识: ID={}, 标题={}", mainKnowledgeId, data.getTitle());
                }
                
                // 6. 处理相似问题（如果有）
                if (StringUtils.isNotBlank(data.getSimilarQuestions())) {
                    String[] similarQuestions = data.getSimilarQuestions().split("\\n");
                    List<FaqSimilarKnowledgePO> similarKnowledgeList = new ArrayList<>();
                    
                    for (String similarQuestion : similarQuestions) {
                        similarQuestion = similarQuestion.trim();
                        if (StringUtils.isNotBlank(similarQuestion)) {
                            FaqSimilarKnowledgePO similarKnowledge = new FaqSimilarKnowledgePO();
                            similarKnowledge.setQuestion(data.getTitle());
                            similarKnowledge.setSimilarQuestion(similarQuestion);
                            similarKnowledge.setCategoryId(categoryId);
                            similarKnowledge.setAnswer(answer);
                            similarKnowledge.setKnowledgeId(mainKnowledgeId);
                            similarKnowledge.setCreatedBy("system");
                            similarKnowledge.setCreatedAt(LocalDateTime.now());
                            similarKnowledge.setUpdatedBy("system");
                            similarKnowledge.setUpdatedAt(LocalDateTime.now());
                            similarKnowledgeList.add(similarKnowledge);
                        }
                    }
                    
                    // 保存相似问
                    if (!similarKnowledgeList.isEmpty()) {
                        try {
                            faqSimilarKnowledgeService.saveBatch(similarKnowledgeList);
                            log.info("保存相似问成功: 数量={}, 主知识ID={}", similarKnowledgeList.size(), mainKnowledgeId);
                        } catch (Exception e) {
                            log.error("保存相似问失败: 主知识ID={}", mainKnowledgeId, e);
                            addErrorMessage("保存相似问失败: 主知识ID=" + mainKnowledgeId + ", 错误: " + e.getMessage());
                            // 即使相似问保存失败，主知识已保存成功，所以算作部分成功
                        }
                    }
                }
                
                successCount++;
            } catch (Exception e) {
                log.error("处理Excel行数据失败: 行号={}", currentRowNum, e);
                addErrorMessage("处理Excel行数据失败: 行号=" + currentRowNum + ", 错误: " + e.getMessage());
                failCount++;
            }
        }
    }
    
    private void addErrorMessage(String message) {
        errorMessages.add("行号[" + currentRowNum + "]: " + message);
    }
} 