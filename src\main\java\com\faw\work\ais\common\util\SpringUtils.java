package com.faw.work.ais.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Spring上下文工具类
 */
@Component
public final class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext context = null;

    /**
     * 获取当前的环境配置，当有多个环境配置时，只获取第一个
     * @return 当前的环境配置
     */
    public static String getActiveProfile() {
        return context.getEnvironment().getActiveProfiles()[0];
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    //通过name获取 Bean.
    public static Object getBean(String name){
        return context.getBean(name);
    }

    //通过class获取Bean.
    public static <T> T getBean(Class<T> clazz){
        return context.getBean(clazz);
    }
}
