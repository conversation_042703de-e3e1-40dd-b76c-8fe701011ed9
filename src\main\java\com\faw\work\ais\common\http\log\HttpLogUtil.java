package com.faw.work.ais.common.http.log;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class HttpLogUtil {

    private static final String REQUEST_BODY = "requestBody";
    private static final String START_TIME = "startTime";

    private static final ThreadLocal<Map<String, Object>> THREAD_LOCAL =
            ThreadLocal.withInitial(() -> new ConcurrentHashMap<>(16));

    public static void setRequestBody(String requestBody) {
        THREAD_LOCAL.get().put(REQUEST_BODY, requestBody);
        THREAD_LOCAL.get().put(START_TIME, Instant.now());
    }

    public static String getRequestBody() {
        return (String) THREAD_LOCAL.get().get(REQUEST_BODY);
    }

    public static long cost() {
        Instant now = Instant.now();
        Instant start = (Instant) THREAD_LOCAL.get().get(START_TIME);
        return Duration.between(now, start).toMillis();
    }

    public static void clear() {
        THREAD_LOCAL.remove();
    }
}
