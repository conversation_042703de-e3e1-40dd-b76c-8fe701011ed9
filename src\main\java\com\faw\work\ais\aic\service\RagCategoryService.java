package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.RagCategoryPO;
import com.faw.work.ais.aic.model.request.RagCategoryPageRequest;

import java.util.List;

/**
 * 文档类目表 服务接口
 *
 * <AUTHOR> Assistant
 */
public interface RagCategoryService extends IService<RagCategoryPO> {

    /**
     * 根据条件查询类目列表
     *
     * @param ragCategory 查询条件
     * @return 类目列表
     */
    List<RagCategoryPO> getCategoryList(RagCategoryPO ragCategory);
    
    /**
     * 分页查询类目
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    Page<RagCategoryPO> getCategoryPage(RagCategoryPageRequest request);
} 