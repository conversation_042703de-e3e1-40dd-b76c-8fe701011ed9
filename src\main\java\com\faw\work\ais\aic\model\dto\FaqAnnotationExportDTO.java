package com.faw.work.ais.aic.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * FAQ标注任务导出DTO
 */
@Data
public class FaqAnnotationExportDTO {

    @ExcelProperty(value = "任务名称", index = 0)
    private String taskName;

    @ExcelProperty(value = "机器人名称", index = 1)
    private String robotName;

    @ExcelProperty(value = "数据来源", index = 2)
    private String dataSourceName;

    @ExcelProperty(value = "用户问法", index = 3)
    private String userQuestion;

    @ExcelProperty(value = "匹配类型", index = 4)
    private String matchTypeName;

    @ExcelProperty(value = "匹配答案", index = 5)
    private String matchedContent;

    @ExcelProperty(value = "FAQ标题", index = 6)
    private String faqTitle;

    @ExcelProperty(value = "匹配度分数", index = 7)
    private String matchScore;

    @ExcelProperty(value = "标注类型", index = 8)
    private String annotationTypeName;

    @ExcelProperty(value = "标注子类型", index = 9)
    private String annotationSubtypeName;

    @ExcelProperty(value = "标注时间", index = 10)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime annotatedAt;

    @ExcelProperty(value = "标注人", index = 11)
    private String annotatorName;

    @ExcelProperty(value = "创建时间", index = 12)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
}
