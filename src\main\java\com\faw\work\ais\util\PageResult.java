package com.faw.work.ais.util;

import java.io.Serializable;
import java.util.*;

/**
 * 通用分页结果类
 * @param <T> 数据类型
 */
public class PageResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    // 当前页码
    private int pageNum;
    // 每页数量
    private int pageSize;
    // 总记录数
    private long total;
    // 总页数
    private int pages;
    // 分页数据
    private List<T> list;
    // 是否第一页
    private boolean isFirstPage;
    // 是否最后一页
    private boolean isLastPage;
    // 是否有上一页
    private boolean hasPreviousPage;
    // 是否有下一页
    private boolean hasNextPage;

    /**
     * 空分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(0, 0, 0, Collections.emptyList());
    }

    /**
     * 构造函数
     */
    public PageResult(int pageNum, int pageSize, long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;

        // 计算总页数
        this.pages = (int) (total / pageSize + ((total % pageSize == 0) ? 0 : 1));

        // 计算分页状态
        this.isFirstPage = (pageNum == 1);
        this.isLastPage = (pageNum == pages || pages == 0);
        this.hasPreviousPage = (pageNum > 1);
        this.hasNextPage = (pageNum < pages);
    }

    // Getter和Setter方法
    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public boolean isFirstPage() {
        return isFirstPage;
    }

    public void setFirstPage(boolean firstPage) {
        isFirstPage = firstPage;
    }

    public boolean isLastPage() {
        return isLastPage;
    }

    public void setLastPage(boolean lastPage) {
        isLastPage = lastPage;
    }

    public boolean isHasPreviousPage() {
        return hasPreviousPage;
    }

    public void setHasPreviousPage(boolean hasPreviousPage) {
        this.hasPreviousPage = hasPreviousPage;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    /**
     * 转换为Map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("pageNum", pageNum);
        map.put("pageSize", pageSize);
        map.put("total", total);
        map.put("pages", pages);
        map.put("list", list);
        map.put("isFirstPage", isFirstPage);
        map.put("isLastPage", isLastPage);
        map.put("hasPreviousPage", hasPreviousPage);
        map.put("hasNextPage", hasNextPage);
        return map;
    }

    /**
     * 重写toString方法
     */
    @Override
    public String toString() {
        return "PageResult{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", total=" + total +
                ", pages=" + pages +
                ", list=" + list +
                ", isFirstPage=" + isFirstPage +
                ", isLastPage=" + isLastPage +
                ", hasPreviousPage=" + hasPreviousPage +
                ", hasNextPage=" + hasNextPage +
                '}';
    }

    /**
     * 重写equals方法
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PageResult<?> that = (PageResult<?>) o;
        return pageNum == that.pageNum &&
                pageSize == that.pageSize &&
                total == that.total &&
                pages == that.pages &&
                isFirstPage == that.isFirstPage &&
                isLastPage == that.isLastPage &&
                hasPreviousPage == that.hasPreviousPage &&
                hasNextPage == that.hasNextPage &&
                Objects.equals(list, that.list);
    }

    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return Objects.hash(pageNum, pageSize, total, pages, list, isFirstPage, isLastPage, hasPreviousPage, hasNextPage);
    }
}