package com.faw.work.ais.service.impl;

import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.dto.ContentSearchResult;
import com.faw.work.ais.service.ContentMilvusService;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.ConsistencyLevel;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.faw.work.ais.aic.config.MilvusPoolConfig.FAQ_TENANT_CLIENT_KEY;

@Service
@Slf4j
@RequiredArgsConstructor
public class ContentMilvusServiceImpl implements ContentMilvusService {

    private final MilvusPoolConfig milvusPoolConfig;
    private final Gson gson = new Gson();

    /**
     *
     * @param collectionName 集合名称
     * @param embedding 查询向量
     * @param topK 返回的结果数量
     * @param similarityThreshold
     * @param filterString
     * @return
     */
    @Override
    public List<ContentSearchResult> searchByEmbedding(String collectionName, float[] embedding, int topK, float similarityThreshold, String filterString) {
        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 准备搜索向量
            FloatVec floatVec = new FloatVec(embedding);

            // 配置搜索参数
            Map<String, Object> searchParams = new HashMap<>(32);
            searchParams.put("metric_type", "COSINE");

            // 执行搜索
            SearchReq request = SearchReq.builder()
                    .collectionName(collectionName)
                    .consistencyLevel(ConsistencyLevel.BOUNDED)
                    .annsField(MilvusPoolConfig.VECTOR_FIELD)
                    .topK(topK)
                    .data(List.of(floatVec))
                    .outputFields(List.of(MilvusPoolConfig.ID_FIELD, "content"))
                    .searchParams(searchParams)
                    .build();
            if (StringUtils.isNotBlank(filterString)){
                request.setFilter(filterString);
            }
            SearchResp searchResp = client.search(request);

            if (searchResp == null || searchResp.getSearchResults() == null || searchResp.getSearchResults().isEmpty()) {
                log.info("没有找到相似的评论");
                return new ArrayList<>();
            }

            // 解析搜索结果
            List<ContentSearchResult> results = new ArrayList<>();

            // 获取第一个查询向量的所有匹配结果
            List<SearchResp.SearchResult> searchResults = searchResp.getSearchResults().get(0);
            for (SearchResp.SearchResult result : searchResults) {
                Long documentId = (Long) result.getId();
                float similarity = result.getScore();
                String content = (String) result.getEntity().get("content");

                // 只添加相似度大于等于阈值的文档
                if (similarity >= similarityThreshold) {
                    log.info("找到相似文档，ID: {}, 相似度: {}", documentId, similarity);

                    ContentSearchResult searchResult = ContentSearchResult.builder()
                            .id(documentId)
                            .score(similarity)
                            .content(content)
                            .build();
                    results.add(searchResult);
                } else {
                    log.info("找到相似文档，ID: {}, 相似度: {}，但阈值 {} 未达到，忽略", documentId, similarity, similarityThreshold);
                }
            }

            return results;
        } catch (Exception e) {
            log.error("搜索向量时发生异常", e);
            return new ArrayList<>();
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }

    @Override
    public InsertResp storeContentEmbedding(String collectionName, Long vectorId, List<MilvusField> properties, float[] embedding) {

        if (StringUtils.isBlank(collectionName) || embedding.length == 0) {
            throw new BizException("集合名称或向量，不能为空");
        }

        MilvusClientV2 client = null;
        try {

            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 准备数据
            List<JsonObject> rows = new ArrayList<>();

            JsonObject row = new JsonObject();
            if (vectorId != null) {
                row.addProperty(MilvusPoolConfig.ID_FIELD, vectorId);
            }

            // 添加自定义属性
            this.addRowProperty(properties, row);

            // 添加向量字段
            row.add(MilvusPoolConfig.VECTOR_FIELD, gson.toJsonTree(embedding));


            rows.add(row);

            // 执行插入
            InsertResp res = client.insert(
                    InsertReq.builder()
                            .collectionName(collectionName)
                            .data(rows)
                            .build()
            );

            log.info("成功将文档ID {} 的向量插入到Milvus，向量维度: {}", vectorId, embedding.length);
            return res;
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }

    private void addRowProperty(List<MilvusField> properties, JsonObject row) {
        if (properties == null || properties.isEmpty()) {
            return;
        }
        for (MilvusField property : properties) {
            if (property.getValue() instanceof String) {
                row.addProperty(property.getName(), (String) property.getValue());
            } else if (property.getValue() instanceof Number) {
                row.addProperty(property.getName(), (Number) property.getValue());
            } else if (property.getValue() instanceof Boolean) {
                row.addProperty(property.getName(), (Boolean) property.getValue());
            } else if (property.getValue() instanceof Character) {
                row.addProperty(property.getName(), (Character) property.getValue());
            }
        }
    }
}
