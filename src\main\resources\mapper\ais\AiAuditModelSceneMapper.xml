<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiAuditModelSceneDao">

    <resultMap id="AiAuditModelScene" type="com.faw.work.ais.model.AiAuditModelScene" >
        <result column="id" property="id" />
        <result column="audit_id" property="auditId" />
        <result column="scene_code" property="sceneCode" />
        <result column="scene_name" property="sceneName" />
        <result column="audit_point_name" property="auditPointName" />
        <result column="create_time" property="createTime" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_code" property="updateUserCode" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `audit_id`,
        `scene_code`,
        `scene_name`,
        `audit_point_name`,
        `create_time`,
        `create_user_code`,
        `create_user_name`,
        `update_time`,
        `update_user_code`,
        `update_user_name`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_audit_model_scene (
            `audit_id`,
            `scene_code`,
            `scene_name`,
            `audit_point_name`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES(
            #{aiAuditModelScene.auditId},
            #{aiAuditModelScene.sceneCode},
            #{aiAuditModelScene.sceneName},
            #{aiAuditModelScene.auditPointName},
            #{aiAuditModelScene.createUserCode},
            #{aiAuditModelScene.createUserName},
            #{aiAuditModelScene.updateUserCode},
            #{aiAuditModelScene.updateUserName}
        )
    </insert>
    <insert id="insertBatch"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_audit_model_scene (
            `audit_id`,
            `scene_code`,
            `scene_name`,
            `audit_point_name`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES
        <foreach collection="aiAuditModelScenes" item="aiAuditModelScene" separator=",">
        (
                  #{aiAuditModelScene.auditId},
                  #{aiAuditModelScene.sceneCode},
                  #{aiAuditModelScene.sceneName},
                  #{aiAuditModelScene.auditPointName},
                  #{aiAuditModelScene.createUserCode},
                  #{aiAuditModelScene.createUserName},
                  #{aiAuditModelScene.updateUserCode},
                  #{aiAuditModelScene.updateUserName}
              )
        </foreach>
    </insert>


    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM ai_audit_model_scene
        where  audit_id = #{id}

    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE ai_audit_model_scene
         <set>
            <if test="aiAuditModelScene.auditId != null and aiAuditModelScene.auditId != '' " >
                audit_id = #{aiAuditModelScene.auditId},
            </if>
            <if test="aiAuditModelScene.sceneCode != null and aiAuditModelScene.sceneCode != '' " >
                scene_code = #{aiAuditModelScene.sceneCode},
            </if>
            <if test="aiAuditModelScene.sceneName != null and aiAuditModelScene.sceneName != '' " >
                scene_name = #{aiAuditModelScene.sceneName},
            </if>
            <if test="aiAuditModelScene.auditPointName != null and aiAuditModelScene.auditPointName != '' " >
                audit_point_name = #{aiAuditModelScene.auditPointName},
            </if>
            <if test="aiAuditModelScene.createTime != null and aiAuditModelScene.createTime != '' " >
                create_time = #{aiAuditModelScene.createTime},
            </if>
            <if test="aiAuditModelScene.createUserCode != null and aiAuditModelScene.createUserCode != '' " >
                create_user_code = #{aiAuditModelScene.createUserCode},
            </if>
            <if test="aiAuditModelScene.createUserName != null and aiAuditModelScene.createUserName != '' " >
                create_user_name = #{aiAuditModelScene.createUserName},
            </if>
            <if test="aiAuditModelScene.updateTime != null and aiAuditModelScene.updateTime != '' " >
                update_time = #{aiAuditModelScene.updateTime},
            </if>
            <if test="aiAuditModelScene.updateUserCode != null and aiAuditModelScene.updateUserCode != '' " >
                update_user_code = #{aiAuditModelScene.updateUserCode},
            </if>
            <if test="aiAuditModelScene.updateUserName != null and aiAuditModelScene.updateUserName != '' " >
                update_user_name = #{aiAuditModelScene.updateUserName},
            </if>
         </set>
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </update>


    <select id="getAiAuditModelSceneList" resultMap="AiAuditModelScene">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_audit_model_scene
        <where>
          <if test="aiAuditModelScene.id != null " >
              AND  id = #{aiAuditModelScene.id}
          </if>
          <if test="aiAuditModelScene.auditId != null " >
              AND  audit_id = #{aiAuditModelScene.auditId}
          </if>
          <if test="aiAuditModelScene.sceneCode != null and aiAuditModelScene.sceneCode != '' " >
              AND  scene_code = #{aiAuditModelScene.sceneCode}
          </if>
          <if test="aiAuditModelScene.sceneName != null and aiAuditModelScene.sceneName != '' " >
              AND  scene_name = #{aiAuditModelScene.sceneName}
          </if>
          <if test="aiAuditModelScene.auditPointName != null and aiAuditModelScene.auditPointName != '' " >
              AND  audit_point_name = #{aiAuditModelScene.auditPointName}
          </if>
          <if test="aiAuditModelScene.createTime != null and aiAuditModelScene.createTime != '' " >
              AND  create_time = #{aiAuditModelScene.createTime}
          </if>
          <if test="aiAuditModelScene.createUserCode != null and aiAuditModelScene.createUserCode != '' " >
              AND  create_user_code = #{aiAuditModelScene.createUserCode}
          </if>
          <if test="aiAuditModelScene.createUserName != null and aiAuditModelScene.createUserName != '' " >
              AND  create_user_name = #{aiAuditModelScene.createUserName}
          </if>
          <if test="aiAuditModelScene.updateTime != null and aiAuditModelScene.updateTime != '' " >
              AND  update_time = #{aiAuditModelScene.updateTime}
          </if>
          <if test="aiAuditModelScene.updateUserCode != null and aiAuditModelScene.updateUserCode != '' " >
              AND  update_user_code = #{aiAuditModelScene.updateUserCode}
          </if>
          <if test="aiAuditModelScene.updateUserName != null and aiAuditModelScene.updateUserName != '' " >
              AND  update_user_name = #{aiAuditModelScene.updateUserName}
          </if>

        </where>
     </select>

    <select id="getAiAuditModelSceneById" parameterType="java.util.Map" resultMap="AiAuditModelScene">
        SELECT <include refid="Base_Column_List" />
        FROM ai_audit_model_scene

        where  id = #{id}

    </select>



</mapper>

