package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * log_info_java_new
 * <AUTHOR>
@Data
@Schema(description = "日志Java信息表")
public class LogInfoJava implements Serializable {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 系统id
     */
    @Schema(description = "系统id")
    private String systemId;

    /**
     * 调用java服务的时间
     */
    @Schema(description = "调用java服务的时间")
    private Date javaCallTime;

    /**
     * 批次id
     */
    @Schema(description = "批次id")
    private String batchId;

    /**
     * 日志创建时间
     */
    @Schema(description = "日志创建时间")
    private Date createTime;

    /**
     * 日志更新时间
     */
    @Schema(description = "日志更新时间")
    private Date updateTime;

    /**
     * 调用java服务的系统入参
     */
    @Schema(description = "调用java服务的系统入参")
    private String javaParam;

    private static final long serialVersionUID = 1L;
}