package com.faw.work.ais.config;

import lombok.Data;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-03-08
 * @description 消息队列配置
 */
@Data
@Configuration
@Component
public class RabbitMQConfig {
    /**
     * 消息队列名称-走python服务的队列
     **/
    @Value("${mq.queue-name}")
    private String queenName;
    /**
     * 交换机名称
     **/
    @Value("${mq.exchange-name}")
    private String exchangeName;
    /**
     * 路由key
     **/
    @Value("${mq.routing-key}")
    private String routingKey;

    @Value("${mq.aisErrorQueue.queue-name}")
    private String errorQueue;

    @Value("${mq.aisErrorQueue.routing-key}")
    private String errorRouting;

    @Value("${mq.bnzxQueue.queue-name}")
    private String bnzxQueue;

    @Value("${mq.bnzxQueue.routing-key}")
    private String bnzxRouting;

    /**
     * @MethodName: queue
     * @description: 消息队列
     * @UpdateTime: 2024/3/7
     * @Return: org.springframework.amqp.core.Queue
     **/
    @Bean
    public Queue queue() {
        return new Queue(queenName);
    }

    /***
     * @MethodName: exchange
     * @description: 交换机
     * @UpdateTime: 2024/3/7
     * @Return: org.springframework.amqp.core.DirectExchange
     **/

    @Bean
    public DirectExchange exchange() {
        return new DirectExchange(exchangeName);
    }

    @Bean
    public Queue errorQueue(){return new Queue(errorQueue);}

    public Queue bnzxQueue(){return new Queue(bnzxQueue);}

    /**
     * @MethodName: binding
     * @description: 将消息队列 绑定到交换机
     * @UpdateTime: 2024/3/7
     **/
    @Bean
    public Binding binding() {
        return BindingBuilder
                .bind(queue())
                .to(exchange())
                .with(routingKey);
    }

    /**
     * @MethodName: binding
     * @description: 将消息队列 绑定到交换机
     * @UpdateTime: 2024/3/7
     **/
    @Bean
    public Binding bindingErrorQueue() {
        return BindingBuilder
                .bind(errorQueue())
                .to(exchange())
                .with(errorRouting);
    }

    /**
     * @MethodName: binding
     * @description: 将国补消息队列 绑定到交换机
     * @UpdateTime: 2024/3/7
     **/
    @Bean
    public Binding bindingBnzxQueue() {
        return BindingBuilder
                .bind(bnzxQueue())
                .to(exchange())
                .with(bnzxRouting);
    }
}
