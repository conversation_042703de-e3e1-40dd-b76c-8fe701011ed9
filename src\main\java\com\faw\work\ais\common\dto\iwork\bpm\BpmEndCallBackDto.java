package com.faw.work.ais.common.dto.iwork.bpm;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Schema(description = "BPM 结束回电 DTO")
@Data
@ApiModel(value = "bpm工作流结束节点监听回调接口参数类")
@EqualsAndHashCode
public class BpmEndCallBackDto {
    @Schema(description = "应用源")
    private String appsource;
    @Schema(description = "服务代码")
    private String servicecode;
    @Schema(description = "接口参数")
    private InterfaceParam interfaceParam;
    @Schema(description = "服务节点 ID")
    private String serviceNodeId;
    @Schema(description = "租户限制 ID")
    private String tenantLimitId;
    @Schema(description = "服务节点名称")
    private String serviceNodeName;
    @Schema(description = "子进程模板名称")
    private String subProcessTemplateName;
    @Schema(description = "类型")
    private String type;
    @Schema(description = "类别")
    private String category;


    @Schema(description = "接口参数")
    @ApiModel(value = "返回的结果在这个类里面")
    @Data
    public static class InterfaceParam {
        /**
         * 审批结果在这个数据中
         * execution.eventName==end 流程通过结束
         * execution.eventName==end&&execution.deleteReason==REJECTTOSTART  流程被驳回
         * execution.eventName==end&&execution.deleteReason==withdraw  流程被撤销
         * execution.eventName==end&&execution.deleteReason==delete 流程删除
         * */
        @Schema(description = "审批结果在这个数据中 execution.eventName==end 流程通过结束 execution.eventName==end&&execution.deleteReason==REJECTTOSTART  流程被驳回 execution.eventName==end&&execution.deleteReason==withdraw  流程被撤销 execution.eventName==end&&execution.deleteReason==delete 流程删除 */")
        private Execution execution;
        @Schema(description = "任务")
        private Task task;
        @Schema(description = "发送类型")
        private String sendType;
        @Schema(description = "登录用户 ID")
        private String loginUserId;
        @Schema(description = "语言")
        private String language;
        @Schema(description = "用户 ID")
        private String userId;

    }

    /**
     * eventName==end 流程通过结束
     * eventName==end&&execution.deleteReason==REJECTTOSTART  流程被驳回
     * eventName==end&&execution.deleteReason==withdraw  流程被撤销
     * eventName==end&&execution.deleteReason==delete 流程删除
     * */
    @Schema(description = "eventName==end 流程通过结束 eventName==end&&execution.deleteReason==REJECTTOSTART  流程被驳回 eventName==end&&execution.deleteReason==withdraw  流程被撤销 eventName==end&&execution.deleteReason==delete 流程删除 */")
    @Data
    public static class Execution {
        @Schema(description = "进程定义 ID")
        private String processDefinitionId;
        @Schema(description = "进程实例 ID")
        private String processInstanceId;
        @Schema(description = "租户代码")
        private String tenantCode;
        @Schema(description = "ESN 文档")
        private Boolean esnDocument;
        @Schema(description = "删除原因")
        private String deleteReason;
        @Schema(description = "启动用户 ID")
        private String startUserId;
        @Schema(description = "模板名称")
        private String templateName;
        @Schema(description = "应用租户 ID")
        private String appTenantId;
        @Schema(description = "业务密钥")
        private String businessKey;
        @Schema(description = "名字")
        private String name;
        @Schema(description = "租户 ID")
        private String tenantId;
        @Schema(description = "事件名称")
        private String eventName;
        @Schema(description = "同上")
        private String id;
        @Schema(description = "应用源")
        private String appSource;
        @Schema(description = "类别")
        private String category;
        @Schema(description = "是发送到入门 IM")
        private Boolean isSendToStarterIM;


    }
    @Schema(description = "任务")
    @Data
    public static class Task {
        @Schema(description = "暂停状态")
        private Integer suspensionState;
        @Schema(description = "最后")
        private Boolean last;
        @Schema(description = "标识链接已初始化")
        private Boolean identityLinksInitialized;
        @Schema(description = "租户代码")
        private String tenantCode;
        @Schema(description = "优先权")
        private Integer priority;
        @Schema(description = "校订")
        private Integer revision;
        @Schema(description = "删除")
        private Boolean deleted;
        @Schema(description = "应用租户 ID")
        private String appTenantId;
        @Schema(description = "名字")
        private String name;
        @Schema(description = "租户 ID")
        private String tenantId;
        @Schema(description = "事件名称")
        private String eventName;
        @Schema(description = "应用源")
        private String appSource;
        @Schema(description = "第一")
        private Boolean first;
    }
}


/**
 * 没有具体参数字段解释
 * */


