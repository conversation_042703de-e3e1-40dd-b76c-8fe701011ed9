package com.faw.work.ais.aic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.enums.EnvEnum;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import com.faw.work.ais.aic.mapper.faq.FaqSimilarKnowledgeMapper;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.aic.service.FaqSimilarKnowledgeService;
import com.faw.work.ais.aic.service.MilvusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 相似问Service实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FaqSimilarKnowledgeServiceImpl extends ServiceImpl<FaqSimilarKnowledgeMapper, FaqSimilarKnowledgePO> implements FaqSimilarKnowledgeService {

    private final EmbeddingService embeddingService;
    private final MilvusService milvusService;
    private final FaqSimilarKnowledgeMapper faqSimilarKnowledgeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSimilarKnowledge(FaqSimilarKnowledgePO similarKnowledge) {
        // 参数校验
        if (similarKnowledge == null || similarKnowledge.getSimilarQuestion() == null) {
            throw new IllegalArgumentException("相似问不能为空");
        }

        if (similarKnowledge.getKnowledgeId() == null) {
            throw new IllegalArgumentException("原问题ID不能为空");
        }

        // 设置创建和更新信息
        LocalDateTime now = LocalDateTime.now();
        similarKnowledge.setCreatedAt(now);
        similarKnowledge.setUpdatedAt(now);
        similarKnowledge.setCreatedBy(UserThreadLocalUtil.getRealName());
        similarKnowledge.setUpdatedBy(UserThreadLocalUtil.getRealName());

        // 保存到数据库
        this.save(similarKnowledge);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchSimilarKnowledge(List<FaqSimilarKnowledgePO> similarKnowledgeList) {
        if (similarKnowledgeList == null || similarKnowledgeList.isEmpty()) {
            return true;
        }

        // 设置创建和更新信息
        LocalDateTime now = LocalDateTime.now();
        String realName = UserThreadLocalUtil.getRealName();
        
        for (FaqSimilarKnowledgePO similarKnowledge : similarKnowledgeList) {
            similarKnowledge.setCreatedAt(now);
            similarKnowledge.setUpdatedAt(now);
            similarKnowledge.setCreatedBy(realName);
            similarKnowledge.setUpdatedBy(realName);
        }

        // 批量保存到数据库
        return this.saveBatch(similarKnowledgeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSimilarKnowledge(FaqSimilarKnowledgePO similarKnowledge) {
        if (similarKnowledge == null || similarKnowledge.getId() == null) {
            return false;
        }

        // 设置更新信息
        similarKnowledge.setUpdatedAt(LocalDateTime.now());
        similarKnowledge.setUpdatedBy(UserThreadLocalUtil.getRealName());

        return this.updateById(similarKnowledge);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSimilarKnowledge(String id) {
        if (id == null) {
            return false;
        }

        boolean dbResult = this.removeById(id);
        if (!dbResult) {
            return false;
        }

        try {
            // 删除向量
            List<String> ids = new ArrayList<>();
            ids.add(id);
            milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, ids, null);
            log.info("相似问向量删除成功, id={}", id);
            return true;
        } catch (Exception e) {
            log.error("相似问向量删除失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public FaqSimilarKnowledgePO getSimilarKnowledgeDetail(String id) {
        return this.getById(id);
    }

    @Override
    public List<FaqSimilarKnowledgePO> listByOriginalId(String originalId, String env) {
        List<FaqSimilarKnowledgePO> result = new ArrayList<>();
        if (EnvEnum.TEST.getCode().equals(env)) {
            result = faqSimilarKnowledgeMapper.selectByOriginalId(originalId);
        } else if (EnvEnum.PROD.getCode().equals(env)) {
            result = faqSimilarKnowledgeMapper.selectProdByOriginalId(originalId);
        } else {
            throw new BizException("环境类型不正确");
        }
        return result;
    }

    @Override
    public List<FaqSimilarKnowledgePO> listByCategoryIds(List<String> categoryIds) {
        return faqSimilarKnowledgeMapper.selectByCategoryIds(categoryIds);
    }


    @Override
    public List<FaqSimilarKnowledgePO> listByOriginalIds(List<String> originalIds) {
        return this.list(new LambdaQueryWrapper<FaqSimilarKnowledgePO>()
                .in(FaqSimilarKnowledgePO::getKnowledgeId, originalIds));
    }
} 