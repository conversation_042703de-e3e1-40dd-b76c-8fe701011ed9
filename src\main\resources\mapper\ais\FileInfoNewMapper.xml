<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.FileInfoNewDao">

    <resultMap id="FileInfoNew" type="com.faw.work.ais.model.FileInfoNew" >
        <result column="id" property="id" />
        <result column="trace_id" property="traceId" />
        <result column="file_url" property="fileUrl" />
        <result column="file_id" property="fileId" />
        <result column="file_index" property="fileIndex" />
        <result column="file_type" property="fileType" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `trace_id`,
        `file_url`,
        `file_id`,
        `file_index`,
        `file_type`,
        `create_time`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO file_info_new (
            `trace_id`,
            `file_url`,
            `file_id`,
            `file_index`,
            `file_type`
        )
        VALUES(
            #{fileInfoNew.traceId},
            #{fileInfoNew.fileUrl},
            #{fileInfoNew.fileId},
            #{fileInfoNew.fileIndex},
            #{fileInfoNew.fileType}
        )
    </insert>

    <insert id="insertBatch"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO file_info_new (
            `trace_id`,
            `file_url`,
            `file_id`,
            `file_index`,
            `file_type`
        )
        VALUES
        <foreach collection="fileInfoNews" item="fileInfoNew" separator=",">
        (
                  #{fileInfoNew.traceId},
                  #{fileInfoNew.fileUrl},
                  #{fileInfoNew.fileId},
                  #{fileInfoNew.fileIndex},
                  #{fileInfoNew.fileType}
              )
        </foreach>
    </insert>

    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM file_info_new
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </delete>

    <delete id="deleteByIds" parameterType="java.util.Map" >
        DELETE FROM file_info_new
        where id  IN (
        <foreach collection="ids" item="code" separator=",">
            #{code}
        </foreach>
        )
    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE file_info_new
         <set>
            <if test="fileInfoNew.traceId != null and fileInfoNew.traceId != '' " >
                trace_id = #{fileInfoNew.traceId},
            </if>
            <if test="fileInfoNew.fileUrl != null and fileInfoNew.fileUrl != '' " >
                file_url = #{fileInfoNew.fileUrl},
            </if>
            <if test="fileInfoNew.fileId != null and fileInfoNew.fileId != '' " >
                file_id = #{fileInfoNew.fileId},
            </if>
            <if test="fileInfoNew.fileIndex != null and fileInfoNew.fileIndex != '' " >
                file_index = #{fileInfoNew.fileIndex},
            </if>
            <if test="fileInfoNew.fileType != null and fileInfoNew.fileType != '' " >
                file_type = #{fileInfoNew.fileType},
            </if>
            <if test="fileInfoNew.createTime != null and fileInfoNew.createTime != '' " >
                create_time = #{fileInfoNew.createTime},
            </if>
         </set>
        WHERE id = #{fileInfoNew.id}
    </update>


    <select id="getFileInfoNewList" resultMap="FileInfoNew">
        SELECT
        <include refid="Base_Column_List" />
        FROM file_info_new
        <where>
          <if test="fileInfoNew.id != null and fileInfoNew.id != '' " >
              AND  id = #{fileInfoNew.id}
          </if>
          <if test="fileInfoNew.traceId != null and fileInfoNew.traceId != '' " >
              AND  trace_id = #{fileInfoNew.traceId}
          </if>
          <if test="fileInfoNew.fileUrl != null and fileInfoNew.fileUrl != '' " >
              AND  file_url = #{fileInfoNew.fileUrl}
          </if>
          <if test="fileInfoNew.fileId != null and fileInfoNew.fileId != '' " >
              AND  file_id = #{fileInfoNew.fileId}
          </if>
          <if test="fileInfoNew.fileIndex != null and fileInfoNew.fileIndex != '' " >
              AND  file_index = #{fileInfoNew.fileIndex}
          </if>
          <if test="fileInfoNew.fileType != null and fileInfoNew.fileType != '' " >
              AND  file_type = #{fileInfoNew.fileType}
          </if>
          <if test="fileInfoNew.createTime != null and fileInfoNew.createTime != '' " >
              AND  create_time = #{fileInfoNew.createTime}
          </if>

        </where>
     </select>

    <select id="getFileInfoNewById" parameterType="java.util.Map" resultMap="FileInfoNew">
        SELECT <include refid="Base_Column_List" />
        FROM file_info_new

        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </select>
    <select id="getFileInfoNewListByResultIds" resultMap="FileInfoNew">
        SELECT
        <include refid="Base_Column_List" />
        FROM file_info_new
       where  trace_id IN (
        <foreach collection="ids" item="code" separator=",">
            #{code}
        </foreach>
        )

    </select>

    <delete id="deleteByTraceId">
        delete
        FROM file_info_new
        where trace_id = #{traceId}
    </delete>

    <select id="getFileInfosByTraceId" resultType="java.lang.String">
        select file_url from file_info_new where trace_id = #{traceId}
    </select>

    <select id="getFileIdByTraceId" resultType="java.lang.Long">
        select id from file_info_new where trace_id = #{traceId}
    </select>

</mapper>

