package com.faw.work.ais.entity.vo.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "AI 任务 vo")
@Data
public class AiTaskVO {

    @Schema(description = "系统 ID")
    @ApiModelProperty(value = "")
    private String systemId;

    @Schema(description = "内容")
    @ApiModelProperty(value = "图片地址")
    private List<String> content;

    @Schema(description = "给定信息 json")
    @ApiModelProperty(value = "")
    private String givenInfoJson;

    @Schema(description = "给定信息 JSON DESC")
    @ApiModelProperty(value = "")
    private String givenInfoJsonDesc;

    @Schema(description = "")
    private String callbackUrl;

    @Schema(description = "网址")
    @ApiModelProperty(value = "")
    private String Url;

    @Schema(description = "回调类型")
    @ApiModelProperty(value = "")
    private String callbackType;

    @Schema(description = "任务类型")
    @ApiModelProperty(value = "")
    private String taskType;

    @Schema(description = "内容类型")
    @ApiModelProperty(value = "")
    private String contentType;

    @Schema(description = "业务 ID")
    @ApiModelProperty(value = "")
    private String bizId;

    @Schema(description = "业务类型")
    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @Schema(description = "提示")
    @ApiModelProperty(value = "提示词")
    private String prompt;

    @Schema(description = "版本")
    @ApiModelProperty(value = "版本")
    private String version;

    @Schema(description = "任务结果")
    @ApiModelProperty(value = "任务结果")
    private String taskResult;

}
