package com.faw.work.ais.common.http.log;

import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;

import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;

import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;




@Slf4j
@RestControllerAdvice
public class LogResponseBodyAdvice<T> implements ResponseBodyAdvice<T> {

    @Resource
    private HttpLogProperties httpLogProperties;
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return httpLogProperties.getEnable();
    }

    @Override
    public T beforeBodyWrite(T body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        String url = request.getURI().toString();
        String path = request.getURI().getPath();
        HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();
        HttpLog httpLog = new HttpLog();
        httpLog.queryString = JSONObject.toJSONString(servletRequest.getQueryString());
        httpLog.url = url;
        String responseBody = JSONObject.toJSONString(body);
        if (responseBody.length() > httpLogProperties.getMaxBodySize()) {
            responseBody = responseBody.substring(0, httpLogProperties.getMaxBodySize());
        }
        httpLog.responseBody = responseBody;
        httpLog.requestBody = HttpLogUtil.getRequestBody();
//        httpLog.cost = HttpLogUtil.cost();
        httpLog.method = request.getMethod().toString();
        log.info("http-log path: {},{}", path, httpLog);
        HttpLogUtil.clear();
        return body;
    }

    @Data
    public static class HttpLog {
        String url;
        Long cost;
        String requestBody;
        String method;
        String responseBody;
        String queryString;

        @Override
        public String toString() {
            return "{" +
                    "url='" + url + '\'' +
                    ", cost=" + cost +
                    ", requestBody='" + requestBody + '\'' +
                    ", queryString='" + queryString + '\'' +
                    ", method='" + method + '\'' +
                    ", responseBody='" + responseBody + '\'' +
                    '}';
        }
    }

}
