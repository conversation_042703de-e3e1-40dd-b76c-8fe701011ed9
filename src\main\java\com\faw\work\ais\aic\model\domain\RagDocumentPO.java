package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 文档表
 *
 * <AUTHOR>
 */
@Data
@TableName("rag_document")
@Schema(description = "文档实体")
public class RagDocumentPO {

    @TableId(value = "id")
    @Schema(description = "文档ID")
    private Long id;

    @TableField("tenant_id")
    @Schema(description = "租户ID，用于多租户隔离")
    private Long tenantId;

    @TableField("category_id")
    @Schema(description = "所属类目ID")
    private Long categoryId;

    @TableField("file_name")
    @Schema(description = "文件名称")
    private String fileName;

    @TableField("file_type")
    @Schema(description = "文件类型（如 pdf, docx 等）")
    private String fileType;

    @TableField("file_size")
    @Schema(description = "文件大小（MB）")
    private BigDecimal fileSize;

    @TableField("file_url")
    @Schema(description = "文件存储路径或URL")
    private String fileUrl;

    @TableField("object_key")
    @Schema(description = "桶key")
    private String objectKey;

    @TableField("parse_type")
    @Schema(description = "数据解析方式（01-代码切分 02-大模型切分）")
    private String parseType;

    @TableField("parse_status")
    @Schema(description = "解析状态（未解析/解析中/解析完成/解析失败）")
    private String parseStatus;

    @TableField("parse_error")
    @Schema(description = "解析失败时的错误信息")
    private String parseError;

    @TableField("chunk_strategy")
    @Schema(description = "分段策略(00-智能切分 01-自定义切分)")
    private String chunkStrategy;

    @TableField("chunk_separator")
    @Schema(description = "分段标识符(默认双换行符\\n ，。！等)")
    private String chunkSeparator;

    @TableField("chunk_length")
    @Schema(description = "分段最大长度(tokens)")
    private Integer chunkLength;

    @TableField("overlap_length")
    @Schema(description = "分段重叠长度(tokens)")
    private Integer overlapLength;

    @TableField("label")
    @Schema(description = "标签")
    private String label;

    @TableField("biz_info")
    @Schema(description = "业务信息")
    private String bizInfo;


    @TableField("created_by")
    @Schema(description = "创建人")
    private String createdBy;

    @TableField("created_at")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField("updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @TableField("updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;


    @TableField(exist = false)
    @Schema(description = "Embedding模型名称")
    private String embeddingModel;

    @TableField(exist = false)
    @Schema(description = "相似度阈值(0-1)，用于召回时过滤")
    private Double similarityThreshold;

    @TableField(exist = false)
    @Schema(description = "集合名称（按照场景分类，相当于分表）")
    private String collectionName;


} 