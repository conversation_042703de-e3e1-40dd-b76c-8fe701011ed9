package com.faw.work.ais.entity.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AI看板筛选入参
 */
@Data
public class AiKanBanDTO {

    @Schema(description = "系统id" )
    private String systemId;

    @Schema(description = "AI审核开始日期" )
    private String aiCheckTimeStart;

    @Schema(description = "AI审核结束日期" )
    private String aiCheckTimeEnd;

    @Schema(description = "人工审核开始日期" )
    private String humanCheckTimeStart;

    @Schema(description = "人工审核结束日期" )
    private String humanCheckTimeEnd;

    @Schema(description = "单据准确率选中标识；0-未选中；1-选中；和规则准确率互斥，默认为0" )
    private String billRightRateFlag;

    @Schema(description = "规则准确率选中标识；0-未选中；1-选中；和规则准确率互斥，默认为0" )
    private String ruleCheckRateFlag;

    @Schema(description = "统计类型；1-日；2-周；3-月" )
    private String totalType;

    @Schema(description = "单据id")
    private String batchId;

    @Schema(description = "请求的唯一id")
    private String traceId;

    @Schema(description = "规则id")
    private String taskType;

}
