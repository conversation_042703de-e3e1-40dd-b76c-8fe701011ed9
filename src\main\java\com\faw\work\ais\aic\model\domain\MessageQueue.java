package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("message_queue")
public class MessageQueue {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    private String bizType;
    private String messageId;
    
    private String queueName;
    
    private String messageContent;
    
    private Integer status;
    
    private Integer retryCount;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
    
    private String remark;
} 