package com.faw.work.ais.common.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "iwork通过用户组code分页查询用户列表参数实体类")
public class QueryUserListPageDto {

    @Schema(description ="用户组编码")
    private String code;

    @Schema(description ="当前页数")
    private Integer currentPage = 1;

    @Schema(description ="每页显示数量")
    private Integer pageSize = 10;

    @Schema(description ="系统id")
    private String systemId;



}
