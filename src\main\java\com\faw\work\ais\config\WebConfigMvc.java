package com.faw.work.ais.config;

import com.faw.work.ais.interceptor.AuthorizationInterceptor;
import com.faw.work.ais.interceptor.UcgApiAuthorizationInterceptor;
import com.faw.work.ais.interceptor.WhiteListInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * 请求拦截器配置
 * <AUTHOR>
 * @date 2024/01/03
 */
@Configuration
@ConditionalOnProperty(
        name = "online.run",
        havingValue = "true",
        matchIfMissing = true
)
public class WebConfigMvc implements WebMvcConfigurer {

    @Bean
    public WhiteListInterceptor getWhitelistInterceptor(){
        return new WhiteListInterceptor();
    }

    @Bean
    public AuthorizationInterceptor getAuthorizationInterceptor(){
        return new AuthorizationInterceptor();
    }

    @Bean
    public UcgApiAuthorizationInterceptor getUcgApiAuthorizationInterceptor(){
        return new UcgApiAuthorizationInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 对入参统一处理的拦截器，暂时没用上
//        registry.addInterceptor(getWhitelistInterceptor());

        // 项目本身拦截器排除路径
        registry.addInterceptor(getAuthorizationInterceptor()).addPathPatterns("/**").excludePathPatterns(
                "/error",
                "/actuator/**",
                "/compilation/callback",
                "/IntegrateCompilation/callback",
                "/project/type/callback",
                "/project/callback",
                "/activitytype/callback",
                "/activityPlan/callback",
                "/dms/**",
                "/v1/**",
                "/common/**",
                "/health/empty",
                "/workflow/**",
                "/external/**",
                "/kanBan/**",
                "/number/**",
                "/aiTaskResultNew/**",
                "/quantityStatistics/**",
                "/operationCenter/**",
                "/scheduled/**",
                "/aiChat/**",
                "/aiTest/**",
                "/faq-knowledge/**",
                "/rag-document/**",
                "/faq-core/**",
                // "/faq-set/**",
                "/public-api/**",
                "/llm-*/**",
                "/content/**",
                "/recommend/**",
                "/aiAgent/**",
                "/yanggou/**",
                "/redis/**"
        );
        // 通过开放api网关调用的接口的拦截器,暂时没生效，不清楚网关那块不同系统获取的token是否是有一致的
//        registry.addInterceptor(getUcgApiAuthorizationInterceptor()).addPathPatterns("/workflow/callback");
    }

}
