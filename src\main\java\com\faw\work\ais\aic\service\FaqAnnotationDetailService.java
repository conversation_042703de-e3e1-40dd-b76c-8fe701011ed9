package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import com.faw.work.ais.aic.model.request.FaqHitLogDetailQueryRequest;
import com.faw.work.ais.aic.model.response.FaqHitLogDetailResponse;
import com.github.pagehelper.PageInfo;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * FAQ标注详情Service接口
 *
 * <AUTHOR>
 */
public interface FaqAnnotationDetailService {

    /**
     * 创建标注详情
     *
     * @param detail 标注详情信息
     * @return 创建的详情ID
     */
    String createDetail(FaqAnnotationDetailPO detail);

    /**
     * 批量创建标注详情
     *
     * @param details 标注详情列表
     * @return 创建的数量
     */
    @Async("commonExecutor")
    CompletableFuture<Integer> batchCreateDetails(List<FaqAnnotationDetailPO> details);

    /**
     * 根据ID查询标注详情
     *
     * @param detailId 详情ID
     * @return 标注详情信息
     */
    FaqAnnotationDetailPO getDetailById(String detailId);

    /**
     * 查询任务的标注详情列表
     *
     * @param request 查询请求参数
     * @return 详情列表
     */
    PageInfo<FaqHitLogDetailResponse> getTaskDetails(FaqHitLogDetailQueryRequest request);

    /**
     * 统计任务的总数据量
     *
     * @param taskId 任务ID
     * @return 总数据量
     */
    int countByTaskId(String taskId);

    /**
     * 统计任务的已标注数量
     *
     * @param taskId 任务ID
     * @return 已标注数量
     */
    int countAnnotatedByTaskId(String taskId);

    /**
     * 标注单条数据
     *
     * @param detailId          详情ID
     * @param annotationType    标注类型
     * @param annotationSubtype 标注子类型
     */
    void markAnnotation(String detailId, String annotationType, String annotationSubtype);

    /**
     * 重新标注（解锁）
     *
     * @param detailId 详情ID
     * @return 是否解锁成功
     */
    Boolean unlockAnnotation(String detailId);

    /**
     * 根据任务ID查询所有标注数据用于导出
     *
     * @param taskId 任务ID
     * @return 标注数据列表
     */
    List<FaqAnnotationDetailPO> getAnnotationDataForExport(String taskId);

    /**
     * 删除任务的所有标注详情
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    Boolean deleteByTaskId(String taskId);

}
