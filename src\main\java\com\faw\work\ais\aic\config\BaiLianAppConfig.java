package com.faw.work.ais.aic.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Configuration
@Data
@RefreshScope
@Slf4j
public class BaiLianAppConfig {

    public static final int SIX = 6;
    @Value("${dashscope.dms.workspace:}")
    private String emotionWorkspaceId;
    @Value("${dashscope.dms.emotion.apiKey:}")
    private String emotionApiKey;
    @Value("${dashscope.dms.emotion.analysis-appId:}")
    private String emotionAnalysisAppId;
    @Value("${dashscope.dms.emotion.tag-appId:}")
    private String emotionTagAppId;
    @Value("${dashscope.dms.emotion.topic-appId:}")
    private String emotionTopicAppId;

    @Value("${dashscope.lingxiaoxi.workspace:}")
    private String lingxiaoxiWorkspaceId;
    @Value("${dashscope.lingxiaoxi.chat.apiKey:}")
    private String lingxiaoxiChatApiKey;
    @Value("${dashscope.lingxiaoxi.chat.reQuery-appId:}")
    private String lingxiaoxiReQueryAppId;

    /**
     * 在Spring Boot启动完成后打印配置信息
     */
    @PostConstruct
    public void printConfigInfo() {
        log.info("=== BaiLian App Configuration ===");
        log.info("Emotion Workspace ID: {}", emotionWorkspaceId);
        log.info("Emotion API Key: {}", maskApiKey(emotionApiKey));
        log.info("Emotion Analysis App ID: {}", emotionAnalysisAppId);
        log.info("Emotion Tag App ID: {}", emotionTagAppId);
        log.info("Emotion Topic App ID: {}", emotionTopicAppId);
        log.info("Lingxiaoxi Workspace ID: {}", lingxiaoxiWorkspaceId);
        log.info("Lingxiaoxi Chat API Key: {}", maskApiKey(lingxiaoxiChatApiKey));
        log.info("Lingxiaoxi ReQuery App ID: {}", lingxiaoxiReQueryAppId);
        log.info("=== Configuration Loaded Successfully ===");
    }

    /**
     * 对API Key进行掩码处理，后6位用*替换
     * @param apiKey 原始API Key
     * @return 掩码后的API Key
     */
    private String maskApiKey(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            return "[NOT_CONFIGURED]";
        }

        if (apiKey.length() <= SIX) {
            // 如果长度小于等于6位，全部用*替换
            return "*".repeat(apiKey.length());
        } else {
            // 保留前面的字符，后6位用*替换
            String prefix = apiKey.substring(0, apiKey.length() - 6);
            return prefix + "******";
        }
    }
}