package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * FAQ标注任务实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("faq_annotation_task")
@Accessors(chain = true)
public class FaqAnnotationTaskPO {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("task_name")
    private String taskName;

    @TableField("robot_id")
    private String robotId;

    @TableField("robot_name")
    private String robotName;

    @TableField("data_source")
    private String dataSource;

    @TableField("call_type")
    private String callType;

    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("status")
    private String status;

    @TableField("total_count")
    private Integer totalCount;

    @TableField("annotated_count")
    private Integer annotatedCount;

    @TableField("creator_id")
    private String creatorId;

    @TableField("creator_name")
    private String creatorName;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("completed_at")
    private LocalDateTime completedAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
