package com.faw.work.ais.common.dto.chat;

import com.faw.work.ais.common.enums.chat.ChatSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 智能聊天 响应参数类
 *
 * <AUTHOR>
 * @since 2025-05-30 10:03
 */
@Data
@Builder
@Schema(description = "智能聊天 响应参数类")
public class AiChatResponse {

    /**
     * 聊天ID
     */
    @Schema(description = "聊天ID")
    private String chatId;

    /**
     * 聊天内容
     */
    @Schema(description = "聊天内容")
    private String content;

    /**
     * 思维链
     */
    @Schema(description = "思维链")
    private String thoughtChain;

    /**
     * 答案来源标识（"0"-大模型、"1"-知识库）
     */
    @Schema(description = "答案来源标识")
    private String answerSource;

    /**
     * 场景标识 {@link ChatSceneEnum}
     */
    @Schema(description = "场景标识")
    private String sceneCode;

    /**
     * 结束原因
     */
    @Schema(description = "结束原因")
    private String finishReason;

    /**
     * 参数
     */
    @Schema(description = "参数")
    private Object params;

}
