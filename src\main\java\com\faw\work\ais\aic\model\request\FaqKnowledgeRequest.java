package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FAQ知识请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ知识请求对象")
public class FaqKnowledgeRequest {
    @Schema(description = "类目ID")
    private String categoryId;

    @Schema(description = "主键ID")
    private String id;
    
    @NotBlank(message = "FAQ题目不能为空")
    @Schema(description = "FAQ题目")
    private String question;
    
    @NotBlank(message = "答案不能为空")
    @Schema(description = "答案")
    private String answer;
    
    @Schema(description = "命中次数")
    private Long hitCount;

    @Schema(description = "类目")
    @NotBlank(message = "类目不能为空")
    private String categoryName;

    @Schema(description = "查询数量")
    @NotBlank(message = "查询数量")
    private Integer topK;

}
