package com.faw.work.ais.common.dto.iwork;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-07-31 11:11
 * 云原生基础服务返回user信息
 */
@Schema(description = "云原生基础服务返回user信息")
@Data
public class CloudNativeUserDTO {

    @Schema(description = "登录名")
    private String loginName;

    @Schema(description = "使")
    private Integer enable;

    @Schema(description = "删除")
    private Integer deleted;

    @Schema(description = "名字")
    private String name;

}
