<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqCategoryMapper">


    <select id="selectCategoryListByName" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        select *
        from faq_category
        where name like #{categoryName}
    </select>
    <select id="selectByNameLike" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        SELECT * FROM faq_category
        where 1=1
        <if test="name!= null and name!= ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
    </select>

    <select id="selectAllCategories" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        SELECT *
        FROM faq_category
    </select>
    <select id="selectByParentId" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        SELECT *
        FROM faq_category
        WHERE parent_id = #{parentId}
    </select>
    <select id="findAllRootCategoryList" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        <choose>
            <when test="env=='test'">select * FROM faq_category</when>
            <when test="env=='prod'">select * FROM faq_category_prod</when>
        </choose>
        WHERE parent_id IS NULL
        ORDER BY created_at ASC <!-- 或者其他您希望的排序方式 -->
    </select>
    <select id="findChildrenByNameAndParentId" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        SELECT *
        FROM faq_category
        WHERE parent_id = #{parentId,jdbcType=BIGINT}
        ORDER BY created_at ASC <!-- 或者其他您希望的排序方式 -->
    </select>
    <select id="findChildrenByParentId" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        SELECT *
        <choose>
        	<when test="env=='test'">FROM faq_category</when>
            <when test="env=='prod'">FROM faq_category_prod</when>
        </choose>
        WHERE parent_id = #{parentId,jdbcType=BIGINT}
    </select>
    <select id="selectProdCategoryListByIds" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        SELECT *
        FROM faq_category_prod
        WHERE id IN
        <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>