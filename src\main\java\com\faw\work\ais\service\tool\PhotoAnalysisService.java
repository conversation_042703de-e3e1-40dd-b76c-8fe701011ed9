package com.faw.work.ais.service.tool;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.MessageEntity;
import com.faw.work.ais.common.dto.chat.PhotoAnalysisRequest;
import com.faw.work.ais.common.dto.chat.PhotoAnalysisResponse;
import com.faw.work.ais.feign.chat.AliYunFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 图片解析服务类
 *
 * <AUTHOR>
 * @since 2025-04-21 16:57
 */
@Slf4j
public class PhotoAnalysisService {

    private final AliYunFeignClient aliYunFeignClient;


    public PhotoAnalysisService(AliYunFeignClient aliYunFeignClient) {
        this.aliYunFeignClient = aliYunFeignClient;
    }


    private static final String TYPE = "type";

    private static final String TEXT = "text";

    private static final String IMAGE_URL = "image_url";

    private static final String USER = "user";

    private static final String MODEL = "qwen-vl-max";


    @Tool(name = "analyzePhoto", description = "图片解析方法：根据提供的图片连接和聊天记录解析图片内容")
    public String analyzePhoto(@ToolParam(description = "图片连接") List<String> photoUrls,
                               @ToolParam(description = "用户和AI最近两轮谈话内容") String chatHistory) {
        Instant start = Instant.now();
        log.info("[PhotoAnalysisService][analyzePhoto][entrance] photoUrl: {}, chatHistory: {}", JSON.toJSONString(photoUrls), chatHistory);

        List<Map<String, Object>> content = new ArrayList<>();

        content.add(Map.of(TYPE, TEXT, TEXT, chatHistory));
        for (String photoUrl : photoUrls) {
            content.add(Map.of(TYPE, IMAGE_URL, IMAGE_URL, photoUrl));
        }

        MessageEntity message = new MessageEntity();
        message.setRole(USER);
        message.setContent(content);

        PhotoAnalysisRequest request = new PhotoAnalysisRequest();
        request.setModel(MODEL);
        request.setMessages(List.of(message));

        PhotoAnalysisResponse response = aliYunFeignClient.analyzePhoto(request);
        log.info("[PhotoAnalysisService][analyzePhoto] cost: {} ms, response: {}", Duration.between(start, Instant.now()).toMillis(), JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }

        return "解析图片内容：" + JSON.toJSONString(photoUrls) + "，用户想要咨询的问题：" + chatHistory;
    }

}
