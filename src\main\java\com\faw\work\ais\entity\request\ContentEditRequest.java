package com.faw.work.ais.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 帖子请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "规则修改请求对象")
public class ContentEditRequest {


    @NotNull(message = "主键不能为空")
    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "类型不能为空")
    @Schema(description = "类型：1评论、2动态")
    private int type;

    @NotNull(message = "状态不能为空")
    @Schema(description = "状态：1启用、2禁用")
    private int status;

    @Schema(description = "规则")
    private String rule;

}
