package com.faw.work.ais.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * ugc服务相关配置
 */
@Configuration
@ConfigurationProperties(prefix = "ugcprodconfig")
@RefreshScope
@Data
public class UgcProdConfig {


    @Schema(description = "appKey")
    private String prodAppKey;

    @Schema(description = "appSecret")
    private String prodAppSecret;

    @Schema(description = "请求地址")
    private String prodHostUrl;



}
