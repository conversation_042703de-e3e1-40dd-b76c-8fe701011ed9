package com.faw.work.ais.service.tool;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.PromptConstants;
import com.faw.work.ais.common.dto.chat.StaffServiceEntity;
import com.faw.work.ais.common.dto.chat.ToolCacheEntity;
import com.faw.work.ais.common.enums.chat.ChatToolEnum;
import com.faw.work.ais.config.chat.ThreadContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 功能跳转服务类，用于实现功能跳转
 *
 * <AUTHOR>
 * @since 2025-06-24 11:13
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FunctionService {

    private final RedisService redisService;

    private final ChatClient universalClient;


    private static final String JUMP_FROM = "lingxiaoxi";

    private static final String STAFF_SERVICE_MESSAGE = """
            请点击下方入口，直达在线客服；
            或者拨打人工坐席电话：<el-tel tel='************'></el-tel>
            """;


    @Tool(name = "staffService", returnDirect = true, description = "人工客服方法：当用户提及人工客服、在线客服时执行")
    public String staffService(@ToolParam(description = "用户和AI最近两轮谈话内容") String chatHistory,
                               ToolContext toolContext) {
        log.info("[FunctionService][staffService][entrance] chatHistory: {}, toolContext: {}", chatHistory, JSON.toJSONString(toolContext));

        ToolCacheEntity toolCache = ToolCacheEntity.builder()
                .toolName(ChatToolEnum.STAFF_SERVICE.getName()).toolStatus(true)
                .toolValue(this.buildStaffServiceEntity(chatHistory)).build();

        String thoughtChain = ThreadContext.get();
        if (StringUtils.isNotEmpty(thoughtChain)) {
            toolCache.setThoughtChain(thoughtChain);
            ThreadContext.clear();
        }

        redisService.set((String) toolContext.getContext().get(CommonConstants.KEY_CACHE_ID), JSON.toJSONString(toolCache), CommonConstants.SECONDS_ONE_HOUR);

        return STAFF_SERVICE_MESSAGE;
    }

    /**
     * 构建客服跳转实体
     *
     * @param chatHistory 用户和AI最近两轮谈话内容
     * @return 客服跳转实体
     */
    public StaffServiceEntity buildStaffServiceEntity(String chatHistory) {
        log.info("[FunctionService][buildStaffServiceEntity][entrance] chatHistory: {}", chatHistory);

        StaffServiceEntity entity = StaffServiceEntity.builder().jumpFrom(JUMP_FROM).sentence(chatHistory).build();

        if (chatHistory != null && chatHistory.length() > CommonConstants.FIFTY) {
            entity.setSentence(universalClient
                    .prompt(String.format(PromptConstants.SEMANTIC_SUMMARY_PROMPT, chatHistory))
                    .call().content());
        }

        entity.setSkillGroup(universalClient
                .prompt(String.format(PromptConstants.SKILL_GROUP_PROMPT, chatHistory))
                .call().content());

        return entity;
    }

}
