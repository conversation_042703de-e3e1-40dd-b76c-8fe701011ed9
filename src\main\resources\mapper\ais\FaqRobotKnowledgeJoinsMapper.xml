<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqRobotKnowledgeJoinsMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, robot_id, knowledge_id, created_at
    </sql>

    <!-- 批量保存机器人知识关联 -->
    <insert id="bindKnowledge">
        INSERT INTO faq_robot_knowledge_joins (id, robot_id, knowledge_id, source, created_at)
        VALUES
        <foreach collection="knowledgeIds" item="knowledgeId" separator=",">
            (REPLACE(UUID(), '-', ''), #{robotId}, #{knowledgeId}, #{source}, NOW())
        </foreach>
    </insert>


    <!-- 根据机器人ID删除关联 -->
    <delete id="deleteByRobotId">
        DELETE FROM faq_robot_knowledge_joins
        WHERE robot_id = #{robotId}
    </delete>
    <delete id="deleteByKnowledgeCategoryId">
        DELETE FROM faq_robot_knowledge_joins where category_id = #{categoryId}
    </delete>
    <delete id="deleteByKnowledgeIds">
        DELETE FROM faq_robot_knowledge_joins WHERE knowledge_id IN
        <foreach collection="allKnowledgeIds" item="allKnowledgeId" separator="," open="(" close=")">
            #{allKnowledgeId}
        </foreach>
    </delete>
    <delete id="deleteProdByRobotId">
        DELETE FROM faq_robot_knowledge_joins_prod
        WHERE robot_id = #{robotId}
    </delete>

    <!-- 根据机器人ID查询知识列表 -->
    <select id="selectKnowledgesByRobotId" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT k.*
        FROM faq_knowledge k
        INNER JOIN faq_robot_knowledge_joins j ON k.id = j.knowledge_id
        WHERE j.robot_id = #{robotId}
        ORDER BY k.updated_at DESC
    </select>

    <!-- 根据机器人ID查询所有关联的ID -->
    <select id="selectIdsByRobotId" resultType="java.lang.String">
        SELECT id FROM faq_robot_knowledge_joins WHERE robot_id = #{robotId}
    </select>
    <select id="selectByKnowledgeIds" resultType="com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO">
        SELECT * FROM faq_robot_knowledge_joins WHERE knowledge_id IN
        <foreach collection="knowledgeIds" item="knowledgeId" open="(" separator="," close=")">
            #{knowledgeId}
        </foreach>
    </select>

    <select id="selectRobotIdsByCategoryId" resultType="java.lang.String">
        SELECT DISTINCT j.robot_id
        FROM faq_robot_knowledge_joins j
                 LEFT JOIN faq_knowledge k ON j.knowledge_id = k.id
        WHERE k.category_id = #{categoryId}
    </select>
    <select id="selectCategoryIdsByRobotId" resultType="java.lang.String">
        SELECT DISTINCT k.category_id FROM faq_robot_knowledge_joins j
        INNER JOIN faq_knowledge k ON j.knowledge_id = k.id
        WHERE j.robot_id = #{robotId}
    </select>
    <select id="selectProdIdsByRobotId" resultType="java.lang.String">
        SELECT id FROM faq_robot_knowledge_joins_prod WHERE robot_id = #{robotId}

    </select>
    <select id="selectProdCategoryIdsByRobotId" resultType="java.lang.String">
        SELECT DISTINCT k.category_id FROM faq_robot_knowledge_joins_prod j
                                               INNER JOIN faq_knowledge_prod k ON j.knowledge_id = k.id
        WHERE j.robot_id = #{robotId}
    </select>
    <select id="selectProdJoinIdWithQuestionMapByJoinIds"
            resultType="com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO">
        SELECT
            frkj.id AS id,
            fk.question AS question -- 来自 faq_knowledge 的问题
        FROM
            faq_robot_knowledge_joins_prod frkj
                INNER JOIN
            faq_knowledge_prod fk ON frkj.knowledge_id = fk.id
        WHERE
            frkj.source = 'original'
          AND frkj.id IN
            <foreach collection="intersectionJoinIds" item="intersectionJoinId" separator=",">
                #{intersectionJoinId}
            </foreach>

        UNION ALL

        SELECT
            frkj.id AS id,
            fsk.similar_question AS question -- 来自 faq_similar_knowledge 的相似问题
        FROM
            faq_robot_knowledge_joins_prod frkj
                INNER JOIN
            faq_similar_knowledge_prod fsk ON frkj.knowledge_id = fsk.id
        WHERE
            frkj.source = 'similar';
        and frkj.id IN
            <foreach collection="intersectionJoinIds" item="intersectionJoinId" separator=",">
                #{intersectionJoinId}
            </foreach>
    </select>
    <select id="selectByRobotId" resultType="com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO">
        SELECT * FROM faq_robot_knowledge_joins WHERE 1=1
        <if test="robotId != null and robotId != ''">
        	AND robot_id = #{robotId}
        </if>
        <if test="knowledgeId != null and knowledgeId != ''">
            AND knowledge_id = #{knowledgeId}
        </if>
    </select>
    <select id="selectDistinctRobotId" resultType="java.lang.String">
        SELECT DISTINCT robot_id FROM faq_robot_knowledge_joins where knowledge_id = #{knowledgeId}
    </select>
    <select id="selectDistinctRobotIdsByKnowledgeIds" resultType="java.lang.String">
        SELECT DISTINCT robot_id FROM faq_robot_knowledge_joins where knowledge_id IN
        <foreach collection="similarIds" item="similarId" separator=",">
            #{similarId}
        </foreach>
    </select>
    <select id="selectByRobotIdAndKnowledgeIds"
            resultType="com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO">
        SELECT * FROM faq_robot_knowledge_joins
        WHERE robot_id = #{robotId}
        AND knowledge_id IN (
        <foreach collection="allKnowledgeIds" item="allKnowledgeId" separator=",">
            #{allKnowledgeId}
        </foreach>
        )
    </select>
    <select id="findKnowledgeIdsByRobotId" resultType="java.lang.String">
        SELECT
        j.knowledge_id
        <choose>
            <when test="env=='test'">
                FROM faq_robot_knowledge_joins j
                INNER JOIN faq_knowledge k ON j.knowledge_id = k.id
            </when>
            <when test="env=='prod'">
                FROM faq_robot_knowledge_joins_prod j
                INNER JOIN faq_knowledge_prod k ON j.knowledge_id = k.id
            </when>
        </choose>
        WHERE
        j.robot_id = #{robotId}
        AND j.source = 'original'  -- 只关注原始知识
        AND (
        k.effectiveType = '00'  -- 永久有效
        OR
        (k.effectiveType = '01'  -- 临时有效且在有效期内
        AND NOW() BETWEEN
        TO_DATE(k.effectiveStartTime, '%Y-%m-%d %H:%i:%s')
        AND TO_DATE(k.effectiveEndTime, '%Y-%m-%d %H:%i:%s')
        )
        )

    </select>
    <select id="selectByKnowledgeId" resultType="com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO">
        SELECT * FROM faq_robot_knowledge_joins WHERE knowledge_id = #{knowledgeId}
    </select>

</mapper> 