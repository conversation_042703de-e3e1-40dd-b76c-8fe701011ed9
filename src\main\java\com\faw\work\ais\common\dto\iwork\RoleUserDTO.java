package com.faw.work.ais.common.dto.iwork;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 云原生用户及子角色用户信息
 * <AUTHOR>
 */
@Schema(description = "云原生用户及子角色用户信息")
@Data
public class RoleUserDTO {

    @Schema(description = "同上")
    private String id;

    @Schema(description = "法典")
    private String code;

    @Schema(description = "名字")
    private String name;

    @Schema(description = "多语言描述")
    private String multilingualDesc;

    @Schema(description = "角色组 ID")
    private String roleGroupId;

    @Schema(description = "角色组名称")
    private String roleGroupName;

    @Schema(description = "系统 ID")
    private String systemId;

    @Schema(description = "父角色 ID")
    private String parentRoleId;

    @Schema(description = "父角色 ID")
    private String parentRoleIds;

    @Schema(description = "用户")
    private List<CloudNativeUserDTO> users;

    @Schema(description = "孩子")
    private List<RoleUserDTO> children;

}
