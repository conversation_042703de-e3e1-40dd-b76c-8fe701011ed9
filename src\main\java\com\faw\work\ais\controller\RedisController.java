package com.faw.work.ais.controller;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.common.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@RestController
@RequestMapping("/redis")
@Tag(name = "缓存", description = "缓存相关接口")
@Slf4j
@RequiredArgsConstructor
public class RedisController {

    private final RedisService redisService;
    @GetMapping("/delete/{key}")
    @Operation(summary = "删除缓存键", description = "[author:10207439]")
    public Response<Boolean> delete(@PathVariable("key") @Parameter(description = "缓存键") String key) {
        redisService.delete(key);
        return Response.success(Boolean.TRUE);
    }

    @GetMapping("/get/{key}")
    @Operation(summary = "获取缓存值", description = "[author:10207439]")
    public Response<String> get(@PathVariable("key") @Parameter(description = "缓存键") String key) {
        Object res = redisService.get(key);
        return Response.success(JSON.toJSONString(res));
    }

    @PostMapping("/set/{key}")
    @Operation(summary = "设置缓存值", description = "[author:10207439]")
    public Response<Boolean> set(
            @PathVariable("key") @Parameter(description = "缓存键") String key,
            @RequestBody @Parameter(description = "缓存值") String value,
            @RequestParam(required = false) @Parameter(description = "过期时间(秒)") Long expire) {
        if (expire != null && expire > 0) {
            redisService.set(key, value, expire);
        } else {
            redisService.set(key, value);
        }
        return Response.success(Boolean.TRUE);
    }

    @GetMapping("/hasKey/{key}")
    @Operation(summary = "检查键是否存在", description = "[author:10207439]")
    public Response<Boolean> hasKey(@PathVariable("key") @Parameter(description = "缓存键") String key) {
        return Response.success(redisService.hasKey(key));
    }

    @GetMapping("/getExpire/{key}")
    @Operation(summary = "获取键的剩余时间", description = "[author:10207439]")
    public Response<Long> getExpire(@PathVariable("key") @Parameter(description = "缓存键") String key) {
        return Response.success(redisService.getExpire(key));
    }

    @PostMapping("/increment/{key}")
    @Operation(summary = "递增键的值", description = "[author:10207439]")
    public Response<Long> increment(
            @PathVariable("key") @Parameter(description = "缓存键") String key,
            @RequestParam(defaultValue = "1") @Parameter(description = "递增步长") long delta) {
        return Response.success(redisService.incr(key, delta));
    }

    @PostMapping("/decrement/{key}")
    @Operation(summary = "递减键的值", description = "[author:10207439]")
    public Response<Long> decrement(
            @PathVariable("key") @Parameter(description = "缓存键") String key,
            @RequestParam(defaultValue = "1") @Parameter(description = "递减步长") long delta) {
        return Response.success(redisService.decr(key, delta));
    }

    @GetMapping("/keys/{pattern}")
    @Operation(summary = "获取匹配的键列表", description = "[author:10207439]")
    public Response<Set<String>> keys(
            @PathVariable("pattern") @Parameter(description = "键模式，如: 'prefix*'") String pattern) {
        return Response.success(redisService.keys(pattern));
    }

    @PostMapping("/expire/{key}")
    @Operation(summary = "设置键的过期时间", description = "[author:10207439]")
    public Response<Boolean> expire(
            @PathVariable("key") @Parameter(description = "缓存键") String key,
            @RequestParam @Parameter(description = "过期时间(秒)") long expire) {
        return Response.success(redisService.expire(key, expire));
    }

}
