package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * FAQ机器人知识关联实体类
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("faq_robot_knowledge_joins")
public class FaqRobotKnowledgeJoinsPO {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 机器人ID
     */
    private String robotId;

    
    /**
     * 知识ID
     */
    private String knowledgeId;
    
    /**
     * 来源类型 (original-faq_knowledge/similar-similar_question)
     */
    private String source;



    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 向量化状态（01-未向量化，02-向量化成功 10-向量化失败）
     */
    private String vectorStatus;

    /**
     * 问题
     */
    @TableField(exist = false)
    private String question;
} 