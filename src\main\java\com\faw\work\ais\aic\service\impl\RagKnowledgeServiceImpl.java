package com.faw.work.ais.aic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.mapper.rag.RagKnowledgeMapper;
import com.faw.work.ais.aic.model.domain.RagKnowledgePO;
import com.faw.work.ais.aic.model.request.RagKnowledgeBasePageRequest;
import com.faw.work.ais.aic.service.RagKnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * RAG知识库配置表 服务实现类
 *
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RagKnowledgeServiceImpl extends ServiceImpl<RagKnowledgeMapper, RagKnowledgePO> implements RagKnowledgeService {

    @Override
    public List<RagKnowledgePO> getKnowledgeBaseList(RagKnowledgePO knowledgeBase) {
        LambdaQueryWrapper<RagKnowledgePO> queryWrapper = new LambdaQueryWrapper<>();

        if (knowledgeBase != null) {
            // 按条件查询
            if (knowledgeBase.getId() != null) {
                queryWrapper.eq(RagKnowledgePO::getId, knowledgeBase.getId());
            }

            if (StringUtils.hasText(knowledgeBase.getName())) {
                queryWrapper.like(RagKnowledgePO::getName, knowledgeBase.getName());
            }

            if (StringUtils.hasText(knowledgeBase.getDataType())) {
                queryWrapper.eq(RagKnowledgePO::getDataType, knowledgeBase.getDataType());
            }
        }

        // 按ID降序排序
        queryWrapper.orderByDesc(RagKnowledgePO::getId);

        return this.list(queryWrapper);
    }

    @Override
    public Page<RagKnowledgePO> getKnowledgeBasePage(RagKnowledgeBasePageRequest request) {
        Page<RagKnowledgePO> page = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<RagKnowledgePO> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (request.getId() != null) {
            queryWrapper.eq(RagKnowledgePO::getId, request.getId());
        }

        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(RagKnowledgePO::getName, request.getName());
        }

        if (StringUtils.hasText(request.getDataType())) {
            queryWrapper.eq(RagKnowledgePO::getDataType, request.getDataType());
        }

        // 按ID降序排序
        queryWrapper.orderByDesc(RagKnowledgePO::getId);

        return this.page(page, queryWrapper);
    }

    @Override
    public RagKnowledgePO getByName(String name) {
        if (!StringUtils.hasText(name)) {
            return null;
        }
        
        LambdaQueryWrapper<RagKnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagKnowledgePO::getName, name);
        return this.getOne(queryWrapper);
    }
} 