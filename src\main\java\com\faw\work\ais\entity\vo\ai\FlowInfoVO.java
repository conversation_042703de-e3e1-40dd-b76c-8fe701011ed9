package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "流程信息")
@Data
public class FlowInfoVO {

    /**
     * 层级
     */
    @Schema(description = "层级")
    private String level;

    /**
     * 流程编码
     */
    @Schema(description = "l3流程编码")
    private String l3flowCode;

    /**
     * 流程名称
     */
    @Schema(description = "l3流程名称")
    private String l3flowName;

}
