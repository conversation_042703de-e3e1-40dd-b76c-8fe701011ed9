package com.faw.work.ais.aic.feign;

import com.faw.work.ais.aic.feign.dto.ApiResponse;
import com.faw.work.ais.aic.feign.dto.DgwResult;
import com.faw.work.ais.aic.feign.dto.KnowledgeFileInfoResponse;
import com.faw.work.ais.aic.feign.dto.KnowledgeNewInfoRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "DmsKnowledgeFeignClient", url = "${aic.config.ucg.dms-knowledge-center-host}")
public interface DmsKnowledgeFeignClient {

    /**
     * 知识列表获取
     *
     * @param request 要求
     * @return {@link DgwResult }
     */
    @PostMapping("/openapi-minerva/minerva/knowledgeInput/getPdfFileList")
    ApiResponse<List<KnowledgeFileInfoResponse>> getFileList(@RequestBody KnowledgeNewInfoRequest request);

}