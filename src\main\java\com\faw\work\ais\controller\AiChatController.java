package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.common.dto.chat.*;
import com.faw.work.ais.service.AiChatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 灵小犀模型 控制类
 *
 * <AUTHOR>
 * @since 2025-04-03 14:26
 */
@Schema(description = "灵小犀模型 控制类")
@RestController
@RequestMapping("/aiChat")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiChatController {

    private final AiChatService aiChatService;


    @Operation(summary = "连续会话（流式对象）", description = "[author:10236535]")
    @PostMapping(value = "/continuousSession", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<AiChatResponse> continuousSession(@RequestBody AiChatRequest request) {
        return aiChatService.continuousSession(request);
    }

    @Operation(summary = "语音转文字", description = "[author:10236535]")
    @PostMapping(value = "/voiceToText")
    public Result<VoiceToTextEntity> voiceToText(@RequestParam("file") MultipartFile file) {
        return aiChatService.voiceToText(file);
    }

    @Operation(summary = "语音转文字纠正", description = "[author:10236535]")
    @PostMapping(value = "/voiceToTextRectify")
    public String voiceToTextRectify(@RequestParam("file") MultipartFile file) {
        return aiChatService.voiceToTextRectify(file);
    }

    @Operation(summary = "保存会话记录", description = "[author:10236535]")
    @PostMapping(value = "/saveSessionRecord")
    public Result<AppChatHistoryDto> saveSessionRecord(@RequestBody AppChatHistoryDto request) {
        return aiChatService.saveSessionRecord(request);
    }

    @Operation(summary = "查询会话记录", description = "[author:10236535]")
    @PostMapping(value = "/querySessionRecord")
    public Result<List<AppChatHistoryDto>> querySessionRecord(@RequestBody AppChatHistoryDto request) {
        return aiChatService.querySessionRecord(request);
    }

    @Operation(summary = "查询会话详情", description = "[author:10236535]")
    @PostMapping(value = "/querySessionDetail")
    public Result<List<AppChatHistoryDetailDto>> querySessionDetail(@RequestBody AppChatHistoryDto request) {
        return aiChatService.querySessionDetail(request);
    }

    @Operation(summary = "删除会话记录", description = "[author:10236535]")
    @PostMapping(value = "/deleteSessionRecord")
    public Result<String> deleteSessionRecord(@RequestBody AppChatHistoryDto request) {
        return aiChatService.deleteSessionRecord(request);
    }

}
