package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文档类目响应
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "文档类目响应")
public class RagCategoryResponse {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "类目名称")
    private String name;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 