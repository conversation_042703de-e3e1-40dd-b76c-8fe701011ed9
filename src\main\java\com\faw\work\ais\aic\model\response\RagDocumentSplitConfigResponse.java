package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文本分段配置响应
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "文本分段配置响应")
public class RagDocumentSplitConfigResponse {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "文档id")
    private Long documentId;

    @Schema(description = "分段策略(00-智能切分 01-自定义切分)")
    private String chunkStrategy;

    @Schema(description = "分段标识符(默认双换行符\\n，。！等)")
    private String chunkSeparator;

    @Schema(description = "分段最大长度(tokens)")
    private Integer chunkLength;

    @Schema(description = "分块大小(字符数)")
    private Integer chunkSize;

    @Schema(description = "分段重叠长度(tokens)")
    private Integer overlapLength;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 