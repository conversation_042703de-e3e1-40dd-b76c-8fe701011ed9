package com.faw.work.ais.common.util;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import com.faw.work.ais.common.enums.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;

/**
 * 日期工具类
 */
@Slf4j
public class DateUtils {
    /**
     * 时间格式字符串：yyyy-MM-dd
     */
    public static final String FORMAT_DATE = "yyyy-MM-dd";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String FORMAT_DATE_OTHER = "yyyyMMdd";

    /**
     * 时间格式字符串：yyyy.MM.dd
     */
    public static final String FORMAT_DATE_POINT = "yyyy.MM.dd";

    /**
     * 时间格式字符串：yyyy-MM-dd HH:mm:ss
     */
    public static final String FORMAT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时间格式字符串：yyyy-MM
     */
    public static final String FORMAT_DATE_YEAR_MONTH = "yyyy-MM";

    /**
     * 时间格式字符串：yyyyMM
     */
    public static final String FORMAT_DATE_YEAR_MONTH_NOSEGMENT = "yyyyMM";

    /**
     * 时间格式字符串：yyyy.MM
     */
    public static final String FORMAT_DATE_YEAR_MONTH_POINT = "yyyy.MM";

    /**
     * 时间格式字符串：MM
     */
    public static final String FORMAT_DATE_MONTH_POINT = "MM";

    /**
     * 时间格式字符串：yyyy
     */
    public static final String FORMAT_DATE_YEAR_POINT = "yyyy";
    public static final String STARTDATE = "startDate";

    public static final String ENDDATE = "endDate";


    /**
     * 根据日期获取星期
     *
     * @param dateString 日期 格式 yyyy-MM-dd
     * @return 星期
     */
    public static String getWeekOfDate(String dateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        LocalDate date = LocalDate.parse(dateString, formatter);
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek.getDisplayName(TextStyle.FULL, Locale.CHINESE);
    }

    /**
     * 取得指定日期格式的字符串
     *
     * @return String
     */
    public static String formatDate(Date date, String format) {
        if (Objects.isNull(date) || StringUtils.isEmpty(format)) {
            return null;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat(format);

        return dateFormat.format(date);
    }

    public static Date now() {
        return new Date();
    }

    /**
     * 获取指定时间
     *
     * @param currentTime 基准时间
     * @param days        前(-) 或 后(+) 天数
     * @return 指定时间(YYYY - MM - DD)
     */
    public static String getAppointDate(String currentTime, Integer days) {
        // 创建 Calendar 对象并设置为当前时间
        Calendar calendar = Calendar.getInstance();

        // 将字符串转换成 Date 格式
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE);
        Date date;
        try {
            date = sdf.parse(currentTime);
        } catch (ParseException e) {
            throw new RuntimeException(ErrorCodeEnum.DATA_EXCEPTION.getDesc());
        }

        // 将 Date 设置到 Calendar 对象上
        calendar.setTime(date);

        // 在 Calendar 对象上进行操作，获取前一天日期
        calendar.add(Calendar.DAY_OF_MONTH, days);

        // 从 Calendar 对象中获取前一天日期
        Date previousDay = calendar.getTime();

        // 输出结果
        return sdf.format(previousDay);
    }

    public static Date getDateByString(String dateString) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = format.parse(dateString);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static List<LocalDate> getDatesBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            dates.add(startDate);
            startDate = startDate.plusDays(1);
        }
        return dates;
    }

    public static LocalDate convertDateToLocalDate(String sDate) {
        SimpleDateFormat inputFormat = new SimpleDateFormat(DateUtils.FORMAT_DATE);
        Date date = null;
        try {
            date = inputFormat.parse(sDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

}
