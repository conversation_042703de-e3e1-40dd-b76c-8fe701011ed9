package com.faw.work.ais.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;


@Configuration
public class NumberEmployeeThreadPoolConfig {
    private final NumberEmployeeThreadPoolProperties properties;

    public NumberEmployeeThreadPoolConfig(NumberEmployeeThreadPoolProperties properties) {
        this.properties = properties;
    }

    @Bean
    public ThreadPoolExecutor threadPoolExecutor() {
        return new ThreadPoolExecutor(
                properties.getCorePoolSize(),// 核心线程数
                properties.getMaxPoolSize(),// 最大线程数
                properties.getKeepAliveTime(),// 空闲线程存活时间
                TimeUnit.SECONDS, // 时间单位
                new ArrayBlockingQueue<>(properties.getQueueCapacity()), // 工作队列
                Executors.defaultThreadFactory(), // 线程工厂
                new ThreadPoolExecutor.AbortPolicy() // 拒绝策略
        );
    }
}
