package com.faw.work.ais.mapper.chat;

import com.faw.work.ais.model.chat.AppChatHistory;

import java.util.List;

/**
 * 智能问答聊天记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-04-27 10:06
 */
public interface AppChatHistoryMapper {

    /**
     * 新增智能问答聊天记录
     *
     * @param po 智能问答聊天记录
     * @return 影响行数
     */
    int insert(AppChatHistory po);

    /**
     * 更新智能问答聊天记录
     *
     * @param po 智能问答聊天记录
     * @return 影响行数
     */
    int update(AppChatHistory po);

    /**
     * 查询智能问答聊天记录
     *
     * @param po 查询条件
     * @return 智能问答聊天记录
     */
    List<AppChatHistory> selectList(AppChatHistory po);

}
