package com.faw.work.ais.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.logging.stdout.StdOutImpl;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;


@Configuration
@MapperScan(basePackages = "com.faw.work.ais.mapper.bupcore", sqlSessionFactoryRef = "bupcoreSqlSessionFactory")
public class DataSourceBupCoreConfig {

    @Bean(name = "bupcoreDatasource")
    @ConfigurationProperties(prefix = "spring.datasource.bupcore")
    public DataSource bupcoreDatasource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "bupcoreSqlSessionFactory")
    public SqlSessionFactory bupcoreSqlSessionFactory(@Qualifier("bupcoreDatasource") DataSource dataSource)
            throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setCallSettersOnNulls(true);
        configuration.setLogImpl(StdOutImpl.class);
        bean.setDataSource(dataSource);
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources("classpath:mapper/bupcore/**.xml");
        bean.setMapperLocations(resources);
        bean.setPlugins(bupcoreMybatisPlusInterceptor());
        return bean.getObject();
    }

    /**
     * 数据源事务配置
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "bupcoreTransactionManager")
    public DataSourceTransactionManager bupcoreTransactionManager(@Qualifier("bupcoreDatasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "bupcoreSqlSessionTemplate")
    public SqlSessionTemplate bupcoreSqlSessionTemplate(@Qualifier("bupcoreSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean
    public MybatisPlusInterceptor bupcoreMybatisPlusInterceptor(){
        MybatisPlusInterceptor interceptor=new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

}