<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.RightTaskRuleMapper">

    <select id="getSampleTaskRuleInfo" resultType="com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleVO">
        SELECT
        SUM(ai_pass) AS aiPassHumanPassNum,
        SUM(human_reject) AS aiPassHumanUnPassNum,
        SUM(human_pass) AS aiUnPassHumanPassNum,
        SUM(ai_reject) AS aiUnPassHumanUnPassNum,
        (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)) AS sampleTotalNum,
        ROUND((SUM(ai_pass) + SUM(ai_reject)) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS sampleRightRate,
        ROUND(SUM(ai_pass) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiPassHumanPassRate,
        ROUND(SUM(human_reject) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiPassHumanUnpassRate,
        ROUND(SUM(human_pass) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiUnpassHumanPassRate,
        ROUND(SUM(ai_reject) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiUnpassHumanUnpassRate
        FROM
        rigth_task_rule hr
        <where>
            <if test = " numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                AND hr.check_date BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
            </if>
            <if test = "bizTypes != null and bizTypes.size > 0 ">
                AND hr.biz_type IN
                <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
                    #{bizType}
                </foreach>
            </if>
            <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                AND hr.system_id = #{numberEmployeeDTO.systemId}
            </if>
        </where>
    </select>
    <select id="getRateTrendChartVO" resultType="com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleChartVO">
        SELECT
        system_id as systemId,  check_date as checkDate,
        SUM(ai_pass) AS aiPassHumanPassNum,
        SUM(human_reject) AS aiPassHumanUnPassNum,
        SUM(human_pass) AS aiUnPassHumanPassNum,
        SUM(ai_reject) AS aiUnPassHumanUnPassNum,
        (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)) AS sampleTotalNum,
        ROUND((SUM(ai_pass) + SUM(ai_reject)) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS sampleRightRate,
        ROUND(SUM(ai_pass) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiPassHumanPassRate,
        ROUND(SUM(human_reject) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiPassHumanUnpassRate,
        ROUND(SUM(human_pass) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiUnpassHumanPassRate,
        ROUND(SUM(ai_reject) * 100.0 / (SUM(ai_pass) + SUM(human_reject) + SUM(human_pass) + SUM(ai_reject)), 2) AS aiUnpassHumanUnpassRate
        FROM
        rigth_task_rule hr
        <where>
            <if test = " numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                AND hr.check_date BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
            </if>
            <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                AND hr.system_id = #{numberEmployeeDTO.systemId}
            </if>
        </where>
        GROUP BY system_id,check_date
    </select>
</mapper>
