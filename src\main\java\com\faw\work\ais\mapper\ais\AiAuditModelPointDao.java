package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.AiAuditModelPoint;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* ai审核点关系表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 10:50:12
*/
@InterceptorIgnore(tenantLine = "true")
public interface AiAuditModelPointDao {

    /**
    * 新增
    */
    public int insert(@Param("aiAuditModelPoint") AiAuditModelPoint aiAuditModelPoint);
    public int insertBatch(@Param("aiAuditModelPoints") List<AiAuditModelPoint> aiAuditModelPoint);

    /**
    * 删除
    */
    public int delete(@Param("id") Long id);

    /**
    * 修改
    */
    public int update(@Param("aiAuditModelPoint") AiAuditModelPoint aiAuditModelPoint);


    /**
    * 根据id查询 getAiAuditModelPointById
    */
    public AiAuditModelPoint getAiAuditModelPointById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<AiAuditModelPoint> getAiAuditModelPointList(@Param("aiAuditModelPoint")AiAuditModelPoint aiAuditModelPoint);



}

