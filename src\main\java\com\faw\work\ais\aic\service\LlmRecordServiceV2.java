package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.AiRequest;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.response.AiResponse;

/**
 * LlmRecordServiceV2 接口定义了与大型语言模型 (LLM) 交互和记录相关操作。
 *
 * <AUTHOR>
 */
public interface LlmRecordServiceV2 {

    /**
     * 测试聊天功能。  该方法接收一个 {@link AiRequest} 对象作为输入，并返回一个 {@link AiResponse} 对象。
     *
     * @param request 包含聊天请求信息的 {@link AiRequest} 对象。
     * @return {@link AiResponse} 对象，包含聊天响应信息。
     */
    AiResponse testChat(AiRequest request);

    /**
     * 处理 A/B 测试场景下的对话流程。  该方法接收一个 {@link ProcessRequest} 对象作为输入，用于处理特定的对话流程。
     *
     * @param request 包含对话处理请求信息的 {@link ProcessRequest} 对象。
     */
    void processConversationAbTest(ProcessRequest request);

    /**
     * 处理 A/B 测试场景下的消息片段。  该方法接收一个消息内容字符串作为输入，用于处理特定的消息片段。
     *
     * @param messageContent 消息内容字符串。
     */
    void processSliceAbTest(String messageContent);
}
