package com.faw.work.ais.common.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 智能聊天意图枚举
 *
 * <AUTHOR>
 * @since 2025-06-03 10:22
 */
@Getter
@AllArgsConstructor
public enum ChatSceneEnum {

    /**
     * 解析图片
     */
    ANALYZE_PHOTO("analyzePhoto", "解析图片"),

    /**
     * 识别意图
     */
    IDENTIFY_INTENT("identifyIntent", "识别意图"),

    /**
     * 语义总结
     */
    SEMANTIC_SUMMARY("semanticSummary", "语义总结"),

    /**
     * 技能组识别
     */
    SKILL_GROUP("skillGroup", "技能组识别"),

    /**
     * 推荐相关问题
     */
    RECOMMEND("recommend", "推荐相关问题"),

    /**
     * 通用知识问答
     */
    SMALL_TALK("smallTalk", "通用知识问答"),

    /**
     * 车辆知识问答
     */
    KNOWLEDGE("knowledge", "用车知识问答"),

    /**
     * 业务系统跳转（人工客服）
     */
    STAFF_SERVICE("staffService", "业务系统跳转（人工客服）"),

    /**
     * 上门取车业务办理
     */
    PICK_UP_CAR("pickUpCar", "上门取车业务办理"),

    /**
     * 一键维保业务办理
     */
    MAINTENANCE("maintenance", "一键维保业务办理"),

    /**
     * 车辆故障
     */
    VEHICLE_FAULT("vehicleFault", "车辆故障"),

    /**
     * 维修进度
     */
    REPAIR_PROGRESS("repairProgress", "维修进度"),

    /**
     * 通用问题
     */
    COMMON("common", "通用问题");


    private final String code;
    private final String desc;


    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ChatSceneEnum getByCode(String code) {
        for (ChatSceneEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return COMMON;
    }
}
