package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * FAQ标注任务响应对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "FAQ标注任务响应对象")
public class FaqAnnotationTaskResponse {

    @Schema(description = "任务ID")
    private String id;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "机器人ID")
    private String robotId;

    @Schema(description = "机器人名称")
    private String robotName;

    @Schema(description = "数据来源：all-全部，prod-正式环境，test-测试环境")
    private String dataSource;

    @Schema(description = "通话类型：all-全部通话，unanswered-仅答非所问")
    private String callType;

    @Schema(description = "抽取开始时间")
    private LocalDateTime startTime;

    @Schema(description = "抽取结束时间")
    private LocalDateTime endTime;

    @Schema(description = "任务状态：processing-进行中，completed-已完成")
    private String status;

    @Schema(description = "抽取总数据量")
    private Integer totalCount;

    @Schema(description = "已标注数量")
    private Integer annotatedCount;

    @Schema(description = "创建人ID")
    private String creatorId;

    @Schema(description = "创建人姓名")
    private String creatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "任务完成时间")
    private LocalDateTime completedAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
