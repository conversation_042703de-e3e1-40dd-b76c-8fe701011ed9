package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FaqCategoryMapper extends BaseMapper<FaqCategoryPO> {
    /**
     * 根据类别名称查询类别列表
     *
     * @param categoryName 类别名称
     * @return 符合条件的类别列表
     */
    List<FaqCategoryPO> selectCategoryListByName(String categoryName);


    /**
     * 查询所有类别
     *
     * @return 所有类别列表
     */
    List<FaqCategoryPO> selectAllCategories();

    /**
     * 根据名称模糊查询类目
     * @param name 类目名称
     * @return 符合条件的类目列表
     */
    List<FaqCategoryPO> selectByNameLike(@Param("name") String name);

    /**
     * 根据父ID查询子类目
     * @param parentId 父ID
     * @return 符合条件的子类目列表
     */
    List<FaqCategoryPO> selectByParentId(@Param("parentId") Integer parentId);

    /**
     * 查询所有根类别列表
     *
     * @param env 环境
     * @return 所有根类别列表
     */
    List<FaqCategoryPO> findAllRootCategoryList(String env);

    /**
     * 根据父ID和名称查询子类目
     *
     * @param parentId 父ID
     * @return 符合条件的子类目列表
     */
    List<FaqCategoryPO> findChildrenByNameAndParentId(String parentId);

    /**
     * 根据父ID查询所有直接子类目
     *
     * @param parentId 父ID
     * @param env      环境
     * @return 符合条件的子类目列表
     */
    List<FaqCategoryPO> findChildrenByParentId(@Param("parentId") String parentId, @Param("env") String env);

    /**
     * 根据ID列表查询类别列表
     *
     * @param categoryIds 类别ID列表
     * @return 符合条件的类别列表
     */
    List<FaqCategoryPO> selectProdCategoryListByIds(@Param("categoryIds")List<String> categoryIds);
}
