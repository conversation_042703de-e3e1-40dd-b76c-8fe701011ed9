package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.aic.model.domain.FaqRobotPO;
import com.faw.work.ais.aic.model.domain.FaqRobotPublishPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * FAQ机器人Mapper接口
 * 用于访问和操作FAQ机器人数据表。
 * <AUTHOR>
 */
@Mapper
public interface FaqRobotMapper extends BaseMapper<FaqRobotPO> {

    /**
     * 分页查询机器人列表，支持按机器人名称进行筛选。
     *
     * @param page 分页对象，用于传递分页参数。
     * @param robotName 机器人名称，用于模糊匹配机器人名称。
     * @return 分页结果，包含符合条件的机器人列表和总记录数。
     */
    IPage<FaqRobotPO> selectPageWithRobotName(Page<FaqRobotPO> page,
                                              @Param("robotName") String robotName);

    /**
     * 根据机器人名称获取机器人信息。
     *
     * @param robotName 机器人名称
     * @return 机器人对象，如果不存在则返回null。
     */
    FaqRobotPO getRobotByName(String robotName);

    /**
     * 保存机器人发布记录。
     * 用于记录机器人的发布信息，例如发布时间、发布人等。
     *
     * @param request 机器人发布对象，包含发布信息。
     */
    void savePublishRecord(FaqRobotPublishPO request);

    /**
     * 更新机器人的状态。
     * 用于更新机器人的状态，例如启用、禁用等。
     *
     * @param robotId 机器人ID。
     * @param status 机器人状态。
     */
    void updateRobotStatus(String robotId, int status);
}
