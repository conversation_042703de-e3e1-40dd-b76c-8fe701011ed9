package com.faw.work.ais.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.entity.dto.ai.AiTaskQueryDTO;
import com.faw.work.ais.entity.vo.ai.AiTaskResultVO;
import com.faw.work.ais.model.AiTaskResult;
import com.faw.work.ais.mapper.ais.AiTaskResultMapper;
import com.faw.work.ais.service.AiTaskResultService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_task_result(ai任务结果记录表)】的数据库操作Service实现
* @createDate 2024-04-07 21:40:12
*/
@Service
public class AiTaskResultServiceImpl extends ServiceImpl<AiTaskResultMapper, AiTaskResult>
    implements AiTaskResultService {

    @Autowired
    private AiTaskResultMapper aiTaskResultMapper;

    @Override
    public Page<AiTaskResultVO> getAiResult(AiTaskQueryDTO aiTaskQueryDTO) {
        long current = aiTaskQueryDTO.getCurrent();
        long size = aiTaskQueryDTO.getSize();
        Page<AiTaskResultVO> page = new Page<>(current, size);
        QueryWrapper<AiTaskResultVO> wrapper=new QueryWrapper();
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getTaskType())){
            wrapper.eq("task_type",aiTaskQueryDTO.getTaskType());
        }
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getContentType())){
            wrapper.eq("content_type",aiTaskQueryDTO.getContentType());
        }
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getDealTimeStart()) && StringUtils.isNotBlank(aiTaskQueryDTO.getDealTimeEnd())){
            wrapper.between("create_time",aiTaskQueryDTO.getDealTimeStart(), aiTaskQueryDTO.getDealTimeEnd());
        }
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getBizId())){
            wrapper.eq("biz_id",aiTaskQueryDTO.getBizId());
        }
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getSystemId())){
            wrapper.eq("system_id",aiTaskQueryDTO.getSystemId());
        }
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getTaskStatus())){
            wrapper.eq("task_status",aiTaskQueryDTO.getTaskStatus());
        }
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getTaskResult())){
            wrapper.eq("task_result",aiTaskQueryDTO.getTaskResult());
        }
        if(StringUtils.isNotBlank(aiTaskQueryDTO.getVersion())){
            wrapper.eq("version",aiTaskQueryDTO.getVersion());
        }
        return aiTaskResultMapper.selectAiTaskResultPage(page, wrapper);
    }
}




