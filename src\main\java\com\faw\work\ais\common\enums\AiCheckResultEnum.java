package com.faw.work.ais.common.enums;

/**
 * @Auther:         hp
 * 0-驳回；1-通过；单次
 * @Description:
 */

public enum AiCheckResultEnum {
    AI_ZERO(0,"驳回"),
    AI_ONE(1,"通过"),
    ;


    private final Integer code;
    private final String message;


    AiCheckResultEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private Integer code() {
        return this.code;
    }

    private String message() {
        return this.message;
    }



    /**
     * 根据key获取value
     *
     * @param code
     * @return
     */
    public static String getValue(Integer code) {
        AiCheckResultEnum[] LevelingStatusEnums = values();
        for (AiCheckResultEnum LevelingStatusEnum : LevelingStatusEnums) {
            if (LevelingStatusEnum.code().equals(code)) {
                return LevelingStatusEnum.message();
            }
        }
        return null;
    }



    /**
     * 根据value获取key
     *
     * @param message
     * @return
     */
    public static Integer getCode(String message) {
        AiCheckResultEnum[] LevelingStatusEnums = values();
        for (AiCheckResultEnum LevelingStatusEnum : LevelingStatusEnums) {
            if (LevelingStatusEnum.message().equals(message)) {
                return LevelingStatusEnum.code();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
