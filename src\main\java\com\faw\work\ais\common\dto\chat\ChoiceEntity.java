package com.faw.work.ais.common.dto.chat;

import com.alibaba.dashscope.common.Message;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Choice实体
 *
 * <AUTHOR>
 * @since 2025-04-22 16:34
 */
@Data
@Schema(description = "Choice实体")
public class ChoiceEntity {

    @Schema(description = "消息")
    private Message message;

    @Schema(description = "完成原因")
    @JsonProperty("finish_reason")
    private String finishReason;

    @Schema(description = "索引")
    private Integer index;

}
