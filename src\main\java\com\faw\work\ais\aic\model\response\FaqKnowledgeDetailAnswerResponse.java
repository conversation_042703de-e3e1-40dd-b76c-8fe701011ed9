package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "知识详情响应，包含知识本身和其相似问列表")
public class FaqKnowledgeDetailAnswerResponse {

    @Schema(description = "知识详情")
    private FaqKnowledgePO knowledge;

    @Schema(description = "相似问列表")
    private List<String> similarQuestions;

    @Schema(description = "类目名称")
    private String categoryName;

    public FaqKnowledgeDetailAnswerResponse(FaqKnowledgePO knowledge, List<String> similarQuestions) {
        this.knowledge = knowledge;
        this.similarQuestions = similarQuestions;
    }
}
