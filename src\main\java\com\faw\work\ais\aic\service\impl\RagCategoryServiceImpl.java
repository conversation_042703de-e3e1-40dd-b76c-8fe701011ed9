package com.faw.work.ais.aic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.mapper.rag.RagCategoryMapper;
import com.faw.work.ais.aic.model.domain.RagCategoryPO;
import com.faw.work.ais.aic.model.request.RagCategoryPageRequest;
import com.faw.work.ais.aic.service.RagCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 文档类目表 服务实现类
 *
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RagCategoryServiceImpl extends ServiceImpl<RagCategoryMapper, RagCategoryPO> implements RagCategoryService {

    @Override
    public List<RagCategoryPO> getCategoryList(RagCategoryPO ragCategory) {
        LambdaQueryWrapper<RagCategoryPO> queryWrapper = new LambdaQueryWrapper<>();

        if (ragCategory != null) {
            // 按条件查询
            if (ragCategory.getId() != null) {
                queryWrapper.eq(RagCategoryPO::getId, ragCategory.getId());
            }

            if (StringUtils.hasText(ragCategory.getName())) {
                queryWrapper.like(RagCategoryPO::getName, ragCategory.getName());
            }
        }

        // 按ID降序排序
        queryWrapper.orderByDesc(RagCategoryPO::getId);

        return this.list(queryWrapper);
    }

    @Override
    public Page<RagCategoryPO> getCategoryPage(RagCategoryPageRequest request) {
        Page<RagCategoryPO> page = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<RagCategoryPO> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (request.getId() != null) {
            queryWrapper.eq(RagCategoryPO::getId, request.getId());
        }

        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(RagCategoryPO::getName, request.getName());
        }

        // 按ID降序排序
        queryWrapper.orderByDesc(RagCategoryPO::getId);

        return this.page(page, queryWrapper);
    }
} 