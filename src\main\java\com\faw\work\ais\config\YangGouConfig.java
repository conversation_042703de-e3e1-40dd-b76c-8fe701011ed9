package com.faw.work.ais.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Configuration
@Data
@RefreshScope
@Slf4j
public class YangGouConfig {

    public static final int SIX = 6;
    @Value("${dashscope.yanggou.workspace:}")
    private String workspaceId;
    @Value("${dashscope.yanggou.apiKey:}")
    private String apiKey;
    @Value("${dashscope.yanggou.kehuhuaxiang.appId:}")
    private String kehuhuaxiangAppId;

    @Value("${dashscope.yanggou.highQualitySpeech:}")
    private String yanggouHighQualitySpeech;

    @Value("${dashscope.yanggou.highQualityPrompt:}")
    private String yanggouHighQualityPrompt;


    @Value("${dashscope.yanggou.highQualitySpeech2:}")
    private String yanggouHighQualitySpeech2;

    @Value("${dashscope.yanggou.knowledge.env:prod}")
    private String env;

    @Value("${dashscope.yanggou.knowledge.robot-id}")
    private String robotId;

    @Value("${dashscope.yanggou.knowledge.topK:1}")
    private Integer topK;

    @Value("${dashscope.yanggou.knowledge.similarityThreshold:0.85f}")
    private float similarityThreshold;

    @Value("${dashscope.yanggou.highQualityKnowledge}")
    private String highQualityKnowledge;

    /**
     * 在Spring Boot启动完成后打印配置信息
     */
    @PostConstruct
    public void printConfigInfo() {
        log.info("=== BaiLian App Configuration ===");
        log.info("yanggou Workspace ID: {}", workspaceId);
        log.info("yanggou API Key: {}", maskApiKey(apiKey));
        log.info("yanggou kehuhuaxiang App ID: {}", kehuhuaxiangAppId);
        log.info("=== Configuration Loaded Successfully ===");
    }

    /**
     * 对API Key进行掩码处理，后6位用*替换
     * @param apiKey 原始API Key
     * @return 掩码后的API Key
     */
    private String maskApiKey(String apiKey) {
        if (!StringUtils.hasText(apiKey)) {
            return "[NOT_CONFIGURED]";
        }

        if (apiKey.length() <= SIX) {
            // 如果长度小于等于6位，全部用*替换
            return "*".repeat(apiKey.length());
        } else {
            // 保留前面的字符，后6位用*替换
            String prefix = apiKey.substring(0, apiKey.length() - 6);
            return prefix + "******";
        }
    }
}