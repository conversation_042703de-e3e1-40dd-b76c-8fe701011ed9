package com.faw.work.ais.common.enums;

/**
 * 常量类，包含常用数据。
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
public class Constants {


    public static final int ONE = 1;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SIX = 6;
    public static final int SEVEN = 7;
    public static final int EIGHT = 8;
    public static final int NINE = 9;
    public static final int TEN = 10;

    public static final int SUCCESS = 200;


    public static final int BATCH_SIZE = 1000;



    public static final String EMPTY_STRING = "";
    public static final String COMMA = ",";
    public static final String SEMICOLON = ";";
    public static final String COLON = ":";
    public static final String HYPHEN = "-";
    public static final String UNDERSCORE = "_";


    public static final boolean TRUE = true;
    public static final boolean FALSE = false;


    public static final String DEFAULT_ENCODING = "UTF-8";


    private Constants() {
        // 防止被实例化
        throw new IllegalStateException("Utility class");
    }
}

