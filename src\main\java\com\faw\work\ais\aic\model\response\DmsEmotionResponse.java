package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.dto.EmotionResponseSummaryDTO;
import com.faw.work.ais.aic.model.dto.TagAnalysisDTO;
import com.faw.work.ais.aic.model.dto.TopicSummaryDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DmsEmotionResponse {

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 情绪数据-情绪识别模型
     */
    private EmotionResponseSummaryDTO emotionData;

    /**
     * 标签数据-产品需求模型
     */
    private List<TagAnalysisDTO> tagData;

    /**
     * 话题总结数据-话题总结模型
     */
    private TopicSummaryDTO topicData;
}
