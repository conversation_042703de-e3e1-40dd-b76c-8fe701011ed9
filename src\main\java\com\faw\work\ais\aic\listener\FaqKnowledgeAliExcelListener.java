package com.faw.work.ais.aic.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import com.faw.work.ais.aic.model.dto.FaqKnowledgeAliExcelDataDTO;
import com.faw.work.ais.aic.service.FaqCategoryService;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.aic.service.FaqSimilarKnowledgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * FAQ知识阿里Excel导入监听器
 * <AUTHOR>
 */
@Slf4j
public class FaqKnowledgeAliExcelListener extends AnalysisEventListener<FaqKnowledgeAliExcelDataDTO> {

    /**
     * 批量处理的大小
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 数据列表
     */
    private final List<FaqKnowledgeAliExcelDataDTO> dataList = new ArrayList<>();

    /**
     * FAQ知识服务
     */
    private final FaqKnowledgeService faqKnowledgeService;

    /**
     * 类目服务
     */
    private final FaqCategoryService faqCategoryService;

  private final FaqSimilarKnowledgeService faqSimilarKnowledgeService;

    /**
     * 成功导入数量
     */
    private int successCount = 0;

    /**
     * 失败导入数量
     */
    private int failCount = 0;

    /**
     * 错误信息列表
     */
    private final List<String> errorMessages = new ArrayList<>();

    /**
     * 构造函数
     *
     * @param faqKnowledgeService    FAQ知识服务
     * @param faqCategoryService     类目服务
     */
    public FaqKnowledgeAliExcelListener(FaqKnowledgeService faqKnowledgeService,
                                        FaqCategoryService faqCategoryService,
                                        FaqSimilarKnowledgeService faqSimilarKnowledgeService) {
        this.faqKnowledgeService = faqKnowledgeService;
        this.faqCategoryService = faqCategoryService;
        this.faqSimilarKnowledgeService = faqSimilarKnowledgeService;
    }

    @Override
    public void invoke(FaqKnowledgeAliExcelDataDTO data, AnalysisContext context) {
        // 添加到数据列表
        dataList.add(data);

        // 达到BATCH_SIZE时，进行批量处理
        if (dataList.size() >= BATCH_SIZE) {
            saveData();
            // 清空列表
            dataList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余数据
        saveData();
        log.info("Excel导入完成，成功导入{}条，失败{}条", successCount, failCount);
    }

    /**
     * 保存数据
     */
    private void saveData() {
        if (dataList.isEmpty()) {
            return;
        }

        int currentRow = successCount + failCount + 1;

        for (FaqKnowledgeAliExcelDataDTO data : dataList) {
            try {
                // 数据校验
                if (StringUtils.isBlank(data.getCategoryName())) {
                    failCount++;
                    errorMessages.add("第" + currentRow + "行：类目名称不能为空");
                    currentRow++;
                    throw new IllegalArgumentException("类目名称不能为空");
                }
                if (StringUtils.isBlank(data.getQuestion())) {
                    failCount++;
                    errorMessages.add("第" + currentRow + "行：知识标题不能为空");
                    currentRow++;
                    throw new IllegalArgumentException("知识标题不能为空");
                }

                // 答案需要至少有一个不为空
                String plainTextAnswer = data.getPlainTextAnswer();
                if (StringUtils.isBlank(plainTextAnswer) && StringUtils.isBlank(data.getRichTextAnswer())) {
                    failCount++;
                    errorMessages.add("第" + currentRow + "行：答案不能为空");
                    currentRow++;
                    throw new IllegalArgumentException("答案不能为空");
                }

                // 获取类目信息，根据类目名称查询
                FaqCategoryPO category = null;
                try {
                    // 根据名称查询类目
                    List<FaqCategoryPO> categoryList = faqCategoryService.getCategoryListByName(data.getCategoryName());
                    if (!categoryList.isEmpty()) {
                        category = categoryList.get(0);
                    }

                } catch (Exception e) {
                    log.warn("类目名称查询异常: {}", data.getCategoryName(), e);
                }

                // 若类目不存在，就创建类目
                if (category == null) {
                    try {
                        // 创建类目
                        category = new FaqCategoryPO();
                        category.setName(data.getCategoryName());
                        category.setCreatedBy("system");
                        category.setCreatedAt(LocalDateTime.now());
                        category.setUpdatedBy("system");
                        category.setUpdatedAt(LocalDateTime.now());
                        faqCategoryService.save(category);
                    } catch (Exception e) {
                        log.warn("类目创建异常: {}", data.getCategoryName(), e);
                    }
                }

                // 确定使用哪个答案
                String answer = StringUtils.isNotBlank(plainTextAnswer)
                        ? plainTextAnswer
                        : data.getRichTextAnswer();
                String answerType = StringUtils.isNotBlank(plainTextAnswer)
                        ? "00"
                        : "01";
                // 检查知识是否已存在（根据问题查重）
                assert category != null;
                String categoryId = category.getId();
                FaqKnowledgePO existingKnowledge = faqKnowledgeService.getByQuestion(data.getQuestion(), categoryId);

                if (existingKnowledge == null) {
                    // 新增知识
                    FaqKnowledgePO knowledge = new FaqKnowledgePO();
                    knowledge.setEffectiveType("00");
                    knowledge.setCategoryId(categoryId);
                    knowledge.setQuestion(data.getQuestion());
                    knowledge.setAnswer(answer);
                    knowledge.setHitCount(0L);
                    knowledge.setCreatedAt(LocalDateTime.now());
                    knowledge.setCreatedBy("system");
                    knowledge.setUpdatedAt(LocalDateTime.now());
                    knowledge.setUpdatedBy("system");
                    faqKnowledgeService.save(knowledge);

                    // 新增相似问（如果有）- 支持多行拆分
                    if (StringUtils.isNotBlank(data.getSimilarQuestion())) {
                        // 按换行符拆分相似问题
                        String[] similarQuestions = data.getSimilarQuestion().split("\\r?\\n");

                        for (String question : similarQuestions) {
                            // 去除首尾空白字符
                            String trimmedQuestion = question.trim();

                            // 跳过空字符串
                            if (StringUtils.isNotBlank(trimmedQuestion)) {
                                FaqSimilarKnowledgePO similarQuestion = new FaqSimilarKnowledgePO();
                                similarQuestion.setQuestion(data.getQuestion());
                                similarQuestion.setSimilarQuestion(trimmedQuestion);
                                similarQuestion.setKnowledgeId(knowledge.getId());
                                similarQuestion.setCategoryId(categoryId);
                                similarQuestion.setAnswer(answer);
                                similarQuestion.setCreatedAt(LocalDateTime.now());
                                faqSimilarKnowledgeService.save(similarQuestion);
                            }
                        }
                    }
                } else {
                    // 已存在知识，只新增相似问
                    if (StringUtils.isNotBlank(data.getSimilarQuestion())) {
                        // 按换行符拆分相似问题
                        String[] similarQuestions = data.getSimilarQuestion().split("\\r?\\n");

                        for (String question : similarQuestions) {
                            // 去除首尾空白字符
                            String trimmedQuestion = question.trim();

                            // 跳过空字符串
                            if (StringUtils.isNotBlank(trimmedQuestion)) {
                                FaqSimilarKnowledgePO similarQuestion = new FaqSimilarKnowledgePO();
                                similarQuestion.setQuestion(existingKnowledge.getQuestion());
                                similarQuestion.setSimilarQuestion(trimmedQuestion);
                                similarQuestion.setKnowledgeId(existingKnowledge.getId());
                                similarQuestion.setCategoryId(existingKnowledge.getCategoryId());
                                similarQuestion.setAnswer(existingKnowledge.getAnswer());
                                similarQuestion.setCreatedAt(LocalDateTime.now());

                                faqSimilarKnowledgeService.save(similarQuestion);
                            }
                        }
                    }
                }

                successCount++;
                currentRow++;

            } catch (Exception e) {
                failCount++;
                errorMessages.add("第" + currentRow + "行：数据处理失败，原因：" + e.getMessage());
                log.error("导入FAQ知识处理失败", e);
                throw e;
            }
        }
    }
    /**
     * 获取成功导入数量
     *
     * @return 成功导入数量
     */
    public int getSuccessCount() {
        return successCount;
    }

    /**
     * 获取失败导入数量
     *
     * @return 失败导入数量
     */
    public int getFailCount() {
        return failCount;
    }

    /**
     * 获取错误信息列表
     *
     * @return 错误信息列表
     */
    public List<String> getErrorMessages() {
        return errorMessages;
    }
} 