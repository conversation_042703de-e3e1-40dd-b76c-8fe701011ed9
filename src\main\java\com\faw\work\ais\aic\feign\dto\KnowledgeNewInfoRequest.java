package com.faw.work.ais.aic.feign.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(title = "知识信息", description = "知识信息")
public class KnowledgeNewInfoRequest {

    /**
     * 知识类型;字典
     */
    @Schema(description = "知识类型")
    private Integer knowledgeType;

    /**
     * 知识子类型;字典
     */
    @Schema(description = "知识子类型")
    private Integer knowledgeSubType;
}
