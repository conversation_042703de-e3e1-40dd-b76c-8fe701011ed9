package com.faw.work.ais.mapper.ais;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.entity.dto.ai.QueryHumanResultDTO;
import com.faw.work.ais.entity.vo.ai.HumanResultVo;
import com.faw.work.ais.entity.vo.ai.TaskRuleVO;
import com.faw.work.ais.model.AiTaskResultNew;
import com.faw.work.ais.model.HumanResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HumanResultMapper extends BaseMapper<HumanResult> {
    int deleteByPrimaryKey(Integer id);

    int insert(HumanResult record);

    int insertSelective(HumanResult record);

    HumanResult selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(HumanResult record);

    int updateByPrimaryKey(HumanResult record);

    int deleteByBatchIdAndTraceIdInt(@Param("traceId") String traceId, @Param("batchId") String batchId);

    public List<HumanResult> getHumanResultThisList(@Param("aiTaskResultNew")AiTaskResultNew aiTaskResultNew);


    /**
     * 查询人工审核数据列表
     * @param dto 请求参数
     * @return List<HumanResultVo> 返回对象累表
     */
    List<HumanResultVo> getHumanResultList(@Param("dto") QueryHumanResultDTO dto);

    /**
     * 获取类型对应的分子数量
     *
     * @param dto  请求条件
     * @param type 类型
     * @return Integer 个数
     */
    Integer getHumanResultDividendCount(@Param("dto") QueryHumanResultDTO dto, @Param("type") String type);

    /**
     * 获取类型对应的分母数量
     *
     * @param dto  请求条件
     * @param type 类型
     * @return Integer 个数
     */
    Integer getHumanResultDivisorCount(@Param("dto") QueryHumanResultDTO dto, @Param("type") String type);

    /**
     * 获取审核数量
     *
     * @param dto  请求条件
     * @param type 类型
     * @return Integer 个数
     */
    Integer getApproveCount(@Param("dto") QueryHumanResultDTO dto, @Param("type") String type);

    /**
     * 批量查询ai审核原因
     * @param traceIdList  请求参数
     * @return  返回对象列表
     */
    List<AiTaskResultNew> getAiResultList(@Param("list") List<String> traceIdList);

    /**
     * 批量查询规则名称
     * @param taskTypeList  请求参数
     * @return  返回对象列表
     */
    List<TaskRuleVO> getTaskRuleName(@Param("list") List<String> taskTypeList);
}