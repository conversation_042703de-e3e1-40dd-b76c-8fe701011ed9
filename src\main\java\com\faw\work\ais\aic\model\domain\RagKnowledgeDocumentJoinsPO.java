package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 知识库-文档关联表
 *
 * <AUTHOR>
 */
@Data
@TableName("rag_knowledge_document_joins")
@Schema(description = "知识库文档关联实体")
public class RagKnowledgeDocumentJoinsPO {

    @TableId(value = "id")
    @Schema(description = "关联ID")
    private Long id;

    @TableField("rag_knowledge_id")
    @Schema(description = "知识库ID")
    private Long ragKnowledgeId;

    @TableField("document_id")
    @Schema(description = "文档ID")
    private Long documentId;

    @TableField("created_by")
    @Schema(description = "创建人")
    private String createdBy;

    @TableField("created_at")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField("updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @TableField("updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 