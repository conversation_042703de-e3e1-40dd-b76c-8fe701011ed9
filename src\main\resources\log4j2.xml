<?xml version="1.0" encoding="UTF-8"?>
<configuration status="debug"  monitorInterval="30" packages="org.apache.skywalking.apm.toolkit.log.log4j.v2.x">
    <properties>
        <property name="logging.path" value="/data/logs" />
        <property name="log.level" value="info" />
        <property name="COMMON_LOG_PATTERN" value="%date{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%logger] [%traceId] [%X{spanId}] [%X{pSpanId}] [%X{req.requestURL}] [%X{req.queryString}] - %msg %exception %n" />
    </properties>
    <appenders>
        <Console name="console" target="SYSTEM_OUT">
            <ThresholdFilter level="${log.level}" onMatch="ACCEPT" onMismatch="DENY" />
            <PatternLayout pattern="${COMMON_LOG_PATTERN}" />
        </Console>
        <RollingFile name="rollingFile" fileName="${logging.path}/log.log" filePattern="${logging.path}/log-%d{yyyy-MM-dd}-%i.log">
            <ThresholdFilter level="${log.level}" onMatch="ACCEPT" onMismatch="DENY" />
            <PatternLayout pattern="${COMMON_LOG_PATTERN}"></PatternLayout>
            <Policies>
                <!-- 每24小时更新一次 -->
                <TimeBasedTriggeringPolicy modulate="true" interval="1" />
                <SizeBasedTriggeringPolicy size="100 MB" />
            </Policies>
        </RollingFile>
    </appenders>
    <loggers>
        <root level="${log.level}">
            <appender-ref ref="console" />
            <appender-ref ref="rollingFile" />
        </root>
    </loggers>
</configuration>  