package com.faw.work.ais.entity.dto.ai.score;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "大模型评分 DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIScoreDTO {
    @Schema(description = "内容")
    private String content;
    @Schema(description = "分数类型")
    private Integer scoreType;
}
