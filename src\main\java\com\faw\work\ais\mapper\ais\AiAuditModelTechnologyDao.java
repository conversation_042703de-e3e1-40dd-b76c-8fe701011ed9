package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.AiAuditModelTechnology;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* ai审核技术模型关系表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 10:55:00
*/
@InterceptorIgnore(tenantLine = "true")
public interface AiAuditModelTechnologyDao {

    /**
    * 新增
    */
    public int insert(@Param("aiAuditModelTechnology") AiAuditModelTechnology aiAuditModelTechnology);
    public int insertBatch(@Param("aiAuditModelTechnologys") List<AiAuditModelTechnology> aiAuditModelTechnology);

    /**
    * 删除
    */
    public int delete(@Param("id") Long id);

    /**
    * 修改
    */
    public int update(@Param("aiAuditModelTechnology") AiAuditModelTechnology aiAuditModelTechnology);


    /**
    * 根据id查询 getAiAuditModelTechnologyById
    */
    public AiAuditModelTechnology getAiAuditModelTechnologyById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<AiAuditModelTechnology> getAiAuditModelTechnologyList(@Param("aiAuditModelTechnology")AiAuditModelTechnology aiAuditModelTechnology);



}

