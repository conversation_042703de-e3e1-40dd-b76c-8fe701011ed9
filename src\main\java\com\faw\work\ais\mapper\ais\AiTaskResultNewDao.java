package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.entity.dto.ai.AiTaskResultDTO;
import com.faw.work.ais.model.AiTaskResultNew;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* AI任务结果记录表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 11:20:13
*/
@InterceptorIgnore(tenantLine = "true")
public interface AiTaskResultNewDao {

    /**
    * 新增
    */
    public int insert(@Param("aiTaskResultNew") AiTaskResultNew aiTaskResultNew);
    /**
     * 新增
     */
    public int insertBatch(@Param("aiTaskResultNews") List<AiTaskResultNew> aiTaskResultNew);
    /**
     * 新增
     */
    public int insertBatchByCreateTime(@Param("aiTaskResultNews") List<AiTaskResultNew> aiTaskResultNew);

    /**
    * 删除
    */
    public int delete(@Param("id") Long id);
    /**
     * 删除
     */
    public int deleteByIds(@Param("ids") List<Long> id);
    /**
    * 修改
    */
    public int update(@Param("aiTaskResultNew") AiTaskResultNew aiTaskResultNew);


    /**
    * 根据id查询 getAiTaskResultNewById
    */
    public AiTaskResultNew getAiTaskResultNewById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<AiTaskResultNew> getAiTaskResultNewList(@Param("aiTaskResultNew")AiTaskResultNew aiTaskResultNew);


    public List<AiTaskResultNew> getAiTaskResultOldList(@Param("aiTaskResultNew")AiTaskResultNew aiTaskResultNew);

    public List<AiTaskResultNew> getAiTaskResultThisList(@Param("aiTaskResultNew")AiTaskResultNew aiTaskResultNew);

    /**
     * 根据traceId查询ai结果表
     *
     * @param searchParam 参数条件
     * @return AiTaskResult ai结果数据
     */
    AiTaskResultNew getAiTaskResultNewOne(@Param("aiTaskResultNew") AiTaskResultDTO searchParam);
}

