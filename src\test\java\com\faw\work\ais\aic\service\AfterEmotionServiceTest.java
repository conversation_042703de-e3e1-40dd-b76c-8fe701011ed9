package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.AfterEmotionRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 售后接待情绪价值模型服务测试类
 * <AUTHOR>
 */
@SpringBootTest
@Slf4j
public class AfterEmotionServiceTest {

    @Autowired
    private AfterEmotionService afterEmotionService;

    @Test
    public void testProcessAfterEmotion() {
        // 构建测试请求
        AfterEmotionRequest request = new AfterEmotionRequest();
        request.setRequestId("test-after-emotion-" + System.currentTimeMillis());
        request.setUserInput("(start:0000) 接待专员：您好，我是一汽红旗的售后服务顾问，请问您今天来是什么问题？ (end:5000)\n" +
                "(start:6000) 客户：我的车子发动机有异响，想检查一下。 (end:7000)\n" +
                "(start:8000) 接待专员：好的，我来帮您安排检查，请问您的车型是什么？ (end:10000)\n" +
                "(start:11000) 客户：红旗H5，2022款的。 (end:14000)\n" +
                "(start:15000) 接待专员：好的，我们马上安排技师为您检查，大概需要30分钟时间。 (end:21000)\n" +
                "(start:22000) 客户：好的，谢谢。 (end:27000)");
        request.setAudioUrl("https://test-bucket.cos.ap-beijing.myqcloud.com/test-audio.mp3");

        try {
            // 测试处理售后接待情绪分析
            log.info("开始测试售后接待情绪价值模型处理");
            afterEmotionService.processAfterEmotion(request);
            log.info("售后接待情绪价值模型处理测试完成");
        } catch (Exception e) {
            log.error("售后接待情绪价值模型处理测试失败", e);
        }
    }

    @Test
    public void testProcessAfterEmotionSlice() {
        String requestId = "test-slice-" + System.currentTimeMillis();
        
        try {
            // 先创建测试数据
            AfterEmotionRequest request = new AfterEmotionRequest();
            request.setRequestId(requestId);
            request.setUserInput("(start:0000) 接待专员：您好，欢迎来到一汽红旗售后服务中心。 (end:3000)\n" +
                    "(start:4000) 客户：我的车子刹车有问题，刹车不灵敏。 (end:7000)\n" +
                    "(start:8000) 接待专员：我们马上为您安排检查，请稍等。 (end:11000)\n" +
                    "(start:12000) 客户：好的，希望能尽快解决。 (end:15000)");
            request.setAudioUrl("https://test-bucket.cos.ap-beijing.myqcloud.com/test-audio2.mp3");
            
            // 先处理请求创建分片
            afterEmotionService.processAfterEmotion(request);
            
            // 等待一段时间让消息队列处理
            Thread.sleep(2000);
            
            // 测试分片处理
            log.info("开始测试售后接待情绪价值模型分片处理");
            boolean result = afterEmotionService.processAfterEmotionSlice(requestId);
            log.info("售后接待情绪价值模型分片处理测试完成，结果: {}", result);
            
        } catch (Exception e) {
            log.error("售后接待情绪价值模型分片处理测试失败", e);
        }
    }
}
