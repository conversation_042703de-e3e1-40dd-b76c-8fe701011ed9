package com.faw.work.ais.common.dto.roleworkbench;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 角色工作台完成（关闭）任务参数实体类
 * <AUTHOR>
 */
@Data
@Schema(description = "角色工作台完成（关闭）任务参数实体类")
@EqualsAndHashCode
@Accessors(chain = true)
public class RoleWorkBenchCloseTaskDto {

    @Schema(description = "请求id,接口幂等参数 String(0-64)")
    private String requestId;

    @Schema(description = "能力中心ClientId, String(0-64),获取位置：工作流管理中心->注册能力中心->App key")
    private String clientId;

    @Schema(description = "事件编码, 对应唯一业务单元编码 String(0-64)")
    private String eventCode;

    @Schema(description = "当前业务单元编码, String(0-64)")
    private String bizUnitCode;

    @Schema(description = "当前业务单元,能力中心侧业务ID, String(20-64), (currentTaskInstanceCode-优先查找, currentBizId-其次查找,必填其中之一查询任务实例并关闭)")
    private String currentBizId;

    @Schema(description = "当前业务单元任务实例编码, String(0-64), (currentTaskInstanceCode-优先查找, currentBizId-其次查找,必填其中之一查询任务实例并关闭)")
    @NotBlank(message = "任务实例编码不能为空")
    private String currentTaskInstanceCode;

    @Schema(description = "强制终止流程, 如果当前业务单元设置了强制结束流程则此开关生效")
    private Boolean forceTerminate;

    @Schema(description = "任务关闭人云原生用户code, String(0-64), 不传默认system")
    private String userCode;

    @Schema(description = "任务关闭人云原生用户name, String(0-64), 不传默认system")
    private String userName;

}
