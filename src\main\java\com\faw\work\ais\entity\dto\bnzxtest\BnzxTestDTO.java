package com.faw.work.ais.entity.dto.bnzxtest;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 国补资源测试DTO
 */
@Schema(description = "国补资源测试DTO")
@Data
public class BnzxTestDTO {

    @Schema(description = "任务类型")
    @ApiModelProperty(value = "业务规则类型")
    private String taskType;

    @Schema(description = "身份证件")
    @ApiModelProperty(value = "id集合")
    private List<String> ids;

    @Schema(description = "开始 num")
    @ApiModelProperty(value = "id范围其实值")
    private Integer beginNum;

    @Schema(description = "结束编号")
    @ApiModelProperty(value = "id范围截止值")
    private Integer endNum;

    @Schema(description = "AI 结果")
    @ApiModelProperty(value = "ai处理结果")
    private String aiResult;

    @Schema(description = "原始结果")
    @ApiModelProperty(value = "ai处理结果解释")
    private String rawResult;

    @Schema(description = "地位")
    @ApiModelProperty(value = "状态")
    private String status;

}

