<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.SystemCallBackUrlMapper">
  <resultMap id="BaseResultMap" type="com.faw.work.ais.model.SystemCallBackUrl">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="call_back_url" jdbcType="VARCHAR" property="callBackUrl" />
    <result column="system_name" jdbcType="VARCHAR" property="systemName" />
    <result column="stop" jdbcType="VARCHAR" property="stop" />
  </resultMap>
  <sql id="Base_Column_List">
    id, system_id, call_back_url, system_name, stop
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_call_back_url
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from system_call_back_url
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.SystemCallBackUrl" useGeneratedKeys="true">
    insert into system_call_back_url (system_id, call_back_url, system_name, 
      stop)
    values (#{systemId,jdbcType=VARCHAR}, #{callBackUrl,jdbcType=VARCHAR}, #{systemName,jdbcType=VARCHAR}, 
      #{stop,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.SystemCallBackUrl" useGeneratedKeys="true">
    insert into system_call_back_url
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        system_id,
      </if>
      <if test="callBackUrl != null">
        call_back_url,
      </if>
      <if test="systemName != null">
        system_name,
      </if>
      <if test="stop != null">
        stop,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="callBackUrl != null">
        #{callBackUrl,jdbcType=VARCHAR},
      </if>
      <if test="systemName != null">
        #{systemName,jdbcType=VARCHAR},
      </if>
      <if test="stop != null">
        #{stop,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.faw.work.ais.model.SystemCallBackUrl">
    update system_call_back_url
    <set>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="callBackUrl != null">
        call_back_url = #{callBackUrl,jdbcType=VARCHAR},
      </if>
      <if test="systemName != null">
        system_name = #{systemName,jdbcType=VARCHAR},
      </if>
      <if test="stop != null">
        stop = #{stop,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.faw.work.ais.model.SystemCallBackUrl">
    update system_call_back_url
    set system_id = #{systemId,jdbcType=VARCHAR},
      call_back_url = #{callBackUrl,jdbcType=VARCHAR},
      system_name = #{systemName,jdbcType=VARCHAR},
      stop = #{stop,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--  获取看板系统列表-->
  <select id="getKanBanSystemList" resultType="com.faw.work.ais.entity.vo.ai.SystemVO">
    SELECT
      t.id systemId,
      t.system_id systemCode,
      t.system_name systemName
    FROM
      `system_call_back_url` t
    WHERE
        t.stop = '0'
  </select>

  <select id="getSystemListByUnitCode" resultType="com.faw.work.ais.entity.vo.ai.SystemVO">
    SELECT
      t.id systemId,
      t.system_id systemCode,
      t.system_name systemName
    FROM
      `system_call_back_url` t
    INNER JOIN biz_unit bu ON t.system_id = bu.system_id
    WHERE
      t.stop = '0'
      <if test="unitCode != null and unitCode != '' ">
        AND bu.unit_code = #{unitCode}
      </if>
    GROUP BY t.id ,
             t.system_id ,
             t.system_name
  </select>
</mapper>