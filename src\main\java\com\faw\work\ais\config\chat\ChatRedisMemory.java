package com.faw.work.ais.config.chat;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.ChatEntity;
import com.faw.work.ais.common.enums.chat.ChatTypeEnum;
import com.faw.work.ais.mapper.chat.AppChatHistoryDetailMapper;
import com.faw.work.ais.mapper.chat.AppChatHistoryMapper;
import com.faw.work.ais.model.chat.AppChatHistory;
import com.faw.work.ais.model.chat.AppChatHistoryDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 会话缓存
 *
 * <AUTHOR>
 * @since 2025-04-03 15:38
 */
@Slf4j
@Component
public class ChatRedisMemory implements ChatMemory {

    private final RedisTemplate<String, Object> redisTemplate;

    private final AppChatHistoryMapper appChatHistoryMapper;

    private final AppChatHistoryDetailMapper appChatHistoryDetailMapper;


    public ChatRedisMemory(RedisTemplate<String, Object> redisTemplate, AppChatHistoryMapper appChatHistoryMapper,
                           AppChatHistoryDetailMapper appChatHistoryDetailMapper) {
        this.redisTemplate = redisTemplate;
        this.appChatHistoryMapper = appChatHistoryMapper;
        this.appChatHistoryDetailMapper = appChatHistoryDetailMapper;
    }


    private static final String KEY_PREFIX = "chat:history:";


    @Override
    public void add(String conversationId, List<Message> messages) {

        String key = KEY_PREFIX + conversationId;
        List<ChatEntity> listIn = new ArrayList<>();

        for (Message msg : messages) {
            String[] strList = msg.getText().split("</think>");
            String text = strList.length == 2 ? strList[1] : strList[0];

            ChatEntity ent = new ChatEntity();
            ent.setChatId(conversationId);
            ent.setType(msg.getMessageType().getValue());
            ent.setText(text);

            listIn.add(ent);
        }

        redisTemplate.opsForList().rightPushAll(key, listIn);
        redisTemplate.expire(key, 30, TimeUnit.MINUTES);
    }

    @Override
    public List<Message> get(String conversationId, int lastN) {
        String key = KEY_PREFIX + conversationId;
        Long size = redisTemplate.opsForList().size(key);
        if (size == null || size == 0) {
            return Collections.emptyList();
        }

        int start = Math.max(0, (int) (size - lastN));
        List<Object> listTmp = redisTemplate.opsForList().range(key, start, -1);
        if (CollectionUtils.isEmpty(listTmp)) {
            return Collections.emptyList();
        }

        List<Message> listOut = new ArrayList<>();
        for (Object obj : listTmp) {
            ChatEntity chat = JSON.parseObject(JSON.toJSONString(obj), ChatEntity.class);

            if (MessageType.USER.getValue().equals(chat.getType())) {
                listOut.add(new UserMessage(chat.getText()));
            } else if (MessageType.ASSISTANT.getValue().equals(chat.getType())) {
                listOut.add(new AssistantMessage(chat.getText()));
            } else if (MessageType.SYSTEM.getValue().equals(chat.getType())) {
                listOut.add(new SystemMessage(chat.getText()));
            }
        }

        return listOut;
    }

    /**
     * 从数据库查询历史聊天记录
     *
     * @param conversationId 聊天ID
     * @return 历史聊天记录列表
     */
    private List<Message> queryChatHistory(String conversationId) {
        if (StringUtils.isEmpty(conversationId)) {
            return Collections.emptyList();
        }

        String[] split = conversationId.split(CommonConstants.COLON);
        if (split.length < CommonConstants.THREE) {
            return Collections.emptyList();
        }

        List<AppChatHistory> appChatHistories = appChatHistoryMapper.selectList(
                AppChatHistory.builder().appId(split[0]).userId(split[1]).sessionId(split[2]).delFlag(CommonConstants.FALSE_INT).build());
        if (CollectionUtils.isEmpty(appChatHistories)) {
            return Collections.emptyList();
        }

        List<AppChatHistoryDetail> historyDetails = appChatHistoryDetailMapper.selectList(
                AppChatHistoryDetail.builder().chatId(appChatHistories.get(CommonConstants.FIRST_INDEX).getId()).build());
        if (CollectionUtils.isEmpty(historyDetails)) {
            return Collections.emptyList();
        }

        List<Message> listOut = new ArrayList<>();
        for (AppChatHistoryDetail item : historyDetails) {
            if (ChatTypeEnum.USER.getCode().equals(item.getType())) {
                listOut.add(new UserMessage(item.getChatText()));
            } else if (ChatTypeEnum.ASSISTANT.getCode().equals(item.getType())) {
                listOut.add(new AssistantMessage(item.getChatText()));
            }
        }

        return listOut;
    }

    @Override
    public void clear(String conversationId) {
        redisTemplate.delete(KEY_PREFIX + conversationId);
    }

}
