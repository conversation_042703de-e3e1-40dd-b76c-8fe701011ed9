package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;

import java.util.List;

/**
 * 相似问Service接口
 */
public interface FaqSimilarKnowledgeService extends IService<FaqSimilarKnowledgePO> {
    
    /**
     * 保存相似问
     *
     * @param similarKnowledge 相似问实体
     */
    void saveSimilarKnowledge(FaqSimilarKnowledgePO similarKnowledge);
    
    /**
     * 批量保存相似问
     *
     * @param similarKnowledgeList 相似问列表
     * @return 是否成功
     */
    boolean saveBatchSimilarKnowledge(List<FaqSimilarKnowledgePO> similarKnowledgeList);
    
    /**
     * 更新相似问
     *
     * @param similarKnowledge 相似问实体
     * @return 是否成功
     */
    boolean updateSimilarKnowledge(FaqSimilarKnowledgePO similarKnowledge);
    
    /**
     * 删除相似问
     *
     * @param id 相似问ID
     * @return 是否成功
     */
    boolean deleteSimilarKnowledge(String id);
    
    /**
     * 获取相似问详情
     *
     * @param id 相似问ID
     * @return 相似问详情
     */
    FaqSimilarKnowledgePO getSimilarKnowledgeDetail(String id);
    
    /**
     * 根据原问题ID查询相似问列表
     *
     * @param originalId 原问题ID
     * @param env
     * @return 相似问列表
     */
    List<FaqSimilarKnowledgePO> listByOriginalId(String originalId, String env);
    
    /**
     * 根据类目ID列表查询相似问列表
     *
     * @param categoryIds 类目ID列表
     * @return 相似问列表
     */
    List<FaqSimilarKnowledgePO> listByCategoryIds(List<String> categoryIds);
    
    /**
     * 根据原始知识ID列表查询相似问列表
     * @param originalIds 原始知识ID列表
     * @return 相似问列表
     */
    List<FaqSimilarKnowledgePO> listByOriginalIds(List<String> originalIds);
} 