<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiTaskResultNewDao">

    <resultMap id="AiTaskResultNew" type="com.faw.work.ais.model.AiTaskResultNew" >
        <result column="id" property="id" />
        <result column="system_id" property="systemId" />
        <result column="task_type" property="taskType" />
        <result column="task_status" property="taskStatus" />
        <result column="biz_type" property="bizType" />
        <result column="biz_id" property="bizId" />
        <result column="batch_id" property="batchId" />
        <result column="trace_id" property="traceId" />
        <result column="ai_result" property="aiResult" />
        <result column="ai_explain" property="aiExplain" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />

        <result column="file_raw_list" property="fileRawList" />
        <result column="file_id" property="fileId" />
        <result column="content_type" property="contentType" />

        <result column="ai_result_str" property="aiResultStr" />

    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `system_id`,
        `task_type`,
        `task_status`,
        `biz_type`,
        `biz_id`,
        `batch_id`,
        `trace_id`,
        `ai_result`,
        `ai_explain`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_task_result_new (
            `system_id`,
            `task_type`,
            `task_status`,
            `biz_type`,
            `biz_id`,
            `batch_id`,
            `trace_id`,
            `ai_result`,
            `ai_explain`
        )
        VALUES(
            #{aiTaskResultNew.systemId},
            #{aiTaskResultNew.taskType},
            #{aiTaskResultNew.taskStatus},
            #{aiTaskResultNew.bizType},
            #{aiTaskResultNew.bizId},
            #{aiTaskResultNew.batchId},
            #{aiTaskResultNew.traceId},
            #{aiTaskResultNew.aiResult},
            #{aiTaskResultNew.aiExplain}
        )
    </insert>
    <insert id="insertBatch"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_task_result_new (
            `system_id`,
            `task_type`,
            `task_status`,
            `biz_type`,
            `biz_id`,
            `batch_id`,
            `trace_id`,
            `ai_result`,
            `ai_explain`
        )
        VALUES
        <foreach collection="aiTaskResultNews" item="aiTaskResultNew" separator=",">
        (
                  #{aiTaskResultNew.systemId},
                  #{aiTaskResultNew.taskType},
                  #{aiTaskResultNew.taskStatus},
                  #{aiTaskResultNew.bizType},
                  #{aiTaskResultNew.bizId},
                  #{aiTaskResultNew.batchId},
                  #{aiTaskResultNew.traceId},
                  #{aiTaskResultNew.aiResult},
                  #{aiTaskResultNew.aiExplain}
              )
        </foreach>
    </insert>
    <insert id="insertBatchByCreateTime"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_task_result_new (
        `system_id`,
        `task_type`,
        `task_status`,
        `biz_type`,
        `biz_id`,
        `batch_id`,
        `trace_id`,
        `ai_result`,
        `ai_explain`,create_time
        )
        VALUES
        <foreach collection="aiTaskResultNews" item="aiTaskResultNew" separator=",">
            (
            #{aiTaskResultNew.systemId},
            #{aiTaskResultNew.taskType},
            #{aiTaskResultNew.taskStatus},
            #{aiTaskResultNew.bizType},
            #{aiTaskResultNew.bizId},
            #{aiTaskResultNew.batchId},
            #{aiTaskResultNew.traceId},
            #{aiTaskResultNew.aiResult},
            #{aiTaskResultNew.aiExplain},#{aiTaskResultNew.createTime}
            )
        </foreach>
    </insert>
    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM ai_task_result_new
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </delete>

    <delete id="deleteByIds" parameterType="java.util.Map" >
        DELETE FROM ai_task_result_new
        where id  IN (
        <foreach collection="ids" item="code" separator=",">
            #{code}
        </foreach>
        )
    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE ai_task_result_new
         <set>
            <if test="aiTaskResultNew.systemId != null and aiTaskResultNew.systemId != '' " >
                system_id = #{aiTaskResultNew.systemId},
            </if>
            <if test="aiTaskResultNew.taskType != null and aiTaskResultNew.taskType != '' " >
                task_type = #{aiTaskResultNew.taskType},
            </if>
            <if test="aiTaskResultNew.taskStatus != null and aiTaskResultNew.taskStatus != '' " >
                task_status = #{aiTaskResultNew.taskStatus},
            </if>
            <if test="aiTaskResultNew.bizType != null and aiTaskResultNew.bizType != '' " >
                biz_type = #{aiTaskResultNew.bizType},
            </if>
            <if test="aiTaskResultNew.bizId != null and aiTaskResultNew.bizId != '' " >
                biz_id = #{aiTaskResultNew.bizId},
            </if>
            <if test="aiTaskResultNew.batchId != null and aiTaskResultNew.batchId != '' " >
                batch_id = #{aiTaskResultNew.batchId},
            </if>
            <if test="aiTaskResultNew.traceId != null and aiTaskResultNew.traceId != '' " >
                trace_id = #{aiTaskResultNew.traceId},
            </if>
            <if test="aiTaskResultNew.aiResult != null and aiTaskResultNew.aiResult != '' " >
                ai_result = #{aiTaskResultNew.aiResult},
            </if>
            <if test="aiTaskResultNew.aiExplain != null and aiTaskResultNew.aiExplain != '' " >
                ai_explain = #{aiTaskResultNew.aiExplain},
            </if>
         </set>
        where id = #{aiTaskResultNew.id}
    </update>


    <select id="getAiTaskResultNewList" resultMap="AiTaskResultNew">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_task_result_new
        <where>

          <if test="aiTaskResultNew.systemId != null and aiTaskResultNew.systemId != '' " >
              AND  system_id = #{aiTaskResultNew.systemId}
          </if>
          <if test="aiTaskResultNew.taskType != null and aiTaskResultNew.taskType != '' " >
              AND  task_type = #{aiTaskResultNew.taskType}
          </if>
          <if test="aiTaskResultNew.taskStatus != null and aiTaskResultNew.taskStatus != '' " >
              AND  task_status = #{aiTaskResultNew.taskStatus}
          </if>
          <if test="aiTaskResultNew.bizType != null and aiTaskResultNew.bizType != '' " >
              AND  biz_type = #{aiTaskResultNew.bizType}
          </if>
          <if test="aiTaskResultNew.bizId != null and aiTaskResultNew.bizId != '' " >
              AND  biz_id = #{aiTaskResultNew.bizId}
          </if>
          <if test="aiTaskResultNew.batchId != null and aiTaskResultNew.batchId != '' " >
              AND  batch_id = #{aiTaskResultNew.batchId}
          </if>
          <if test="aiTaskResultNew.traceId != null and aiTaskResultNew.traceId != '' " >
              AND  trace_id = #{aiTaskResultNew.traceId}
          </if>
          <if test="aiTaskResultNew.aiResult != null and aiTaskResultNew.aiResult != '' " >
              AND  ai_result = #{aiTaskResultNew.aiResult}
          </if>
          <if test="aiTaskResultNew.aiExplain != null and aiTaskResultNew.aiExplain != '' " >
              AND  ai_explain = #{aiTaskResultNew.aiExplain}
          </if>
          <if test="aiTaskResultNew.createTime != null and aiTaskResultNew.createTime != '' " >
              AND  create_time = #{aiTaskResultNew.createTime}
          </if>
          <if test="aiTaskResultNew.updateTime != null and aiTaskResultNew.updateTime != '' " >
              AND  update_time = #{aiTaskResultNew.updateTime}
          </if>

        </where>
     </select>

    <select id="getAiTaskResultNewById" parameterType="java.util.Map" resultMap="AiTaskResultNew">
        SELECT <include refid="Base_Column_List" />
        FROM ai_task_result_new

        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </select>

    <select id="getAiTaskResultOldList" fetchSize="2000" resultMap="AiTaskResultNew">
        SELECT
             file_raw_list,file_id,content_type,
            `system_id`,
            `task_type`,
            `task_status`,
            `biz_type`,
            `biz_id`,
            `batch_id`,
            `trace_id`,
            `ai_result` as ai_result_str,
            `ai_explain`,
            `create_time`,
            `update_time`
        FROM ai_task_result
        where create_time >= #{aiTaskResultNew.startTime}
        and create_time <![CDATA[<=]]>  #{aiTaskResultNew.endTime}

    </select>

    <select id="getAiTaskResultThisList" resultMap="AiTaskResultNew">
        SELECT id,
            `system_id`,
            `task_type`,
            `task_status`,
            `biz_type`,
            `biz_id`,
            `batch_id`,
            `trace_id`,
            `ai_result`,
            `ai_explain`,
            `create_time`,
            `update_time`
        FROM ai_task_result_new
        where create_time >= #{aiTaskResultNew.startTime}
          and create_time <![CDATA[<=]]>  #{aiTaskResultNew.endTime}

    </select>

    <select id="getAiTaskResultNewOne" resultMap="AiTaskResultNew">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_task_result_new
        where batch_id = #{aiTaskResultNew.batchId}
        and trace_id = #{aiTaskResultNew.traceId}
    </select>
</mapper>

