package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.response.FaqChatResponse;
import com.faw.work.ais.aic.model.response.FaqKnowledgeListResponse;
import com.faw.work.ais.aic.model.response.ExcelFaqImportResultResponse;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * FAQ知识Service接口
 * <AUTHOR>
 */
public interface FaqKnowledgeService extends IService<FaqKnowledgePO> {

    /**
     * 创建知识
     *
     * @param request 知识请求对象
     * @return 知识ID
     */
    String createKnowledge(FaqKnowledgeCreateRequest request);

    /**
     * 更新知识
     *
     * @param request 知识请求对象
     * @return 是否成功
     */
    boolean updateKnowledge(FaqKnowledgeUpdateRequest request);

    /**
     * 删除知识
     *
     * @param id 知识ID
     */
    void deleteKnowledge(String id);

    /**
     * 获取知识详情
     *
     * @param id  知识ID
     * @param env 环境
     * @return 知识详情
     */
    FaqKnowledgePO getKnowledgeDetail(String id, String env);

    /**
     * 分页查询知识列表
     *
     * @param request 查询请求对象
     * @return 分页结果
     */
    IPage<FaqKnowledgePO> pageKnowledge(FaqKnowledgeQueryRequest request);

    /**
     * 通过问题搜索相似知识
     *
     * @param question 查询问题
     * @param topK     返回结果数量
     * @return 最匹配的知识
     */
    List<FaqKnowledgePO> searchByQuestion(String question, int topK);

    /**
     * 导入excel
     *
     * @param file 文件
     * @return {@link ExcelFaqImportResultResponse }
     */
    ExcelFaqImportResultResponse importExcel(MultipartFile file);

    /**
     * 导入阿里excel
     *
     * @param file 文件
     * @return {@link ExcelFaqImportResultResponse }
     */
    ExcelFaqImportResultResponse importAliExcel(MultipartFile file);


    /**
     * 批量保存知识并向量化存储
     *
     * @param requestList 知识请求对象列表
     * @return 保存成功的知识数量
     */
    int batchSaveKnowledgeWithVector(List<FaqKnowledgeRequest> requestList);

    /**
     * 根据问题查询知识
     *
     * @param question   问题
     * @param categoryId 类目ID
     * @return 知识实体
     */
    FaqKnowledgePO getByQuestion(String question, String categoryId);

    /**
     * 根据机器人ID搜索知识
     *
     * @param request 要求
     * @return 最匹配的知识列表
     */
    List<FaqKnowledgeResponse> searchByRobotId(FaqSearchByRobotRequest request);


    /**
     * 知识转移
     * @param request 转移请求
     */
    void transferKnowledge(FaqKnowledgeTransferRequest request);

    /**
     * 复制知识和相似问到目标类目
     *
     * @param request 复制请求对象
     */
    void copyKnowledge(FaqKnowledgeCopyRequest request);

    /**
     * 与机器人进行聊天
     * @param request 聊天请求
     * @return 聊天响应
     */
    FaqChatResponse chatWithRobot(FaqChatRequest request);

    /**
     * 列表知识
     *
     * @param request 要求
     * @return {@link IPage }<{@link FaqKnowledgeListResponse }>
     */
    IPage<FaqKnowledgeListResponse> listReportKnowledge(FaqKnowledgeListRequest request);

    /**
     * 养狗话术知识入库
     *
     * @param request 请求
     */
    void dogKnowledgeInsert(FaqDogKnowledgeInsertRequest request);
}
