<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiAuditModelPointDao">

    <resultMap id="AiAuditModelPoint" type="com.faw.work.ais.model.AiAuditModelPoint" >
        <result column="id" property="id" />
        <result column="audit_id" property="auditId" />
        <result column="audit_point_name" property="auditPointName" />
        <result column="create_time" property="createTime" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_code" property="updateUserCode" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `audit_id`,
        `audit_point_name`,
        `create_time`,
        `create_user_code`,
        `create_user_name`,
        `update_time`,
        `update_user_code`,
        `update_user_name`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_audit_model_point (
            `audit_id`,
            `audit_point_name`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES(
            #{aiAuditModelPoint.auditId},
            #{aiAuditModelPoint.auditPointName},
            #{aiAuditModelPoint.createUserCode},
            #{aiAuditModelPoint.createUserName},
            #{aiAuditModelPoint.updateUserCode},
            #{aiAuditModelPoint.updateUserName}
        )
    </insert>

    <insert id="insertBatch"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_audit_model_point (
            `audit_id`,
            `audit_point_name`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES
        <foreach collection="aiAuditModelPoints" item="aiAuditModelPoint" separator=",">
            (
                  #{aiAuditModelPoint.auditId},
                  #{aiAuditModelPoint.auditPointName},
                  #{aiAuditModelPoint.createUserCode},
                  #{aiAuditModelPoint.createUserName},
                  #{aiAuditModelPoint.updateUserCode},
                  #{aiAuditModelPoint.updateUserName}
              )
        </foreach>
    </insert>

    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM ai_audit_model_point
        where  audit_id = #{id}

    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE ai_audit_model_point
         <set>
            <if test="aiAuditModelPoint.auditId != null and aiAuditModelPoint.auditId != '' " >
                audit_id = #{aiAuditModelPoint.auditId},
            </if>
            <if test="aiAuditModelPoint.auditPointName != null and aiAuditModelPoint.auditPointName != '' " >
                audit_point_name = #{aiAuditModelPoint.auditPointName},
            </if>
            <if test="aiAuditModelPoint.createTime != null and aiAuditModelPoint.createTime != '' " >
                create_time = #{aiAuditModelPoint.createTime},
            </if>
            <if test="aiAuditModelPoint.createUserCode != null and aiAuditModelPoint.createUserCode != '' " >
                create_user_code = #{aiAuditModelPoint.createUserCode},
            </if>
            <if test="aiAuditModelPoint.createUserName != null and aiAuditModelPoint.createUserName != '' " >
                create_user_name = #{aiAuditModelPoint.createUserName},
            </if>
            <if test="aiAuditModelPoint.updateTime != null and aiAuditModelPoint.updateTime != '' " >
                update_time = #{aiAuditModelPoint.updateTime},
            </if>
            <if test="aiAuditModelPoint.updateUserCode != null and aiAuditModelPoint.updateUserCode != '' " >
                update_user_code = #{aiAuditModelPoint.updateUserCode},
            </if>
            <if test="aiAuditModelPoint.updateUserName != null and aiAuditModelPoint.updateUserName != '' " >
                update_user_name = #{aiAuditModelPoint.updateUserName},
            </if>
         </set>
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </update>


    <select id="getAiAuditModelPointList" resultMap="AiAuditModelPoint">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_audit_model_point
        <where>
          <if test="aiAuditModelPoint.id != null  " >
              AND  id = #{aiAuditModelPoint.id}
          </if>
          <if test="aiAuditModelPoint.auditId != null  " >
              AND  audit_id = #{aiAuditModelPoint.auditId}
          </if>
          <if test="aiAuditModelPoint.auditPointName != null and aiAuditModelPoint.auditPointName != '' " >
              AND  audit_point_name = #{aiAuditModelPoint.auditPointName}
          </if>
          <if test="aiAuditModelPoint.createTime != null and aiAuditModelPoint.createTime != '' " >
              AND  create_time = #{aiAuditModelPoint.createTime}
          </if>
          <if test="aiAuditModelPoint.createUserCode != null and aiAuditModelPoint.createUserCode != '' " >
              AND  create_user_code = #{aiAuditModelPoint.createUserCode}
          </if>
          <if test="aiAuditModelPoint.createUserName != null and aiAuditModelPoint.createUserName != '' " >
              AND  create_user_name = #{aiAuditModelPoint.createUserName}
          </if>
          <if test="aiAuditModelPoint.updateTime != null and aiAuditModelPoint.updateTime != '' " >
              AND  update_time = #{aiAuditModelPoint.updateTime}
          </if>
          <if test="aiAuditModelPoint.updateUserCode != null and aiAuditModelPoint.updateUserCode != '' " >
              AND  update_user_code = #{aiAuditModelPoint.updateUserCode}
          </if>
          <if test="aiAuditModelPoint.updateUserName != null and aiAuditModelPoint.updateUserName != '' " >
              AND  update_user_name = #{aiAuditModelPoint.updateUserName}
          </if>

        </where>
     </select>

    <select id="getAiAuditModelPointById" parameterType="java.util.Map" resultMap="AiAuditModelPoint">
        SELECT <include refid="Base_Column_List" />
        FROM ai_audit_model_point
        where  id = #{id}

    </select>



</mapper>

