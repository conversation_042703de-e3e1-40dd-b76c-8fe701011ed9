package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文本分段配置分页请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "文本分段配置分页请求")
public class RagDocumentSplitConfigPageRequest {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "文档id")
    private Long documentId;

    @Schema(description = "分段策略(00-智能切分 01-自定义切分)")
    private String chunkStrategy;
    
    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页数量", defaultValue = "10")
    private Integer pageSize = 10;
} 