package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发布状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {
    /**
     * 未发布
     */
    UNPUBLISHED(0, "未发布"),
    /**
     * 已发布
     */
    PUBLISHED(1, "已发布"),
    /**
     * 发布中
     */
    PUBLISHING(2, "发布中"),
    /**
     * 发布失败
     */
    FAILED(10, "发布失败");

    private final int code;
    private final String desc;

    public static StatusEnum getByCode(int code) {
        for (StatusEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return UNPUBLISHED;
    }
} 