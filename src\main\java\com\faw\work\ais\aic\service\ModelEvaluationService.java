package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.ModelEvaluationRequest;
import com.faw.work.ais.aic.model.response.ModelEvaluationResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 大模型评测服务接口
 * <AUTHOR>
 */
public interface ModelEvaluationService {
    
    /**
     * 执行大模型评测
     *
     * @param file Excel文件，包含user_input和model_result两列
     * @param request 大模型调用参数
     * @return 评测结果，包含下载链接
     */
    ModelEvaluationResponse evaluateModel(MultipartFile file, ModelEvaluationRequest request);
}
