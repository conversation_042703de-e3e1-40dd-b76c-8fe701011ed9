package com.faw.work.ais.aic.common.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public class StrUtils {

    public static final String JSON_TAG = "```json";

    /**
     * 清理文本，移除多余空格和处理多行换行
     *
     * @param rawText 原始文本
     * @return 处理后的文本
     */
    public static String cleanText(String rawText) {
        if (rawText == null) {
            return "";
        }

        // 首先将多行换行替换为单个换行符
        String singleLineBreak = rawText.replaceAll("\\n+", "\n");

        // 然后替换所有空白字符（包括空格、制表符等，但不包括刚处理的换行符）为单个空格
        String normalized = singleLineBreak.replaceAll("\\s&&[^\n" +
                "]+", " ");

        // 移除首尾空格
        return normalized.trim();
    }

    /**
     * 从时间戳格式的文本中提取时间信息
     *
     * @param line 包含时间戳的对话行
     * @return 提取的时间字符串，格式为 HH:mm:ss，如果没有找到则返回null
     */
    private static String extractTime(String line) {
        // 匹配时间格式：HH:mm:ss
        Pattern timePattern = Pattern.compile("^(\\d{2}:\\d{2}:\\d{2})");
        Matcher matcher = timePattern.matcher(line.trim());
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 为对话行添加结束时间
     *
     * @param lines 所有对话行
     * @return 添加了结束时间的对话行列表
     */
    private static List<String> addEndTime(List<String> lines) {
        List<String> result = new ArrayList<>();

        for (int i = 0; i < lines.size(); i++) {
            String currentLine = lines.get(i);
            String endTime = null;

            // 查找下一行的开始时间作为当前行的结束时间
            if (i + 1 < lines.size()) {
                endTime = extractTime(lines.get(i + 1));
            }

            // 如果找到了结束时间，添加到当前行
            if (endTime != null) {
                result.add(currentLine + "  " + endTime);
            } else {
                // 如果是最后一行或者下一行没有时间戳，保持原样
                result.add(currentLine);
            }
        }

        return result;
    }

    public static List<String> sliceConversation(String fullConversation,
                                                 String customerSpeaker,
                                                 int minCustomerSentences,
                                                 int overlapLines) {
        if (StrUtil.isBlank(fullConversation)) {
            return new ArrayList<>();
        }

        // 分割并过滤空行
        String[] lines = fullConversation.split("\\r?\\n");
        List<String> allLines = new ArrayList<>();
        for (String line : lines) {
            if (StrUtil.isNotBlank(line)) {
                allLines.add(line);
            }
        }

        if (allLines.isEmpty()) {
            return new ArrayList<>();
        }

        // 为所有对话行添加结束时间
        List<String> linesWithEndTime = addEndTime(allLines);

        List<String> slices = new ArrayList<>();
        int startIndex = 0;
        int customerCount = 0;

        // 遍历所有对话行
        for (int i = 0; i < linesWithEndTime.size(); i++) {
            String line = linesWithEndTime.get(i);

            // 检查是否包含客户语句
            if (line.contains(customerSpeaker + ": ")) {
                customerCount++;

                // 当找到足够的客户语句时
                if (customerCount >= minCustomerSentences) {
                    // 添加片段：[startIndex, i]
                    slices.add(joinLines(linesWithEndTime, startIndex, i));

                    // 计算下一个片段的起始位置
                    if (overlapLines > 0) {
                        // 有重叠：从当前位置往前退overlapLines行（但不能超过当前startIndex）
                        startIndex = Math.max(startIndex, i - overlapLines + 1);
                    } else {
                        // 无重叠：从当前客户语句开始作为下一个片段的起点
                        startIndex = i;
                    }

                    // 重置计数器，并将当前客户语句计入新片段
                    customerCount = 1;
                }
            }
        }

        // 处理剩余的文本（如果还有未处理的内容）
        if (startIndex < linesWithEndTime.size()) {
            slices.add(joinLines(linesWithEndTime, startIndex, linesWithEndTime.size() - 1));
        }

        return slices;
    }

    private static String joinLines(List<String> lines, int start, int end) {
        if (start < 0 || end >= lines.size() || start > end) {
            return "";
        }

        StringBuilder slice = new StringBuilder();
        for (int i = start; i <= end; i++) {
            slice.append(lines.get(i)).append("\n");
        }
        return slice.toString();
    }

    /**
     * 剔除字符串中多余的代码块标记，只保留纯JSON内容
     *
     * @param input 可能包含多余标记的字符串
     * @return 清理后的纯JSON字符串
     */
    public static String cleanJsonString(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 匹配整个 ```json ... ``` 块，提取中间内容
        Pattern pattern = Pattern.compile("(?s)```json\\s*(.*?)\\s*```");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // 如果没有匹配，原样返回
        return input.trim();
    }

}