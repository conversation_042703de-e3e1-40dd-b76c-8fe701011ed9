package com.faw.work.ais.interceptor;


import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.config.UgcConfig;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.SpringUtils;
import com.faw.work.ais.common.util.UcgTokenutils;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.Duration;

/**
 * @ClassName UcgApiAuthorizationInterceptor
 * @desc 开放api网关调用请求拦截器
 * <AUTHOR>
 * @Date 2023年10月31日15:35:39
 * @Version 1.0
 **/
@Component
public class UcgApiAuthorizationInterceptor implements HandlerInterceptor {

    Logger logger = LoggerFactory.getLogger(UcgApiAuthorizationInterceptor.class);

    @Resource
    private UgcConfig ugcConfig;

    @Resource
    private RedissonClient redissonClient;

    // 开放api网关token
    private final String tokenKey = "ucg:ais:gatewayToken";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String authorization = request.getHeader("access_token");
        if(StringUtils.isBlank(authorization)){
            throw new BizException("access_token为空");
        }
        String token;
        // redis缓存中获取token
        RBucket<String> tokenBucket = redissonClient.getBucket(tokenKey);
        if(tokenBucket.isExists()){
            token = tokenBucket.get();
        } else {
            JSONObject jsonObject = UcgTokenutils.getUcgAccessToken(SpringUtils.getActiveProfile(),ugcConfig.getAppKey(),ugcConfig.getAppSecret());
            Integer successCode = 200;
            if (successCode.equals(jsonObject.getInteger("code"))) {
                token = jsonObject.getJSONObject("data").getString("access_token");
                long expireTime = jsonObject.getJSONObject("data").getLong("expire");
                tokenBucket.set(token);
                // 设置过期时间和ugc平台返回的一致
                tokenBucket.expire(Duration.ofSeconds(expireTime));
            }else {
                logger.info("获取工作台token失败:{}",jsonObject);
                throw new BizException("获取工作台token失败" + jsonObject);
            }
        }
        // todo token对比
        logger.info(token);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 暂不需要
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserThreadLocalUtil.clear();
    }

}
