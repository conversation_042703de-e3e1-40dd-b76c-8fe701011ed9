package com.faw.work.ais.common.exception;

import com.faw.work.ais.common.enums.IEnum;

public class CustomErrorWrapper implements IEnum {

    public CustomErrorWrapper(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private String code;

    private String msg;

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String msg() {
        return this.msg;
    }
}
