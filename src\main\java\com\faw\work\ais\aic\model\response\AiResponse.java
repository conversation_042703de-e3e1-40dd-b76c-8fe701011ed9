package com.faw.work.ais.aic.model.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI模型响应结果
 * <AUTHOR>
 */
@Schema(description = "数据AI模型响应结果")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiResponse {

    /**
     * 响应内容
     */
    @Schema(description = "响应内容")
    private String content;


}

