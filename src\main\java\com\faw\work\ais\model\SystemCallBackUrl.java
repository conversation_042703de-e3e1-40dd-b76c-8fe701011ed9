package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * system_call_back_url
 * <AUTHOR>
@Schema(description = "回调地址表")
public class SystemCallBackUrl implements Serializable {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 系统id
     */
    @Schema(description = "系统ID")
    private String systemId;

    /**
     * 回调地址完整服务路径；
     */
    @Schema(description = "回调地址完整服务路径")
    private String callBackUrl;

    /**
     * 系统名称
     */
    @Schema(description = "系统名称")
    private String systemName;

    /**
     * 停用标识；1-停用；0-未停用；
     */
    @Schema(description = "停用标识；1-停用；0-未停用；")
    private String stop;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getCallBackUrl() {
        return callBackUrl;
    }

    public void setCallBackUrl(String callBackUrl) {
        this.callBackUrl = callBackUrl;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getStop() {
        return stop;
    }

    public void setStop(String stop) {
        this.stop = stop;
    }
}