package com.faw.work.ais.feign;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.CallBackDTO;
import com.faw.work.ais.feign.interceptor.IworkOpenApiFeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.net.URI;

/**
 * iwork开放api  feign调用接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "callBack", url = "EMPTY" , configuration = IworkOpenApiFeignInterceptor.class)
public interface IworkOpenApiFeignClient {

    /**
     * ai动态回调接口测试用
     *
     * @return Object
     */
    @RequestMapping(method = RequestMethod.POST)
    String dynamicCallBackTest(URI uri, @RequestBody String test, @RequestHeader(value = "exportServer") String chargeOn);

    /**
     * ai动态回调接口
     *
     * @param callBackDTO 入参
     * @return Object
     */
    @RequestMapping(method = RequestMethod.POST)
    Response<Object> dynamicCallBack(URI uri, @RequestBody CallBackDTO callBackDTO);

}
