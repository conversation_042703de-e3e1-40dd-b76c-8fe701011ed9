package com.faw.work.ais.mapper.ais;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.entity.dto.ai.AiKanBanDTO;
import com.faw.work.ais.entity.dto.ai.AiTaskResultDTO;
import com.faw.work.ais.entity.vo.ai.*;
import com.faw.work.ais.model.AiTaskResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ai_task_result(ai任务结果记录表)】的数据库操作Mapper
* @createDate 2024-04-07 21:40:12
* @Entity generator.domain.AiTaskResult
*/
@Mapper
public interface AiTaskResultMapper extends BaseMapper<AiTaskResult> {

    AiTaskResult getResult(@Param("param") AiTaskResultDTO aiTaskResultDTO);

    Page<AiTaskResultVO> selectAiTaskResultPage(Page<AiTaskResultVO> page,
                                                @Param(Constants.WRAPPER) Wrapper wrappers);

    /**
     * 健康检测sql，任意查询一条数据
     * @return
     */
    int healthMonitorSql();

    /**
     * 获取ai请求时使用的文件的coskey
     * @param traceId
     * @return
     */
    String getFileIdsByTraceId(@Param("traceId") String traceId);

    /**
     * 获取看板报表上部数据
     * @param aiKanBanDTO
     * @return
     */
    KanbanTopDataVO getTopData(@Param("param") AiKanBanDTO aiKanBanDTO);

    KanBanTopDataRuleVO getTopDataRule(@Param("param") AiKanBanDTO aiKanBanDTO);

    String getAiResultSingle(@Param("batchId") String batchId, @Param("traceId") String traceId);

    String getAiResultFinal(@Param("batchId") String batchId);

    /**
     * 获取AI审核时间
     * @return
     */
    String getAiResultAndCreateTime(@Param("batchId") String batchId, @Param("traceId") String traceId);

    List<KanBanBillListVO> getBillList(@Param("param") AiKanBanDTO aiKanBanDTO);

    List<KanBanRuleListVO> getRuleList(@Param("batchId") String batchId, @Param("traceId") String traceId, @Param("taskType") String taskType);

    String getAiRate(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId);

    int updateResultStatusByTraceId(@Param("traceId") String traceId);
}




