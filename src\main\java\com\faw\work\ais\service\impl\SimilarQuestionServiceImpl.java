package com.faw.work.ais.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.service.impl.FaqSimilarKnowledgeServiceImpl;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.aic.mapper.faq.FaqSimilarKnowledgeMapper;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.service.SimilarQuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 相似问服务实现类
 * @deprecated 使用 {@link FaqSimilarKnowledgeServiceImpl} 替代
 */
@Service
@Slf4j
@Deprecated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SimilarQuestionServiceImpl extends ServiceImpl<FaqSimilarKnowledgeMapper, FaqSimilarKnowledgePO> implements SimilarQuestionService {

    private final MilvusService milvusService;
    private final EmbeddingService embeddingService;
    private final FaqSimilarKnowledgeMapper faqSimilarKnowledgeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSimilarQuestion(FaqSimilarKnowledgePO similarQuestion) {
        // 1. 校验参数
        if (similarQuestion == null || similarQuestion.getSimilarQuestion() == null) {
            throw new BizException("相似问不能为空");
        }
        if (similarQuestion.getKnowledgeId() == null) {
            throw new BizException("原问题ID不能为空");
        }

        // 2. 设置创建时间
        if (similarQuestion.getCreatedAt() == null) {
            similarQuestion.setCreatedAt(LocalDateTime.now());
        }

        // 3. 保存到数据库
        boolean dbResult = this.save(similarQuestion);
        if (!dbResult) {
            log.error("相似问保存失败: {}", similarQuestion);
            return;
        }

        // 4. 获取问题的向量表示
        try {
            float[] embedding = embeddingService.getEmbedding(similarQuestion.getSimilarQuestion());

            // 5. 存储到Milvus
            List<MilvusField> properties = new ArrayList<>();
            properties.add(new MilvusField("biz_info", similarQuestion.getKnowledgeId().toString()));
            properties.add(new MilvusField("content", similarQuestion.getSimilarQuestion()));

            milvusService.saveEmbedding(
                    MilvusPoolConfig.FAQ_COLLECTION_NAME,
                    similarQuestion.getId(),
                    properties,
                    embedding);

            log.info("相似问及其向量存储完成，ID: {}", similarQuestion.getId());
        } catch (Exception e) {
            log.error("相似问向量存储失败: {}", similarQuestion.getId(), e);
            // 向量存储失败，但数据库已存储成功，不回滚事务
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchSimilarQuestion(List<FaqSimilarKnowledgePO> similarQuestionList) {
        if (similarQuestionList == null || similarQuestionList.isEmpty()) {
            return true;
        }

        // 1. 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        for (FaqSimilarKnowledgePO similarQuestion : similarQuestionList) {
            if (similarQuestion.getCreatedAt() == null) {
                similarQuestion.setCreatedAt(now);
            }
        }

        // 2. 批量保存到数据库
        boolean dbResult = this.saveBatch(similarQuestionList);
        if (!dbResult) {
            log.error("批量保存相似问失败");
            return false;
        }

        // 3. 批量获取向量并存储到Milvus
        try {
            for (FaqSimilarKnowledgePO similarQuestion : similarQuestionList) {
                float[] embedding = embeddingService.getEmbedding(similarQuestion.getSimilarQuestion());

                List<MilvusField> properties = new ArrayList<>();
                properties.add(new MilvusField("biz_info", similarQuestion.getKnowledgeId().toString()));
                properties.add(new MilvusField("content", similarQuestion.getSimilarQuestion()));

                milvusService.saveEmbedding(
                        MilvusPoolConfig.FAQ_COLLECTION_NAME,
                        similarQuestion.getId(),
                        properties,
                        embedding);
            }

            log.info("批量保存相似问及其向量完成，数量: {}", similarQuestionList.size());
            return true;
        } catch (Exception e) {
            log.error("批量相似问向量存储失败", e);
            // 向量存储失败，但数据库已存储成功，不回滚事务
            return true;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSimilarQuestion(String id) {
        // 1. 校验参数
        if (id == null) {
            throw new BizException("相似问ID不能为空");
        }

        // 2. 从数据库删除
        boolean dbResult = this.removeById(id);
        if (!dbResult) {
            log.error("相似问删除失败: {}", id);
            return false;
        }

        // 3. 从Milvus删除
        try {
            List<String> ids = new ArrayList<>();
            ids.add(id);
            milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, ids, null);
            log.info("相似问及其向量删除完成，ID: {}", id);
            return true;
        } catch (Exception e) {
            log.error("相似问向量删除失败: {}", id, e);
            throw new BizException("相似问向量删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByOriginalId(String originalId) {
        // 1. 校验参数
        if (originalId == null) {
            throw new BizException("原问题ID不能为空");
        }

        // 2. 查询所有相关的相似问ID
        List<FaqSimilarKnowledgePO> similarQuestionList = faqSimilarKnowledgeMapper.selectByOriginalId(originalId);
        if (similarQuestionList.isEmpty()) {
            return true;
        }

        // 3. 从数据库删除
        int dbResult = faqSimilarKnowledgeMapper.deleteByOriginalId(originalId);
        if (dbResult == 0) {
            log.error("根据原问题ID删除相似问失败: {}", originalId);
            return false;
        }

        // 4. 从Milvus删除
        try {
            List<String> ids = similarQuestionList.stream()
                .map(FaqSimilarKnowledgePO::getId)
                .collect(Collectors.toList());
            
            if (!ids.isEmpty()) {
                milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, ids, null);
            }
            
            log.info("根据原问题ID删除相似问及其向量完成，原问题ID: {}, 删除数量: {}", originalId, similarQuestionList.size());
            return true;
        } catch (Exception e) {
            log.error("根据原问题ID删除相似问向量失败: {}", originalId, e);
            throw new BizException("根据原问题ID删除相似问向量失败: " + e.getMessage());
        }
    }

    @Override
    public FaqSimilarKnowledgePO getSimilarQuestionDetail(String id) {
        return this.getById(id);
    }

    @Override
    public List<FaqSimilarKnowledgePO> listByOriginalId(String originalId) {
        if (originalId == null) {
            throw new BizException("原问题ID不能为空");
        }
        return faqSimilarKnowledgeMapper.selectByOriginalId(originalId);
    }
} 