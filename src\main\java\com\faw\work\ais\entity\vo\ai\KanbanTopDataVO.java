package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 看板列表
 */
@Data
public class KanbanTopDataVO {

    @Schema(description = "单据审核准确率" )
    private String billCheckRightRate;

    @Schema(description = "AI和人工审核一致的数量；即 ai通过且人工通过+ai驳回且人工驳回")
    private String aiPassHumanPassSumAiBackHumanBac;

    @Schema(description = "AI审核单据数量-需求中无描述" )
    private String aiCheckBillNum;

    @Schema(description = "人工复审单据数量-需求中无描述" )
    private String hunmanCheckBillNum;

    @Schema(description = "AI审核通过(数量-单据维度)" )
    private String aiPassNum;

    @Schema(description = "准确率-(AI审核通过)" )
    private String aiPassRate;

    @Schema(description = "人工复审通过-AI审核通过-单据维度-需求没描述" )
    private String aiPassHunmanCheckPassNum;

    @Schema(description = "人工复审驳回-AI审核通过-单据维度-需求没描述" )
    private String aiPassHunmanCheckRackBackNum;

    @Schema(description = "AI审核驳回(数量-单据维度)" )
    private String aiBackNum;

    @Schema(description = "准确率-AI审核驳回(单据维度)" )
    private String aiBackRate;

    @Schema(description = "AI审核驳回-人工复审通过单据维度" )
    private String aiBackHumanPassNum;

    @Schema(description = "AI审核驳回-人工复审驳回单据维度" )
    private String aiBackHumanBackNum;

    @Schema(description = "规则维度的数据" )
    private KanBanTopDataRuleVO ruleData;

    @Schema(description = "看板的数据列表" )
    private List<KanBanBillListVO> kanBanBillDatas;
}
