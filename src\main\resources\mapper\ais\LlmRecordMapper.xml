<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.llm.LlmRecordMapper">


    <select id="selectByRequestId" resultType="com.faw.work.ais.aic.model.domain.LlmRecord">
        select * from llm_record where request_id = #{requestId}
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="bizType != null and bizType != ''">
            and biz_type = #{bizType}
        </if>

    </select>
</mapper>
