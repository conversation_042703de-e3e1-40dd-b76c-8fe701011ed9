package com.faw.work.ais.aic.model.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class OmniResponse {
    private String id;
    private String object;
    private Long created;
    private String model;
    private List<Choice> choices;
    private Usage usage;

    @Data
    public static class Choice {
        private Integer index;
        private OmniMessage message;
        private String finish_reason;
    }

    @Data
    public static class OmniMessage {
        private String role;
        private List<ContentItem> content;
    }

    @Data
    public static class ContentItem {
        private String type;
        private String text;
        private Map<String, Object> audio;
    }

    @Data
    public static class Usage {
        private Integer prompt_tokens;
        private Integer completion_tokens;
        private Integer total_tokens;
    }
}

