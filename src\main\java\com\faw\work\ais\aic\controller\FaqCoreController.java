package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.mapper.faq.FaqKnowledgeMapper;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.ExcelFaqImportResultResponse;
import com.faw.work.ais.aic.model.response.FaqChatResponse;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * FAQ核心功能Controller
 * 包含Excel导入、相似度查询和知识绑定等复杂逻辑接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/faq-core")
@Tag(name = "FAQ核心管理", description = "FAQ核心管理相关接口，包含Excel导入、相似度查询和知识绑定等复杂逻辑")
@Slf4j
public class FaqCoreController {

    @Autowired
    private  FaqKnowledgeMapper faqKnowledgeMapper;
    @Autowired
    private  FaqKnowledgeService faqKnowledgeService;


    @PostMapping("/import-excel")
    @Operation(summary = "文件Excel批量导入FAQ-已废弃", description = "[author:10200571]")
    @Deprecated
    public AiResult<ExcelFaqImportResultResponse> importExcel(@RequestParam("file") MultipartFile file) {
        log.info("接收到Excel批量导入FAQ知识请求");
        return AiResult.success(faqKnowledgeService.importExcel(file));
    }
    
    @PostMapping("/import-ali-excel")
    @Operation(summary = "阿里Excel批量导入FAQ", description = "[author:10200571]")
    public AiResult<ExcelFaqImportResultResponse> importAliExcel(@RequestParam("file") MultipartFile file) {
        log.info("接收到阿里Excel批量导入FAQ知识请求");
        return AiResult.success(faqKnowledgeService.importAliExcel(file));
    }
    
    @PostMapping("/search-by-robot")
    @Operation(summary = "根据机器人ID搜索FAQ", description = "[author:10200571]")
    public AiResult<List<FaqKnowledgeResponse>> searchByRobotId(@RequestBody @Valid FaqSearchByRobotRequest request) {
        log.debug("接收到根据机器人ID搜索FAQ知识请求: robotId={}, query={}", request.getRobotId(), request.getQuery());
        return AiResult.success(faqKnowledgeService.searchByRobotId(request));
    }


    @PostMapping("/chat")
    @Operation(summary = "机器人聊天", description = "[author:10200571]")
    public AiResult<FaqChatResponse> chat(@RequestBody @Valid FaqChatRequest request) {
        log.info("接收到机器人聊天请求: robotId={}, query={}, debugMode={}, similarityThreshold={}", request.getRobotId(), request.getQuery(), request.getDebugMode(), request.getSimilarityThreshold());
        FaqChatResponse response = faqKnowledgeService.chatWithRobot(request);
        return AiResult.success(response);
    }


    @PostMapping("/dog-knowledge-insert")
    @Operation(summary = "养狗-话术知识入库", description = "[author:10200571]")
    public AiResult<String> dogKnowledgeInsert(@RequestBody @Valid FaqDogKnowledgeInsertRequest request) {
        faqKnowledgeService.dogKnowledgeInsert(request);
        return AiResult.success("养狗-话术知识入库成功");
    }

    @PostMapping("/dog-knowledge-count")
    @Operation(summary = "养狗-版本号查询知识数 ", description = "[author:10200571]")
    public AiResult<Integer> dogKnowledgeCount(@RequestBody @Valid FaqDogKnowledgeQueryRequest request) {
        return AiResult.success(faqKnowledgeMapper.dogKnowledgeCount(request.getRobotId(),request.getKnowledgeVersion()));
    }


    @PostMapping("/searchByQuestion")
    @Operation(summary = "养狗-版本号查询知识数 ", description = "[author:10200571]")
    public AiResult<List<FaqKnowledgePO>> searchByQuestion(@RequestBody @Valid FaqLingKnowledgeQueryRequest request) {
        return AiResult.success(faqKnowledgeService.searchByQuestion(request.getQuestion(),request.getTopK()));
    }

}