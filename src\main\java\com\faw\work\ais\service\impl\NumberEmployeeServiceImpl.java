package com.faw.work.ais.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeAiCoverInfoDTO;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.dto.ai.ProcessInfoDTO;
import com.faw.work.ais.entity.vo.ai.*;
import com.faw.work.ais.mapper.ais.NumberEmployeeMapper;
import com.faw.work.ais.mapper.ais.NumberEmployeeSplitMapper;
import com.faw.work.ais.mapper.ais.SystemCallBackUrlMapper;
import com.faw.work.ais.mapper.bupcore.TaskInfoMapper;
import com.faw.work.ais.service.NumberEmployeeService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数字员工看板service
 */
@Service
@Slf4j
public class NumberEmployeeServiceImpl implements NumberEmployeeService {

    @Autowired
    private NumberEmployeeMapper numberEmployeeMapper;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private SystemCallBackUrlMapper systemCallBackUrlMapper;

    @Autowired
    private NumberEmployeeSplitMapper numberEmployeeSplitMapper;

    @Value("${numberEmployee.handler}")
    private String dealName;

    @Value("${numberEmployee.dealerCode}")
    private String dealerCode;

    @Value("${aiSave.saveTime}")
    private Integer aiSaveTimeConfig;

    @Value("${aiSave.saveCost}")
    private Integer aiSaveCostConfig;

    /**
     * AI节省的时间
     */
    final String AI_SAVE_FLAG_TIME = "1";
    /**
     * AI节省的费用
     */
    final String AI_SAVE_FLAG_COST = "2";

    @Override
    public NumberEmployeeVO getInfo(NumberEmployeeDTO numberEmployeeDTO) {
        // 获取查询的业务单元编码
        List<String> getBizCodes = getBizCodeByL3FlowCodeOrBizType(numberEmployeeDTO);
        // 获取处理人对应的业务单元信息
        List<BizUnitInfoVO> bizUnitInfoVOS = taskInfoMapper.getBizUnitInfos(dealName);
        if(CollectionUtils.isEmpty(bizUnitInfoVOS)){
            throw new BizException("找不到处理人对应的业务单元！");
        }

        // AI覆盖角色数
//        Integer aiCoverRoleNum = taskInfoMapper.getAiCoverRoleNum(dealName);
        // AI覆业务单元则数
//        Integer aiCoverBizNum = taskInfoMapper.getAiCoverBizNum(dealName);
        // AI覆盖规则数
//        Integer aiCoverRuleNum = numberEmployeeMapper.getAiCoverRuleNumBybizUnitInfos(bizUnitInfoVOS);

        // AI触发任务数信息
        NumberEmployeeVO aiTriggerTaskInfo = numberEmployeeMapper.getAiTouchTaskInfo(numberEmployeeDTO, getBizCodes);
        // AI触发规则数信息
        NumberEmployeeVO aiTriggerRuleInfo = numberEmployeeMapper.getAiTouchRuleInfo(numberEmployeeDTO, getBizCodes);
        // 抽检任务数信息 + 人工抽检准确率_圆饼图(单据维度)
        HumanSampleRightRateVO aiSampleInfo = numberEmployeeMapper.getSampleTaskInfo(numberEmployeeDTO, getBizCodes);
        // 抽检任务数信息 + 人工抽检准确率_圆饼图(规则维度)
        HumanSampleRightRateRuleVO aiSampleRuleInfo = numberEmployeeMapper.getSampleTaskRuleInfo(numberEmployeeDTO, getBizCodes);

        // AI通过率折线图 或者 人工抽检准确率_折线（单据维度） 或者 人工抽检准确率_折线（规则维度）
        List<RateTrendChartVO> aiPassRates = getTableLineInfo(numberEmployeeDTO, getBizCodes,"");

        // 组装数字员工看板结果
        NumberEmployeeVO numberEmployeeVO = new NumberEmployeeVO();
        numberEmployeeVO.setAiCoverSceneNum("0");
//        if(aiCoverRoleNum != null){
//            numberEmployeeVO.setAiCoverRoleNum(aiCoverRoleNum.toString());
//        }
//        if(aiCoverBizNum != null){
//            numberEmployeeVO.setAiCoverBizNum(aiCoverBizNum.toString());
//        }
//        if(aiCoverRuleNum != null){
//            numberEmployeeVO.setAiCoverRuleNum(aiCoverRuleNum.toString());
//        }
        if(aiTriggerTaskInfo != null){
            numberEmployeeVO.setAiTriggerTaskNum(aiTriggerTaskInfo.getAiTriggerTaskNum());
            numberEmployeeVO.setAiTriggerTaskPassNum(aiTriggerTaskInfo.getAiTriggerTaskPassNum());
            numberEmployeeVO.setAiTriggerTaskRate(aiTriggerTaskInfo.getAiTriggerTaskRate());
        }
        if(aiTriggerRuleInfo != null){
            numberEmployeeVO.setAiTriggerRuleNum(aiTriggerRuleInfo.getAiTriggerRuleNum());
            numberEmployeeVO.setAiTriggerRulePassNum(aiTriggerRuleInfo.getAiTriggerRulePassNum());
            numberEmployeeVO.setAiTriggerRulePassRate(aiTriggerRuleInfo.getAiTriggerRulePassRate());
        }
        // 抽检任务数（单据）
        if (aiSampleInfo != null){
            numberEmployeeVO.setHumanSampleRightRate(aiSampleInfo);
            numberEmployeeVO.setSampleTaskRate(aiSampleInfo.getSampleRightRate());
            numberEmployeeVO.setSampleTaskNum(aiSampleInfo.getSampleTotalNum());

            // 使用 DecimalFormat 格式化结果为小数点后两位
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            Double aiUnpassHumanUnpassRate = Double.valueOf("100")
                    - Double.valueOf(aiSampleInfo.getAiPassHumanPassRate() == null ? "0" : aiSampleInfo.getAiPassHumanPassRate())
                    - Double.valueOf(aiSampleInfo.getAiUnpassHumanPassRate() == null ? "0" : aiSampleInfo.getAiUnpassHumanPassRate())
                    - Double.valueOf(aiSampleInfo.getAiPassHumanUnpassRate() == null ? "0" : aiSampleInfo.getAiPassHumanUnpassRate());
            // 格式化结果
            String formattedRate = decimalFormat.format(aiUnpassHumanUnpassRate);
            aiSampleInfo.setAiUnpassHumanUnpassRate(aiSampleInfo.getAiUnpassHumanUnpassRate() == null ? null : String.valueOf(formattedRate));
        }
        // 抽检任务数（规则）
        if(aiSampleRuleInfo != null){
            numberEmployeeVO.setHumanSampleRightRateRule(aiSampleRuleInfo);
            numberEmployeeVO.setSampleTaskRuleNum(aiSampleRuleInfo.getSampleTotalNum());
            numberEmployeeVO.setSampleTaskRuleRate(aiSampleRuleInfo.getSampleRightRate());

            // 使用 DecimalFormat 格式化结果为小数点后两位
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            Double aiUnpassHumanUnpassRate = Double.valueOf("100")
                    - Double.valueOf(aiSampleRuleInfo.getAiPassHumanPassRate() == null ? "0" : aiSampleRuleInfo.getAiPassHumanPassRate())
                    - Double.valueOf(aiSampleRuleInfo.getAiUnpassHumanPassRate() == null ? "0" : aiSampleRuleInfo.getAiUnpassHumanPassRate())
                    - Double.valueOf(aiSampleRuleInfo.getAiPassHumanUnpassRate() == null ? "0" : aiSampleRuleInfo.getAiPassHumanUnpassRate());
            // 格式化结果
            String formattedRate = decimalFormat.format(aiUnpassHumanUnpassRate);
            aiSampleRuleInfo.setAiUnpassHumanUnpassRate(aiSampleRuleInfo.getAiUnpassHumanUnpassRate() == null ? null : String.valueOf(formattedRate));
        }
        if(CollectionUtils.isNotEmpty(aiPassRates)){
            numberEmployeeVO.setTrendLine(aiPassRates);
        }
        return numberEmployeeVO;
    }
    @Override
    public List<RateTrendChartVO> getTableLineInfo(NumberEmployeeDTO numberEmployeeDTO, List<String> bizTypes, String aiSaveFlag) {
        NumberEmployeeDTO numberEmployeeCount = new NumberEmployeeDTO();
        BeanUtils.copyProperties(numberEmployeeDTO, numberEmployeeCount);

        numberEmployeeCount.setTotalType("1");
        List<RateTrendChartVO> list = new ArrayList<>();
        numberEmployeeCount.setSystemId("YSZC-ESC");
        List<RateAndDateVO> erShouCheVos = getRateAndDateWithSql(numberEmployeeCount, bizTypes,aiSaveFlag);
        if(CollectionUtils.isNotEmpty(erShouCheVos)){
            RateTrendChartVO  rateTrendChartVO = new RateTrendChartVO();
            rateTrendChartVO.setSystemId("YSZC-ESC");
            rateTrendChartVO.setSystemName("衍生支持-二手车");
            // 计算新增较昨日对比的数据
            List<RateAndDateVO> erShouCheLists =  compareYesterday(erShouCheVos);
            rateTrendChartVO.setRateAndDateVOList(erShouCheLists);
            list.add(rateTrendChartVO);
        }
        numberEmployeeCount.setSystemId("BNZX");
        List<RateAndDateVO> guobus = getRateAndDateWithSql(numberEmployeeCount, bizTypes,aiSaveFlag);
        if(CollectionUtils.isNotEmpty(guobus)){
            RateTrendChartVO  rateTrendChartVO = new RateTrendChartVO();
            rateTrendChartVO.setSystemId("BNZX");
            rateTrendChartVO.setSystemName("补能中心");
            // 计算新增较昨日对比的数据
            List<RateAndDateVO> guobuLists =  compareYesterday(guobus);
            rateTrendChartVO.setRateAndDateVOList(guobuLists);
            list.add(rateTrendChartVO);
        }
        numberEmployeeCount.setSystemId("YKYY");
        List<RateAndDateVO> yaokes = getRateAndDateWithSql(numberEmployeeCount, bizTypes,aiSaveFlag);
        if(CollectionUtils.isNotEmpty(yaokes)){
            RateTrendChartVO  rateTrendChartVO = new RateTrendChartVO();
            rateTrendChartVO.setSystemId("YKYY");
            rateTrendChartVO.setSystemName("要客运营中心");
            // 计算新增较昨日对比的数据
            List<RateAndDateVO> yaokeLists =  compareYesterday(yaokes);
            rateTrendChartVO.setRateAndDateVOList(yaokeLists);
            list.add(rateTrendChartVO);
        }
        return list;
    }

    /**
     * 计算两个相邻日期准确率差，今天-昨天
     * @param nowList
     * @return
     */
    private List<RateAndDateVO> compareYesterday(List<RateAndDateVO> nowList){
        List<RateAndDateVO> newList = new ArrayList<>();
        for (int i = 1; i < nowList.size(); i++) {
            RateAndDateVO current = nowList.get(i);
            RateAndDateVO previous = nowList.get(i - 1);
            try {
                double currentRate = Double.parseDouble(current.getBillRate() == null ? "0" : current.getBillRate());
                double previousRate = Double.parseDouble(previous.getBillRate() == null ? "0" : previous.getBillRate());
                // 计算差值，并转换为String类型存储
                if(StringUtils.isEmpty(previous.getBillRate())){
                    current.setCompareYesterdayRate("-");
                }else {
                    double difference = currentRate - previousRate;
                    // 使用 DecimalFormat 格式化结果为小数点后两位
                    DecimalFormat decimalFormat = new DecimalFormat("0.00");
                    Double diffenceRate = Double.valueOf(currentRate)
                            - Double.valueOf(previousRate);
                    // 格式化结果
                    String formattedRate = decimalFormat.format(diffenceRate);
                    current.setCompareYesterdayRate(formattedRate == null ? "-" : String.valueOf(formattedRate));
                }
            } catch (NumberFormatException e) {
                log.error("----数字类型转换错误-----" + e.getMessage(), e);
                throw new BizException("数字类型转换错误!");
            }
            newList.add(current);
        }
        RateAndDateVO firstData = nowList.get(0);
        firstData.setCompareYesterdayRate("-");
        newList.add(firstData);
        return nowList;
    }

    /**
     * 根据维度结算统计日期
     * @param aiKanBanDTO
     * @param bizTypes
     * @param aiSaveFlag ai节省类型标识； 1：保存时间，2：保存成本
     *
     * @return
     */
    private List<RateAndDateVO> getRateAndDateWithSql(NumberEmployeeDTO aiKanBanDTO, List<String> bizTypes, String aiSaveFlag){
        List<RateAndDateVO> totalList = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 天维度计算准确率
        if("1".equals(aiKanBanDTO.getTotalType())){
            // 使用DateTimeFormatter来解析和格式化日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            DateTimeFormatter formatterDay = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startDate = null;
            LocalDate endDate = null;
            if (StringUtils.isNotEmpty(aiKanBanDTO.getAiCheckDateStart()) && StringUtils.isNotEmpty(aiKanBanDTO.getAiCheckDateEnd())) {
                startDate = LocalDateTime.parse(aiKanBanDTO.getAiCheckDateStart(), formatter).toLocalDate();
                endDate = LocalDateTime.parse(aiKanBanDTO.getAiCheckDateEnd(), formatter).toLocalDate();
                // 计算并打印两个日期之间相差的天数
                long daysBetween = ChronoUnit.DAYS.between(endDate, startDate);
                long absoluteDaysBetween = Math.abs(daysBetween);
                if(absoluteDaysBetween > 30){
                    throw new BizException("日期间隔不能大于30天，请重新选择！");
                }
            }
            if (startDate != null && endDate != null) {
                // 两个日期都不为空，输出两个日期之间的所有日期（包括起始和结束日期）
                RateAndDateVO rateAndDateVO = new RateAndDateVO();
                // 将LocalDate对象格式化为字符串
                String startDateStr = startDate.format(formatterDay);
                String endDateStr = endDate.format(formatterDay);
                rateAndDateVO.setBillDateBegin(startDateStr.concat(" 00:00:00"));
                rateAndDateVO.setBillDateEnd(endDateStr.concat(" 23:59:59"));
                if("1".equals(aiKanBanDTO.getAiTriggerTaskFlag())){
                    totalList = numberEmployeeSplitMapper.getAiPassRateMonth(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                }
                if("1".equals(aiKanBanDTO.getSampleTaskFlag())){
                    totalList = numberEmployeeSplitMapper.getSampleRateMonth(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                }
                if("1".equals(aiKanBanDTO.getSampleTaskRuleFlag())){
                    totalList = numberEmployeeSplitMapper.getSampleRuleRateMonth(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                }
                if(AI_SAVE_FLAG_TIME.equals(aiSaveFlag)){
                    totalList = numberEmployeeSplitMapper.aiSaveTime(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes, aiSaveTimeConfig);
                }
                if(AI_SAVE_FLAG_COST.equals(aiSaveFlag)){
                    totalList = numberEmployeeSplitMapper.aiSaveCost(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes, aiSaveCostConfig);
                }
                totalList = dealMissDateAsEmptyData(totalList, rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd());
            } else {
                // 任一日期为空，输出从当前日期开始到前30天的所有日期
                LocalDate currentDate = LocalDate.now();
                LocalDate pastDate = currentDate.minusDays(29); // 加1是因为我们需要从当前日期开始的前30天
                RateAndDateVO rateAndDateVO = new RateAndDateVO();
                // 将LocalDate对象格式化为字符串
                String formattedDateStart = pastDate.format(formatterDay);
                String formattedDateEnd = currentDate.format(formatterDay);
                rateAndDateVO.setBillDateBegin(formattedDateStart.concat(" 00:00:00"));
                rateAndDateVO.setBillDateEnd(formattedDateEnd.concat(" 23:59:59"));
                if("1".equals(aiKanBanDTO.getAiTriggerTaskFlag())){
                    totalList = numberEmployeeSplitMapper.getAiPassRateMonth(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                }
                if("1".equals(aiKanBanDTO.getSampleTaskFlag())){
                    totalList = numberEmployeeSplitMapper.getSampleRateMonth(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                }
                if("1".equals(aiKanBanDTO.getSampleTaskRuleFlag())){
                    totalList = numberEmployeeSplitMapper.getSampleRuleRateMonth(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                }
                if(AI_SAVE_FLAG_TIME.equals(aiSaveFlag)){
                    totalList = numberEmployeeSplitMapper.aiSaveTime(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes, aiSaveTimeConfig);
                }
                if(AI_SAVE_FLAG_COST.equals(aiSaveFlag)){
                    totalList = numberEmployeeSplitMapper.aiSaveCost(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes, aiSaveCostConfig);
                }
                totalList = dealMissDateAsEmptyData(totalList, rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd());
            }
        }

        return totalList;
    }

    /**
     * 得到结果后，检查开始和结束时间内，是否有丢失日期的情况，如果丢失则补上这天数据为0
     * @return
     */
    private List<RateAndDateVO> dealMissDateAsEmptyData(List<RateAndDateVO> originalDatas, String startDate, String endDate){
        List<RateAndDateVO> newDatas = new ArrayList<>();

        // 解析开始和结束日期
        Map<String, RateAndDateVO> dateMap = new HashMap<>();
        // 将originalDatas中的日期和对象映射起来，便于快速查找
        if(CollectionUtils.isNotEmpty(originalDatas)){
            for (RateAndDateVO vo : originalDatas) {
                dateMap.put(vo.getBillDateBegin(), vo);
            }
        }

        // 解析开始和结束日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDate start = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);
        // 遍历每一天
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            if (!dateMap.containsKey(date.toString().concat(" 00:00:00"))) {
                // 如果不存在，则创建一个新的RateAndDateVO对象，billRate设置为0
                RateAndDateVO newVo = new RateAndDateVO();
                newVo.setBillDateBegin(date.toString().concat(" 00:00:00"));
                newVo.setBillDateEnd(date.toString().concat(" 23:59:59"));
                newVo.setBillRate("0");
                newDatas.add(newVo);
            } else {
                // 如果存在，则直接添加到结果列表中（如果需要保留原始数据）
                newDatas.add(dateMap.get(date.toString().concat(" 00:00:00")));
            }
        }
        return newDatas;
    }

    /**
     * 根据维度结算统计日期
     * @param aiKanBanDTO
     * @return
     */
    private List<RateAndDateVO> getRateAndDate(NumberEmployeeDTO aiKanBanDTO, List<String> bizTypes){
        List<RateAndDateVO> totalList = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 天维度计算准确率
        if("1".equals(aiKanBanDTO.getTotalType())){
            // 使用DateTimeFormatter来解析和格式化日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            DateTimeFormatter formatterDay = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startDate = null;
            LocalDate endDate = null;
            if (StringUtils.isNotEmpty(aiKanBanDTO.getAiCheckDateStart()) && StringUtils.isNotEmpty(aiKanBanDTO.getAiCheckDateEnd())) {
                startDate = LocalDateTime.parse(aiKanBanDTO.getAiCheckDateStart(), formatter).toLocalDate();
                endDate = LocalDateTime.parse(aiKanBanDTO.getAiCheckDateEnd(), formatter).toLocalDate();
                // 计算并打印两个日期之间相差的天数
                long daysBetween = ChronoUnit.DAYS.between(endDate, startDate);
                long absoluteDaysBetween = Math.abs(daysBetween);
                if(absoluteDaysBetween > 30){
                    throw new BizException("日期间隔不能大于30天，请重新选择！");
                }
            }
            if (startDate != null && endDate != null) {
                // 两个日期都不为空，输出两个日期之间的所有日期（包括起始和结束日期）
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    RateAndDateVO rateAndDateVO = new RateAndDateVO();
                    // 将LocalDate对象格式化为字符串
                    String formattedDate = date.format(formatterDay);
                    rateAndDateVO.setBillDateBegin(formattedDate.concat(" 00:00:00"));
                    rateAndDateVO.setBillDateEnd(formattedDate.concat(" 23:59:59"));
                    // 计算每天的准确率
                    String rightRate = null;
                    if("1".equals(aiKanBanDTO.getAiTriggerTaskFlag())){
                        rightRate = numberEmployeeMapper.getAiPassRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                    }
                    if("1".equals(aiKanBanDTO.getSampleTaskFlag())){
                        rightRate = numberEmployeeMapper.getSampleRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                    }
                    if("1".equals(aiKanBanDTO.getSampleTaskRuleFlag())){
                        rightRate = numberEmployeeMapper.getSampleRuleRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                    }
                    rateAndDateVO.setBillRate(rightRate);
                    totalList.add(rateAndDateVO);
                }
            } else {
                // 任一日期为空，输出从当前日期开始到前30天的所有日期
                LocalDate currentDate = LocalDate.now();
                for (int i = 30; i >= 0; i--) {
                    LocalDate pastDate = currentDate.minusDays(i + 1); // 加1是因为我们需要从当前日期开始的前30天
                    RateAndDateVO rateAndDateVO = new RateAndDateVO();
                    // 将LocalDate对象格式化为字符串
                    String formattedDate = pastDate.format(formatterDay);
                    rateAndDateVO.setBillDateBegin(formattedDate.concat(" 00:00:00"));
                    rateAndDateVO.setBillDateEnd(formattedDate.concat(" 23:59:59"));
                    // 计算准确率
                    String rightRate = null;
                    if("1".equals(aiKanBanDTO.getAiTriggerTaskFlag())){
                        rightRate = numberEmployeeMapper.getAiPassRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                    }
                    if("1".equals(aiKanBanDTO.getSampleTaskFlag())){
                        rightRate = numberEmployeeMapper.getSampleRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                    }
                    if("1".equals(aiKanBanDTO.getSampleTaskRuleFlag())){
                        rightRate = numberEmployeeMapper.getSampleRuleRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId(), bizTypes);
                    }
                    rateAndDateVO.setBillRate(rightRate);
                    totalList.add(rateAndDateVO);
                }
            }
        }
        return totalList;
    }

    /**
     * 根据l3流程编码或者业务类型获取业务单元编码
     * @param numberEmployeeDTO
     * @return
     */
    private List<String> getBizCodeByL3FlowCodeOrBizType(NumberEmployeeDTO numberEmployeeDTO) {
        List<BizUnitInfoVO> bizUnitInfoVOS = new ArrayList<>();
        // 通过l3流程取业务单元代码集合
        if(StringUtils.isNotEmpty(numberEmployeeDTO.getL3Flow())){
            bizUnitInfoVOS = taskInfoMapper.getL3FlowBizUnitInfos(dealName, numberEmployeeDTO.getL3Flow());
        }else {
            bizUnitInfoVOS = taskInfoMapper.getBizUnitInfos(dealName);
        }
        if(StringUtils.isNotEmpty(numberEmployeeDTO.getBizType())){
            BizUnitInfoVO bizUnitInfoVO = new BizUnitInfoVO();
            bizUnitInfoVO.setBizUnitCode(numberEmployeeDTO.getBizType());
            List<BizUnitInfoVO> finalBizUnitInfoVOS = new ArrayList<>();
            finalBizUnitInfoVOS.add(bizUnitInfoVO);
            bizUnitInfoVOS = finalBizUnitInfoVOS;
        }
        if(CollectionUtils.isNotEmpty(bizUnitInfoVOS)){
            return bizUnitInfoVOS.stream().map(BizUnitInfoVO::getBizUnitCode).collect(Collectors.toList());
        }else {
            return null;
        }
    }

    @Override
    public List<FlowInfoVO> getL3FlowBusiness() {
        return numberEmployeeMapper.getFlowInfos();
    }

    @Override
    public List<BizUnitInfoVO> getBizUnitInfos() {
        return taskInfoMapper.getBizUnitInfos(dealName);
    }

    /**
     * l3对应的业务单元代码列表
     * @param processInfoDTO
     * @return
     */
    @Override
    public List<BizUnitInfoVO> getl3BizUnitInfos(ProcessInfoDTO processInfoDTO) {
        return numberEmployeeMapper.getl3BizUnitInfos(processInfoDTO.getL3FlowBusinessCode());
    }

    @Override
    public List<SystemVO> getSystemInfoByUnitCode(@RequestBody ProcessInfoDTO processInfoDTO) {
        return systemCallBackUrlMapper.getSystemListByUnitCode(processInfoDTO.getUnitCode());
    }

    @Override
    public String getAiCoverRoleNum(){
        // AI覆盖角色数
        Integer aiCoverRoleNum = numberEmployeeMapper.getAiCoverRoleNum(dealName);
        return String.valueOf(aiCoverRoleNum);
    }

    @Override
    public String getAiCoverBizNum(){
        // AI覆业务单元则数
        Integer aiCoverBizNum = numberEmployeeMapper.getAiCoverBizNum(dealName);
        return String.valueOf(aiCoverBizNum);
    }

    @Override
    public String getAiCoverRuleNum(){
        // AI覆盖规则数
        Integer aiCoverRuleNum = numberEmployeeMapper.getAiCoverRuleNum(dealName);
        return String.valueOf(aiCoverRuleNum);
    }

    @Override
    public AiCoverDicVO getAiCoverRuleRate() {
        AiCoverDicVO aiCoverDicVO = numberEmployeeMapper.getAiCoverRuleRate(dealName);

        return aiCoverDicVO;
    }

    @Override
    public NumberEmployeeVO getInfoNew(NumberEmployeeDTO numberEmployeeDTO) {
        if(CollectionUtils.isEmpty(numberEmployeeDTO.getBizTypes())){
            throw new BizException("业务单元代码不能为空！");
        }
        log.info("-------数字员工看板----获取业务单元代码开始------");
        List<String> bizTypes = numberEmployeeSplitMapper.getBizTypesByUnitCode(numberEmployeeDTO.getBizTypes());
        log.info("-------数字员工看板----获取业务单元代码结束------");
        if(CollectionUtils.isEmpty(bizTypes)){
            throw new BizException("该业务单元代码不到对应的业务类型！");
        }
        // AI触发任务数信息
        log.info("-------数字员工看板----AI触发任务数信息开始------");
        NumberEmployeeVO aiTriggerTaskInfo = numberEmployeeSplitMapper.getAiTouchTaskInfo(numberEmployeeDTO, bizTypes);
        log.info("-------数字员工看板----AI触发任务数信结束------");
        // AI触发规则数信息
        log.info("-------数字员工看板----AI触发规则数信息开始------");
        NumberEmployeeVO aiTriggerRuleInfo = numberEmployeeSplitMapper.getAiTouchRuleInfo(numberEmployeeDTO, bizTypes);
        log.info("-------数字员工看板----AI触发规则数信息结束------");
        // 抽检任务数信息 + 人工抽检准确率_圆饼图(单据维度)
        log.info("-------数字员工看板----抽检任务数信息 + 人工抽检准确率_圆饼图(单据维度)开始------");
        HumanSampleRightRateVO aiSampleInfo = numberEmployeeSplitMapper.getSampleTaskInfo(numberEmployeeDTO, bizTypes);
        log.info("-------数字员工看板----抽检任务数信息 + 人工抽检准确率_圆饼图(单据维度)结束------");
        // 抽检任务数信息 + 人工抽检准确率_圆饼图(规则维度)
        log.info("-------数字员工看板----抽检任务数信息 + 人工抽检准确率_圆饼图(规则维度)开始------");
        HumanSampleRightRateRuleVO aiSampleRuleInfo = numberEmployeeSplitMapper.getSampleTaskRuleInfo(numberEmployeeDTO, bizTypes);
        log.info("-------数字员工看板----抽检任务数信息 + 人工抽检准确率_圆饼图(规则维度)结束------");
        List<RateTrendChartVO> aiPassRates = null;
        List<RateTrendChartVO> aiSaveTime = null;
        List<RateTrendChartVO> aiSaveCost = null;
        // AI节省工时
        log.info("-------数字员工看板----AI节省工时开始------");
        Double aiSaveTimes = numberEmployeeMapper.saveTimes(numberEmployeeDTO,bizTypes, aiSaveTimeConfig);
        log.info("-------数字员工看板----AI节省工时结束------");
        // AI节省费用
        log.info("-------数字员工看板----AI节省费用开始------");
        Double aiSaveCosts = numberEmployeeMapper.saveMoney(numberEmployeeDTO,bizTypes, aiSaveCostConfig);
        log.info("-------数字员工看板----AI节省费用结束------");
        // 统计折线图部分数据
        if(StringUtils.isNotEmpty(numberEmployeeDTO.getAiSaveFlag()) && "1".equals(numberEmployeeDTO.getAiSaveFlag())){
            log.info("-------数字员工看板----AI节省工时折线图开始------");
            aiSaveTime = getTableLineInfo(numberEmployeeDTO, bizTypes,"1");
            log.info("-------数字员工看板----AI节省工时折线图结束------");
            log.info("-------数字员工看板----AI节省费用折线图开始------");
            aiSaveCost = getTableLineInfo(numberEmployeeDTO, bizTypes,"2");
            log.info("-------数字员工看板----AI节省费用折线图结束------");
        }else{
            // AI通过率折线图 或者 人工抽检准确率_折线（单据维度） 或者 人工抽检准确率_折线（规则维度）
            log.info("-------数字员工看板----AI通过率折线图 或者 人工抽检准确率_折线（单据维度） 或者 人工抽检准确率_折线（规则维度）开始------");
            aiPassRates = getTableLineInfo(numberEmployeeDTO, bizTypes,"");
            log.info("-------数字员工看板----AI通过率折线图 或者 人工抽检准确率_折线（单据维度） 或者 人工抽检准确率_折线（规则维度）结束------");
        }

        // 组装数字员工看板结果
        NumberEmployeeVO numberEmployeeVO = new NumberEmployeeVO();
        numberEmployeeVO.setAiCoverSceneNum("0");
        // AI节省工时
        if(aiSaveTimes != null){
            numberEmployeeVO.setAiSaveTime(aiSaveTimes.toString());
        }
        // AI节省费用
        if(aiSaveCosts != null){
            numberEmployeeVO.setAiSaveCost(aiSaveCosts.toString());
        }
        // 折线图
        if(CollectionUtils.isNotEmpty(aiPassRates)){
            numberEmployeeVO.setTrendLine(aiPassRates);
        }
        // ai节约时间折线图
        if(CollectionUtils.isNotEmpty(aiSaveTime)){
            numberEmployeeVO.setTrendLineSaveTime(aiSaveTime);
        }
        // ai节约费用折线图
        if(CollectionUtils.isNotEmpty(aiSaveCost)){
            numberEmployeeVO.setTrendLineSaveCost(aiSaveCost);
        }
        if(aiTriggerTaskInfo != null){
            numberEmployeeVO.setAiTriggerTaskNum(aiTriggerTaskInfo.getAiTriggerTaskNum());
            numberEmployeeVO.setAiTriggerTaskPassNum(aiTriggerTaskInfo.getAiTriggerTaskPassNum());
            numberEmployeeVO.setAiTriggerTaskRate(aiTriggerTaskInfo.getAiTriggerTaskRate());
        }
        if(aiTriggerRuleInfo != null){
            numberEmployeeVO.setAiTriggerRuleNum(aiTriggerRuleInfo.getAiTriggerRuleNum());
            numberEmployeeVO.setAiTriggerRulePassNum(aiTriggerRuleInfo.getAiTriggerRulePassNum());
            numberEmployeeVO.setAiTriggerRulePassRate(aiTriggerRuleInfo.getAiTriggerRulePassRate());
        }
        // 抽检任务数（单据）
        if (aiSampleInfo != null){
            numberEmployeeVO.setHumanSampleRightRate(aiSampleInfo);
            numberEmployeeVO.setSampleTaskRate(aiSampleInfo.getSampleRightRate());
            numberEmployeeVO.setSampleTaskNum(aiSampleInfo.getSampleTotalNum());

            // 使用 DecimalFormat 格式化结果为小数点后两位
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            Double aiUnpassHumanUnpassRate = Double.valueOf("100")
                    - Double.valueOf(aiSampleInfo.getAiPassHumanPassRate() == null ? "0" : aiSampleInfo.getAiPassHumanPassRate())
                    - Double.valueOf(aiSampleInfo.getAiUnpassHumanPassRate() == null ? "0" : aiSampleInfo.getAiUnpassHumanPassRate())
                    - Double.valueOf(aiSampleInfo.getAiPassHumanUnpassRate() == null ? "0" : aiSampleInfo.getAiPassHumanUnpassRate());
            // 格式化结果
            String formattedRate = decimalFormat.format(aiUnpassHumanUnpassRate);
            aiSampleInfo.setAiUnpassHumanUnpassRate(aiSampleInfo.getAiUnpassHumanUnpassRate() == null ? null : String.valueOf(formattedRate));
        }
        // 抽检任务数（规则）
        if(aiSampleRuleInfo != null){
            numberEmployeeVO.setHumanSampleRightRateRule(aiSampleRuleInfo);
            numberEmployeeVO.setSampleTaskRuleNum(aiSampleRuleInfo.getSampleTotalNum());
            numberEmployeeVO.setSampleTaskRuleRate(aiSampleRuleInfo.getSampleRightRate());

            // 使用 DecimalFormat 格式化结果为小数点后两位
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            Double aiUnpassHumanUnpassRate = Double.valueOf("100")
                    - Double.valueOf(aiSampleRuleInfo.getAiPassHumanPassRate() == null ? "0" : aiSampleRuleInfo.getAiPassHumanPassRate())
                    - Double.valueOf(aiSampleRuleInfo.getAiUnpassHumanPassRate() == null ? "0" : aiSampleRuleInfo.getAiUnpassHumanPassRate())
                    - Double.valueOf(aiSampleRuleInfo.getAiPassHumanUnpassRate() == null ? "0" : aiSampleRuleInfo.getAiPassHumanUnpassRate());
            // 格式化结果
            String formattedRate = decimalFormat.format(aiUnpassHumanUnpassRate);
            aiSampleRuleInfo.setAiUnpassHumanUnpassRate(aiSampleRuleInfo.getAiUnpassHumanUnpassRate() == null ? null : String.valueOf(formattedRate));
        }

        return numberEmployeeVO;
    }

    @Override
    public void updateAiCoverInfo() {
        // 获取处理人对应的业务单元信息 (即ai覆盖业务单元数)
        List<BizUnitInfoVO> bizUnitInfoVOS = taskInfoMapper.getBizUnitInfos(dealerCode);
        if(CollectionUtils.isEmpty(bizUnitInfoVOS)){
            throw new BizException("找不到处理人对应的业务单元！");
        }
        // AI覆盖规则数
        Integer aiCoverRuleNum = numberEmployeeMapper.getAiCoverRuleNumBybizUnitInfos(bizUnitInfoVOS);
        // AI覆盖规则数
        Integer aiCoverRoleNum = taskInfoMapper.getAiCoverRoleNum(bizUnitInfoVOS);

        NumberEmployeeAiCoverInfoDTO numberEmployeeAiCoverInfoDTO = new NumberEmployeeAiCoverInfoDTO();
        numberEmployeeAiCoverInfoDTO.setAiCoverRoleNum(aiCoverRoleNum);
        numberEmployeeAiCoverInfoDTO.setAiCoverBizNum(bizUnitInfoVOS.size());
        numberEmployeeAiCoverInfoDTO.setAiCoverRuleNum(aiCoverRuleNum);
        numberEmployeeAiCoverInfoDTO.setId(1);
        numberEmployeeSplitMapper.updateAiCoverInfo(numberEmployeeAiCoverInfoDTO);

    }
}
