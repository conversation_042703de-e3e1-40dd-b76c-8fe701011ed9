package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.entity.vo.ai.SystemVO;
import com.faw.work.ais.entity.vo.ai.TaskRuleVO;
import com.faw.work.ais.model.SystemCallBackUrl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SystemCallBackUrlMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SystemCallBackUrl record);

    int insertSelective(SystemCallBackUrl record);

    SystemCallBackUrl selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SystemCallBackUrl record);

    int updateByPrimaryKey(SystemCallBackUrl record);

    /**
     * 获取系统信息
     * @return
     */
    List<SystemVO> getKanBanSystemList();

    /**
     * 根据业务单元获取系统信息
     * @param unitCode 业务单元代码
     * @return
     */
    List<SystemVO> getSystemListByUnitCode(String unitCode);
}