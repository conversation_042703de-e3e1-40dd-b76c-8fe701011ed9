package com.faw.work.ais.common.dto.roleworkbench;

import com.faw.work.ais.common.enums.TaskBizEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TaskBizDTO {
    @Schema(description = "任务类型枚举值")
    private TaskBizEnum taskBizEnum;
    @Schema(description = "角色工作台对应的业务单元code")
    private String bizUnitCode;
    @Schema(description = "用户编码")
    private List<String> userCodeList;
    @Schema(description = "角色工作台触发任务后返回的当前任务流程code")
    private String taskInstanceCode;
    @Schema(description = "上一任务业务id,比如合辑Id")
    private String bizId;

    @Schema(description = "活动Id,活动设计任务时需要传")
    private String activityId;

    @Schema(description = "活动名称,活动设计任务时需要传")
    private String activityName;

}
