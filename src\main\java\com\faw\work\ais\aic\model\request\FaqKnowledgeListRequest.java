package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * FAQ知识列表查询请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ知识列表查询请求")
public class FaqKnowledgeListRequest {

    @Schema(description = "知识名称")
    private String knowledgeName;

    @Schema(description = "环境：prod-正式环境, test-测试环境", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "环境不能为空")
    private String env;

    @Schema(description = "查询开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
    private String startTime;

    @Schema(description = "查询结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
    private String endTime;

    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageNum ;

    @Schema(description = "每页大小", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageSize ;

    @Schema(description = "机器人id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String robotId ;
} 