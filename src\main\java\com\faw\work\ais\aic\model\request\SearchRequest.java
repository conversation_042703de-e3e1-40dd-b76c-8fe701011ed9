package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 搜索请求实体
 * <AUTHOR>
 */
@Schema(description = "搜索请求实体")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchRequest {
    /**
     * 问题内容
     */
    @Schema(description = "问题内容")
    private String question;
    
    /**
     * 返回的最大结果数量
     */
    @Schema(description = "返回的最大结果数量")
    private Integer topK = 1;
} 