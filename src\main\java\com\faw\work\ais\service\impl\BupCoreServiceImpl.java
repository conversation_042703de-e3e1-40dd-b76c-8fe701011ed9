package com.faw.work.ais.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.faw.work.ais.entity.vo.ai.BizUnitInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowVO;
import com.faw.work.ais.mapper.bupcore.TaskInfoMapper;
import com.faw.work.ais.model.AiScene;
import com.faw.work.ais.service.BupCoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数字员工看板service
 */
@Service
@Slf4j
public class BupCoreServiceImpl implements BupCoreService {

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Override
    public FlowVO getFlowList() {

        FlowVO flowVO = new FlowVO();
        List<FlowInfoVO> l3flowInfoList = new ArrayList<>();
        List<FlowInfoVO> l4flowInfoList = new ArrayList<>();
        List<FlowInfoVO> l5flowInfoList = new ArrayList<>();

        List<AiScene> aiSceneList = taskInfoMapper.getFlowInfoList();

        Map<String, List<AiScene>> l3flowMap = aiSceneList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getL3ProcessCode())).collect(Collectors.groupingBy(AiScene::getL3ProcessCode));
        Map<String, List<AiScene>> l4flowMap = aiSceneList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getL4ProcessCode())).collect(Collectors.groupingBy(AiScene::getL4ProcessCode));
        Map<String, List<AiScene>> l5flowMap = aiSceneList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getL5ProcessCode())).collect(Collectors.groupingBy(AiScene::getL5ProcessCode));

        for (String l3ProcessCode : l3flowMap.keySet()) {
            FlowInfoVO flowInfoVO = new FlowInfoVO();
            flowInfoVO.setLevel("3");
            flowInfoVO.setL3flowCode(l3ProcessCode);
            flowInfoVO.setL3flowName(l3flowMap.get(l3ProcessCode).get(0).getL3ProcessName());
            l3flowInfoList.add(flowInfoVO);
        }

        for (String l4ProcessCode : l4flowMap.keySet()) {
            FlowInfoVO flowInfoVO = new FlowInfoVO();
            flowInfoVO.setLevel("4");
            flowInfoVO.setL3flowCode(l4ProcessCode);
            flowInfoVO.setL3flowName(l4flowMap.get(l4ProcessCode).get(0).getL3ProcessName());
            l4flowInfoList.add(flowInfoVO);
        }

        for (String l5ProcessCode : l5flowMap.keySet()) {
            FlowInfoVO flowInfoVO = new FlowInfoVO();
            flowInfoVO.setLevel("5");
            flowInfoVO.setL3flowCode(l5ProcessCode);
            flowInfoVO.setL3flowName(l5flowMap.get(l5ProcessCode).get(0).getL3ProcessName());
            l5flowInfoList.add(flowInfoVO);
        }

        flowVO.setL3flowInfoList(l3flowInfoList);
        flowVO.setL4flowInfoList(l4flowInfoList);
        flowVO.setL5flowInfoList(l5flowInfoList);
        return flowVO;
    }

    @Override
    public List<BizUnitInfoVO> getUnitList(FlowInfoVO req) {
        return taskInfoMapper.getUnitList(req);
    }

}
