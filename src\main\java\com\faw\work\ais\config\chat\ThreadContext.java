package com.faw.work.ais.config.chat;

/**
 * 线程上下文
 *
 * <AUTHOR>
 * @since 2025-07-04 8:37
 */
public class ThreadContext {

    private ThreadContext() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * 线程上下文
     */
    private static final ThreadLocal<String> CONTEXT = new ThreadLocal<>();


    /**
     * 设置线程上下文
     *
     * @param value 线程上下文
     */
    public static void set(String value) {
        CONTEXT.set(value);
    }

    /**
     * 获取线程上下文
     *
     * @return 线程上下文
     */
    public static String get() {
        return CONTEXT.get();
    }

    /**
     * 清除线程上下文
     */
    public static void clear() {
        CONTEXT.remove();
    }

}
