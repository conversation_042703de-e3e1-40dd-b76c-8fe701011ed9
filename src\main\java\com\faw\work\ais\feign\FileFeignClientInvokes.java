package com.faw.work.ais.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.File;
import java.net.HttpURLConnection;
import java.net.URI;

@FeignClient(value = "file", url = "https://hqtds.faw.cn/api/sys-res/attachFile/download?fileId=15219c5a28d6958e07e2b9455267c933&thumbnailFlag=normal&tenantId=15&downloadFlag=0")
public interface FileFeignClientInvokes {

    /**
     * 获取图片
     * @param uri
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    File getFileByUrl(URI uri);

}
