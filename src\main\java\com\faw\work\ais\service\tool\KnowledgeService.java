package com.faw.work.ais.service.tool;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.request.SearchContentRequest;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.ToolCacheEntity;
import com.faw.work.ais.common.enums.chat.ChatToolEnum;
import com.faw.work.ais.entity.domain.SearchResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用车手册服务类，用于获取车辆手册信息
 *
 * <AUTHOR>
 * @since 2025-04-07 15:33
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class KnowledgeService {

    private final RagDocumentService ragDocumentService;

    private final FaqKnowledgeService faqKnowledgeService;

    private final RedisService redisService;

    private final FunctionService functionService;


    private static final String CAR_SERIES_EMPTY_MESSAGE = "请输入正确的车系编码";

    private static final String CHAT_HISTORY_EMPTY_MESSAGE = "请输入正确的谈话内容";

    private static final String KNOWLEDGE_EMPTY_MESSAGE = """
            未查询到相关知识，如果需要人工协助，请点击下方入口，直达在线客服；
            或者拨打人工坐席电话：<el-tel tel='************'></el-tel>
            """;


    @Tool(name = "getVehicleManual", description = "车辆手册信息获取：根据用户提供的车系查询车辆手册信息")
    public String getVehicleManual(@ToolParam(description = "车系编码") String carSeries,
                                   @ToolParam(description = "用户和AI最近两轮谈话内容") String chatHistory, ToolContext toolContext) {
        Instant start = Instant.now();
        log.info("[VehicleService][getVehicleManual][entrance] carSeries: {}, chatHistory: {}, toolContext: {}", carSeries, chatHistory, JSON.toJSONString(toolContext));
        // 1 参数校验
        if (StringUtils.isEmpty(carSeries)) {
            return CAR_SERIES_EMPTY_MESSAGE;
        }
        if (StringUtils.isEmpty(chatHistory)) {
            return CHAT_HISTORY_EMPTY_MESSAGE;
        }

        // 2 召回知识库信息
        SearchContentRequest request = new SearchContentRequest();
        request.setQuery(chatHistory);
        request.setLabel(carSeries);
        request.setRagKnowledgeId(1L);

        List<SimilarContentSearchResponse> documentInfo = ragDocumentService.searchSimilarContentNew(request);
        List<FaqKnowledgePO> faqKnowledgeInfo = faqKnowledgeService.searchByQuestion(chatHistory, 3);

        // 3 合并两个集合
        List<SearchResultVO> combinedResults = new ArrayList<>();
        // 3.1 提取documentInfo的content字段
        if (!CollectionUtils.isEmpty(documentInfo)) {
            combinedResults.addAll(
                    documentInfo.stream().filter(Objects::nonNull).map(
                            doc -> {
                                SearchResultVO result = new SearchResultVO();
                                result.setText(doc.getDocumentContent());
                                result.setScore(doc.getScore());
                                result.setType("documents");
                                return result;
                            }).toList());
        }
        // 3.2 提取faqKnowledgeInfo的answer和score字段
        if (!CollectionUtils.isEmpty(faqKnowledgeInfo)) {
            combinedResults.addAll(
                    faqKnowledgeInfo.stream().filter(Objects::nonNull).map(
                            faq -> {
                                SearchResultVO result = new SearchResultVO();
                                result.setText(faq.getAnswer());
                                result.setScore(faq.getScore());
                                result.setType("faqKnowledge");
                                return result;
                            }).toList());
        }
        log.info("[VehicleService][getVehicleManual] combinedResults.size: {}", combinedResults.size());

        // 4 判断知识召回是否成功
        String result;
        ToolCacheEntity toolCache = ToolCacheEntity.builder().toolName(ChatToolEnum.GET_VEHICLE_MANUAL.getName()).toolStatus(true).build();
        if (combinedResults.isEmpty()) {
            toolCache.setToolStatus(false);
            toolCache.setToolValue(functionService.buildStaffServiceEntity(chatHistory));
            result = KNOWLEDGE_EMPTY_MESSAGE;
        } else {
            result = JSON.toJSONString(combinedResults);
        }
        log.info("[VehicleService][getVehicleManual] toolCache: {}", JSON.toJSONString(toolCache));

        // 5 将toolCache信息存入redis中
        redisService.set((String) toolContext.getContext().get(CommonConstants.KEY_CACHE_ID), JSON.toJSONString(toolCache), CommonConstants.SECONDS_ONE_HOUR);

        log.info("[VehicleService][getVehicleManual][exit] cost: {} ms, combinedResults: {}", Duration.between(start, Instant.now()).toMillis(), result);
        return result;
    }

}
