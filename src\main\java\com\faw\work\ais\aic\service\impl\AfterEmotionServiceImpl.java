package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.common.enums.LLMBizTypeEnum;
import com.faw.work.ais.aic.common.enums.MessageTypeEnum;
import com.faw.work.ais.aic.common.mq.AfterEmotionMessageProducer;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.common.util.StrUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.feign.AfterEmotionCallbackFeignClient;
import com.faw.work.ais.aic.mapper.llm.LlmRecordMapper;
import com.faw.work.ais.aic.model.domain.LlmRecord;
import com.faw.work.ais.aic.model.request.AfterEmotionCallbackRequest;
import com.faw.work.ais.aic.model.request.AfterEmotionRequest;
import com.faw.work.ais.aic.model.response.ConversationSummaryResult;
import com.faw.work.ais.aic.model.response.EmotionAnalysisResult;
import com.faw.work.ais.aic.service.AfterEmotionService;
import com.faw.work.ais.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 售后接待情绪价值模型服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AfterEmotionServiceImpl extends ServiceImpl<LlmRecordMapper, LlmRecord> implements AfterEmotionService {

    private static final String STATUS_UNPROCESSED = "00";
    private static final String STATUS_PROCESSING = "01";
    private static final String STATUS_COMPLETED = "02";
    private static final String STATUS_FAILED = "10";

    @Autowired
    private LlmRecordMapper llmRecordMapper;

    @Autowired
    private AfterEmotionMessageProducer afterEmotionMessageProducer;

    @Autowired
    private BaiLianAppConfig baiLianAppConfig;

    @Autowired
    private AfterEmotionService self;

    @Autowired
    private Executor dmsAfterEmotionExecutor;

    @Autowired
    private AfterEmotionCallbackFeignClient afterEmotionCallbackFeignClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiResult<String> processAfterEmotion(AfterEmotionRequest request) {
        try {
            String requestId = request.getRequestId();
            List<LlmRecord> llmRecords = llmRecordMapper.selectByRequestId(requestId, null, null);
            if (CollUtil.isNotEmpty(llmRecords)) {
                return AiResult.fail("请求ID: " + requestId + " 已存在，请勿重复提交");
            }

            String conversationContent = request.getUserInput();
            if (StrUtil.isBlank(conversationContent)) {
                return AiResult.fail("用户输入内容不能为空");
            }

            String customerSpeaker = "客户";

            // 分片客户对话内容
            List<String> slices = StrUtils.sliceConversation(conversationContent, customerSpeaker, 3, 1);
            if (CollUtil.isEmpty(slices)) {
                return AiResult.fail("用户输入内容格式不正确，请重新输入");
            }

            String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 创建售后接待情绪分析分片记录
            List<LlmRecord> emotionList = new ArrayList<>();
            for (String slice : slices) {
                LlmRecord emotion = new LlmRecord();
                emotion.setRequestId(requestId);
                emotion.setUserInput(slice);
                emotion.setStatus(STATUS_UNPROCESSED);
                emotion.setBizType(LLMBizTypeEnum.DMS_AFTER_EMOTION.getCode());
                emotion.setCallbackUrl("http://localhost:8080/api/v1/llm/after-emotion/callback");
                emotion.setField2(request.getAudioUrl());
                emotion.setCreateAt(now);
                emotionList.add(emotion);
            }

            this.saveBatch(emotionList);
            log.info("成功将 {} 个售后接待情绪分析对话分片存入数据库", emotionList.size());

            // 发送MQ消息
            afterEmotionMessageProducer.sendAfterEmotionMessage(requestId, MessageTypeEnum.DMS_AFTER_EMOTION.getCode());

        } catch (Exception e) {
            log.error("处理售后接待情绪分析请求失败", e);
            throw new BizException(e.getMessage());
        }

        return AiResult.success("成功接收消息，请关注回调");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processAfterEmotionSlice(String requestId) {
        List<LlmRecord> list = llmRecordMapper.selectByRequestId(requestId, STATUS_UNPROCESSED, LLMBizTypeEnum.DMS_AFTER_EMOTION.getCode());

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (LlmRecord slice : list) {
            // 更新状态为处理中
            log.info("开始处理售后接待情绪分析分片ID: {}", slice.getId());
            self.updateStatusTran(slice.getId(), STATUS_PROCESSING, null);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 执行情绪分析
                    performAfterEmotionAnalysis(slice);
                    self.updateStatusTran(slice.getId(), STATUS_COMPLETED, null);
                    log.info("售后接待情绪分析分片ID: {} 处理成功", slice.getId());
                } catch (Exception e) {
                    log.error("售后接待情绪分析分片ID: {} 处理失败，可能是大模型限流了", slice.getId(), e);
                    self.updateStatusTran(slice.getId(), STATUS_FAILED, null);
                }
            }, dmsAfterEmotionExecutor);

            futures.add(future);
        }

        // 等待所有分片处理完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("所有售后接待情绪分析分片处理完成，开始进行对话总结，请求ID: {}", requestId);

        // 进行对话总结
        ConversationSummaryResult summaryResult = this.performConversationSummary(requestId);

        this.sendCallbackToUpstream(requestId, summaryResult);

        return true;
    }

    /**
     * 执行售后接待情绪分析
     *
     * @param emotion 分片记录
     */
    private void performAfterEmotionAnalysis(LlmRecord emotion) {
        log.info("开始对记录ID: {} 进行售后接待情绪分析", emotion.getId());

        // 调用应用id为b793a2eae5064584998e6abd3706e399的模型进行情绪分析
        EmotionAnalysisResult result = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getAfterEmotionAppId(),
                emotion.getUserInput(),
                EmotionAnalysisResult.class);

        if (result != null) {
            LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LlmRecord::getId, emotion.getId())
                    .set(LlmRecord::getLlmOutput, JSONUtil.toJsonStr(result))
                    .set(LlmRecord::getField3, result.getStart())
                    .set(LlmRecord::getField4, result.getEnd());
            this.update(updateWrapper);
            log.info("记录ID: {} 售后接待情绪分析成功", emotion.getId());
        }
    }

    /**
     * 执行对话总结
     */
    private ConversationSummaryResult performConversationSummary(String requestId) {
        List<LlmRecord> completedSlices = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_AFTER_EMOTION.getCode());

        // 组成完整对话
        StringBuilder fullConversation = new StringBuilder();
        for (LlmRecord slice : completedSlices) {
            fullConversation.append(slice.getUserInput()).append("\n");
        }

        return BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getAfterEmotionSummaryAppId(),
                fullConversation.toString(),
                ConversationSummaryResult.class);

    }

    /**
     * 发送回调给上游
     *
     * @param requestId     请求ID
     * @param summaryResult 对话总结结果
     */
    private void sendCallbackToUpstream(String requestId, ConversationSummaryResult summaryResult) {

        List<LlmRecord> emotionSlices = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_AFTER_EMOTION.getCode());

        log.info("开始发送回调给上游，请求ID: {}", requestId);

        try {
            // 构建回调请求
            AfterEmotionCallbackRequest callbackRequest = new AfterEmotionCallbackRequest();
            callbackRequest.setRequestId(requestId);
            callbackRequest.setProcessTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 封装情绪分析结果
            List<EmotionAnalysisResult> emotionAnalysisList = new ArrayList<>();
            for (LlmRecord slice : emotionSlices) {
                if (StrUtil.isNotBlank(slice.getLlmOutput())) {
                    try {
                        EmotionAnalysisResult emotionResult = JSONUtil.toBean(slice.getLlmOutput(), EmotionAnalysisResult.class);
                        emotionAnalysisList.add(emotionResult);
                    } catch (Exception e) {
                        log.warn("解析情绪分析结果失败，分片ID: {}, 错误: {}", slice.getId(), e.getMessage());
                    }
                }
            }
            callbackRequest.setEmotionAnalysisList(emotionAnalysisList);

            // 设置对话总结结果
            callbackRequest.setConversationSummary(summaryResult);
            log.info("售后接待情绪分析回调请求结果: {}", JSONUtil.toJsonStr(callbackRequest));
            // TODO: 回调上游接口
            // DgwResult dgwResult = afterEmotionCallbackFeignClient.callBackAfterEmotion(callbackRequest);

        } catch (Exception e) {
            log.error("发送回调失败，请求ID: {}", requestId, e);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateStatusTran(Long id, String status, String remark) {
        LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LlmRecord::getId, id)
                .set(LlmRecord::getStatus, status)
                .set(LlmRecord::getRemark, remark);
        this.update(updateWrapper);
    }
}
