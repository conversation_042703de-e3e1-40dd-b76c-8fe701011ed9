package com.faw.work.ais.message;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.entity.dto.MessageDTO;
import com.faw.work.ais.entity.dto.ai.CosQueueDTO;
import com.faw.work.ais.service.AiTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@ConditionalOnProperty(
        name = "online.run",
        havingValue = "true",
        matchIfMissing = true
)
public class RabbitMQConsumer {

    @Autowired
    AiTaskService aiTaskService;
    @RabbitListener(queues = "${mq.queue-name}")
    public void receiveMessage(String message) {
        try {
            log.info("------------监听消息队列开始-----------------",message);
            MessageDTO messageDTO = JSON.parseObject(message, MessageDTO.class);
            if("1".equals(messageDTO.getBusinessType())){
                CosQueueDTO cosQueueDTO = JSON.parseObject(messageDTO.getJsonMapString(), CosQueueDTO.class);
                log.info("-------------------该条消息的tranceId-----------------------" + cosQueueDTO.getTraceId());
                aiTaskService.getAiResult(cosQueueDTO);
            }
            log.info("------------监听消息队列结束-----------------",message);
        } catch (Exception e) {
            log.error("-----------------队列监听消息异常【{}】" + e.getMessage(), e);
        }
    }

    /**
     * 监听json格式错误的消息
     * @param message
     */
    @RabbitListener(queues = "${mq.aisErrorQueue.queue-name}")
    public void receiveErrorMessage(String message) {
        try {
            log.info("------------receiveErrorMessage开始消费消息-----------------",message);
            MessageDTO messageDTO = JSON.parseObject(message, MessageDTO.class);
            CosQueueDTO cosQueueDTO = JSON.parseObject(messageDTO.getJsonMapString(), CosQueueDTO.class);
            aiTaskService.dealErrorQueueMsg(cosQueueDTO);
            log.info("------------receiveErrorMessage消费消息结束-----------------",message);
        } catch (Exception e) {
            log.error("-----------------receiveErrorMessage队列监听消息异常【{}】" + e.getMessage(), e);
        }
    }

    @RabbitListener(queues = "${mq.bnzxQueue.queue-name}")
    public void receiveBnzxMessage(String message) {
        try {
            log.info("------------BNZX监听消息队列开始-----------------",message);
            MessageDTO messageDTO = JSON.parseObject(message, MessageDTO.class);
            if("1".equals(messageDTO.getBusinessType())){
                CosQueueDTO cosQueueDTO = JSON.parseObject(messageDTO.getJsonMapString(), CosQueueDTO.class);
                log.info("-------------------BNZX该条消息的tranceId-----------------------" + cosQueueDTO.getTraceId());
                aiTaskService.getAiResult(cosQueueDTO);
            }
            log.info("------------BNZX监听消息队列结束-----------------",message);
        } catch (Exception e) {
            log.error("-----------------BNZX队列监听消息异常【{}】" + e.getMessage(), e);
        }
    }
}
