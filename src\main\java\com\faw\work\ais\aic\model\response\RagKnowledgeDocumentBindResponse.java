package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 知识库文档绑定响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库文档绑定响应")
public class RagKnowledgeDocumentBindResponse {

    @Schema(description = "知识库ID")
    private Long ragKnowledgeId;

    @Schema(description = "知识库名称")
    private String knowledgeName;

    @Schema(description = "成功绑定的文档数量")
    private Integer successCount;

    @Schema(description = "失败的文档数量")
    private Integer failCount;

    @Schema(description = "成功绑定的文档ID列表")
    private List<Long> successDocumentIds;

    @Schema(description = "失败的文档ID列表")
    private List<Long> failDocumentIds;

    @Schema(description = "失败原因列表")
    private List<String> failReasons;
}
