package com.faw.work.ais.aic.common.util;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.faw.work.ais.common.exception.BizException;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 大模型调用工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class BaiLianUtils {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 调用大模型获取单个对象
     *
     * @param workSpace 工作空间
     * @param apiKey    百炼API Key
     * @param appId     百炼应用ID
     * @param content   输入内容 (作为prompt)
     * @param clazz     返回对象的类型
     * @param bizParams 自定义参数 参照：https://help.aliyun.com/zh/model-studio/application-calling-guide?spm=a2c4g.11186623.0.0.62532bf5TsdBcg#3d3707645dcej
     * @return 返回指定类型的对象
     */
    public static <T> T callForObject(String workSpace, String apiKey, String appId, String content, Class<T> clazz, Map<String, Object> bizParams) {
        try {
            String response = sendRequest(workSpace, apiKey, appId, content, bizParams);

            if (response == null) {
                return null;
            }

            // 如果目标类型就是 String，直接返回
            if (clazz == String.class) {
                return clazz.cast(response);
            }
            // 格式话 JSON 字符串，避免大模型抽风输出```json```格式的字符串
            response = StrUtils.cleanJsonString(response);
            return OBJECT_MAPPER.readValue(response, clazz);
        } catch (Exception e) {
            log.error("调用大模型获取对象异常，API Key: {}, App ID: {}, 内容: {}, 类型: {}",
                    apiKey, appId, content, clazz.getSimpleName(), e);
            throw new BizException("调用大模型异常: " + e.getMessage());
        }
    }


    /**
     * 调用大模型获取单个对象
     *
     * @param apiKey  百炼API Key
     * @param appId   百炼应用ID
     * @param content 输入内容 (作为prompt)
     * @param clazz   返回对象的类型
     * @param <T>     泛型类型
     * @return 返回指定类型的对象
     */
    public static <T> T callForObject(String workSpace, String apiKey, String appId, String content, Class<T> clazz) {
        try {
            String response = sendRequest(workSpace, apiKey, appId, content, null);

            if (response == null) {
                return null;
            }

            // 如果目标类型就是 String，直接返回
            if (clazz == String.class) {
                return clazz.cast(response);
            }
            // 格式话 JSON 字符串，避免大模型抽风输出```json```格式的字符串
            response = StrUtils.cleanJsonString(response);

            return OBJECT_MAPPER.readValue(response, clazz);
        } catch (Exception e) {
            log.error("调用大模型获取对象异常，API Key: {}, App ID: {}, 内容: {}, 类型: {}",
                    apiKey, appId, content, clazz.getSimpleName(), e);
            throw new BizException("调用大模型异常: " + e);
        }
    }

    /**
     * 调用大模型获取对象列表
     *
     * @param apiKey  百炼API Key
     * @param appId   百炼应用ID
     * @param content 输入内容 (作为prompt)
     * @param clazz   列表元素的类型
     * @param <T>     泛型类型
     * @return 返回指定类型的对象列表
     */
    public static <T> List<T> callForList(String workSpace, String apiKey, String appId, String content, Class<T> clazz, Map<String, Object> bizParams) {
        try {
            // 发送请求，content作为prompt
            String response = sendRequest(workSpace, apiKey, appId, content, bizParams);

            if (response == null) {
                return new ArrayList<>();
            }

            // 构建List类型的JavaType
            JavaType listType = OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz);

            // 格式话 JSON 字符串，避免大模型抽风输出```json```格式的字符串
            response = StrUtils.cleanJsonString(response);

            return OBJECT_MAPPER.readValue(response, listType);

        } catch (Exception e) {
            log.error("调用大模型获取列表异常，API Key: {}, App ID: {}, 内容: {}, 类型: {}",
                    apiKey, appId, content, clazz.getSimpleName(), e);
            throw new BizException("调用大模型异常: " + e.getMessage());
        }
    }

    /**
     * 发送请求调用百炼大模型
     *
     * @param apiKey    百炼API Key
     * @param appId     百炼应用ID
     * @param prompt    发送给模型的Prompt内容
     * @param bizParams 自定义参数
     * @return 模型的响应字符串，失败返回null
     */
    private static String sendRequest(String workSpace, String apiKey, String appId, String prompt, Map<String, Object> bizParams)
            throws NoApiKeyException, InputRequiredException, ApiException {

        // 构建基础参数
        ApplicationParam param = ApplicationParam.builder()
                .workspace(workSpace)
                .apiKey(apiKey)
                .appId(appId)
                .prompt(prompt)
                .build();

        // 处理业务参数
        if (bizParams != null && !bizParams.isEmpty()) {
            JsonObject userPromptParams = new JsonObject();
            // 将Map中的每个键值对添加到user_prompt_params中
            bizParams.forEach((key, value) -> {
                if (value instanceof String) {
                    userPromptParams.addProperty(key, (String) value);
                } else if (value instanceof Number) {
                    userPromptParams.addProperty(key, (Number) value);
                } else if (value instanceof Boolean) {
                    userPromptParams.addProperty(key, (Boolean) value);
                }
                // 其他类型可以根据需要添加
            });

            JsonObject bizParamsJson = new JsonObject();
            bizParamsJson.add("user_prompt_params", userPromptParams);

            param.setBizParams(bizParamsJson);
        }

        Application application = new Application();
        ApplicationResult result = application.call(param);

        if (result != null && result.getOutput() != null) {
            return result.getOutput().getText();
        }
        log.warn("大模型调用返回结果为空或输出文本为空。API Key: {}, App ID: {}, Prompt: {}", apiKey, appId, prompt);
        return null;
    }

    /**
     * 调用DashScope应用接口获取直接返回结果
     *
     * @param apiKey API Key，建议从环境变量获取
     * @param appId  应用ID
     * @param query  提示文本
     * @return 返回调用结果
     * @throws ApiException           API异常
     * @throws NoApiKeyException      无API Key异常
     * @throws InputRequiredException 输入缺失异常
     */
    public static String callDashScopeApp(String workspace, String apiKey, String appId, String query)
            throws ApiException, NoApiKeyException, InputRequiredException {
        ApplicationParam param = ApplicationParam.builder()
                .workspace(workspace)
                .apiKey(apiKey)
                .appId(appId)
                .prompt(query)
                .build();

        Application application = new Application();
        ApplicationResult result = application.call(param);

        return result.getOutput().getText();
    }
}
