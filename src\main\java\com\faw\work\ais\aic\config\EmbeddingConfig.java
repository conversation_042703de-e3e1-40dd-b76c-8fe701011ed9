package com.faw.work.ais.aic.config;

import com.alibaba.cloud.ai.dashscope.api.DashScopeApi;
import com.alibaba.cloud.ai.dashscope.embedding.DashScopeEmbeddingModel;
import com.alibaba.cloud.ai.dashscope.embedding.DashScopeEmbeddingOptions;
import org.springframework.ai.document.MetadataMode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 */
@Configuration
public class EmbeddingConfig {

    @Value("${ali.embedding.api-key:}")
    private String apiKey;

    @Value("${spring.ai.dashscope.content-key:}")
    private String contentKey;

    /**
     * 创建DashScopeApi实例
     */
    @Bean
    public DashScopeApi dashScopeApi() {
        return new DashScopeApi(apiKey);
    }

    @Bean
    public DashScopeApi contentApi() {
        return new DashScopeApi(contentKey);
    }

    /**
     * 创建text-embedding-v1模型
     */
    @Bean
    public DashScopeEmbeddingModel embeddingModelV1(DashScopeApi dashScopeApi) {
        return new DashScopeEmbeddingModel(
                dashScopeApi, 
                MetadataMode.EMBED,
                DashScopeEmbeddingOptions.builder()
                        .withModel("text-embedding-v1")
                        .build());
    }

    /**
     * 创建text-embedding-v2模型
     */
    @Bean
    public DashScopeEmbeddingModel embeddingModelV2(DashScopeApi dashScopeApi) {
        return new DashScopeEmbeddingModel(
                dashScopeApi, 
                MetadataMode.EMBED,
                DashScopeEmbeddingOptions.builder()
                        .withModel("text-embedding-v2")
                        .build());
    }

    /**
     * 创建text-embedding-v3模型
     */
    @Bean
    @Primary
    public DashScopeEmbeddingModel embeddingModelV3(DashScopeApi dashScopeApi) {
        return new DashScopeEmbeddingModel(
                dashScopeApi, 
                MetadataMode.EMBED,
                DashScopeEmbeddingOptions.builder()
                        .withModel("text-embedding-v3")
                        .build());
    }

    /**
     * 创建text-embedding-v2模型
     */
    @Bean
    public DashScopeEmbeddingModel embeddingModelV4(DashScopeApi dashScopeApi) {
        return new DashScopeEmbeddingModel(
                dashScopeApi,
                MetadataMode.EMBED,
                DashScopeEmbeddingOptions.builder()
                        .withModel("text-embedding-v4")
                        .build());
    }
    /**
     * 创建text-embedding-v1模型
     */
    @Bean("textEmbeddingV1")
    public DashScopeEmbeddingModel textEmbeddingV1(DashScopeApi contentApi) {
        return new DashScopeEmbeddingModel(
                contentApi,
                MetadataMode.EMBED,
                DashScopeEmbeddingOptions.builder()
                        .withModel("text-embedding-v1")
                        .build());
    }

    /**
     * 创建text-embedding-v2模型
     */
    @Bean("textEmbeddingV2")
    public DashScopeEmbeddingModel textEmbeddingV2(DashScopeApi contentApi) {
        return new DashScopeEmbeddingModel(
                contentApi,
                MetadataMode.EMBED,
                DashScopeEmbeddingOptions.builder()
                        .withModel("text-embedding-v2")
                        .build());
    }

    /**
     * 创建text-embedding-v3模型
     */
    @Bean("textEmbeddingV3")
    public DashScopeEmbeddingModel textEmbeddingV3(DashScopeApi contentApi) {
        return new DashScopeEmbeddingModel(
                contentApi,
                MetadataMode.EMBED,
                DashScopeEmbeddingOptions.builder()
                        .withModel("text-embedding-v3")
                        .build());
    }
}
