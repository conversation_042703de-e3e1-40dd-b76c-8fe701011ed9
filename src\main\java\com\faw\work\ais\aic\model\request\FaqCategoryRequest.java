package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 类目请求实体
 * <AUTHOR>
 */
@Data
@Schema(description = "类目请求")
public class FaqCategoryRequest {

    @Schema(description = "类目ID")
    private String id;

    @Schema(description = "类目名称")
    @NotBlank(message = "类目名称不能为空")
    @Size(max = 255, message = "类目名称长度不能超过255")
    private String name;
    
    @Schema(description = "父类ID，如果是一级类目则为null，如果是平级类目则为所选类目的parentId，如果是下级类目则所选类目ID")
    private String parentId;

    
    @Schema(description = "00-一级类目，01-平级类目，02-下级类目")
    private String type;

} 