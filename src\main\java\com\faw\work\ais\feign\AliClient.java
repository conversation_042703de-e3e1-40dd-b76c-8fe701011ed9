package com.faw.work.ais.feign;

import com.faw.work.ais.common.dto.chat.AnalysisRequest;
import com.faw.work.ais.common.dto.chat.AnalysisResponse;
import com.faw.work.ais.feign.content.AliFeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 阿里云Feign调用接口
 *
 * <AUTHOR>
 * @since 2023-03-07 16:06
 */
@FeignClient(value = "AliClient", url = "${spring.ai.dashscope.api-host:}")
public interface AliClient {

    /**
     * 大模型调用
     *
     * @param analysisRequest 请求体
     * @return 解析结果
     */
    @PostMapping("/compatible-mode/v1/chat/completions")
    AnalysisResponse analyze(@RequestBody AnalysisRequest analysisRequest,
                             @RequestHeader("Authorization") String key);

}
