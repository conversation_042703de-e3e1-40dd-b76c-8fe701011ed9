package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgeProdPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * FAQ相似问Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface FaqSimilarKnowledgeProdMapper extends BaseMapper<FaqSimilarKnowledgeProdPO> {

    @Select("<script>" +
            "SELECT * FROM faq_similar_knowledge_prod " +
            "WHERE knowledge_id IN " +
            "<foreach collection='originalKnowledgeIds' item='originalKnowledgeId' open='(' separator=',' close=')'>" +
            "   #{originalKnowledgeId}" +
            "</foreach>" +
            "</script>")
    List<FaqSimilarKnowledgeProdPO> selectByOriginalIds(@Param("originalKnowledgeIds") List<Long> originalKnowledgeIds);
}

