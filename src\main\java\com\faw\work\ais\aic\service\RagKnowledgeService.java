package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.RagKnowledgePO;
import com.faw.work.ais.aic.model.request.RagKnowledgeBasePageRequest;

import java.util.List;

/**
 * RAG知识库配置表 服务接口
 *
 * <AUTHOR> Assistant
 */
public interface RagKnowledgeService extends IService<RagKnowledgePO> {

    /**
     * 根据条件查询知识库列表
     *
     * @param knowledgeBase 查询条件
     * @return 知识库列表
     */
    List<RagKnowledgePO> getKnowledgeBaseList(RagKnowledgePO knowledgeBase);
    
    /**
     * 分页查询知识库
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    Page<RagKnowledgePO> getKnowledgeBasePage(RagKnowledgeBasePageRequest request);
    
    /**
     * 根据名称查询知识库
     *
     * @param name 知识库名称
     * @return 知识库信息
     */
    RagKnowledgePO getByName(String name);
} 