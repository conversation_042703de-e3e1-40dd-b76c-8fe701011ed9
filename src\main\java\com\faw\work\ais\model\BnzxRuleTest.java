package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 * @TableName bnzx_rule_test
 */
@Schema(description = "BNZX规则测试")
@TableName(value ="bnzx_rule_test")
@Data
public class BnzxRuleTest implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String filedesc;

    /**
     * 文件业务说明
     */
    @Schema(description = "文件业务说明")
    private String status;

    /**
     * 车辆编码
     */
    @Schema(description = "车辆编码")
    private String vin;

    /**
     * 购买价格
     */
    @Schema(description = "购买价格")
    private BigDecimal price;

    /**
     * 发票号
     */
    @Schema(description = "发票号")
    private String indicate;

    /**
     * 发票日期
     */
    @Schema(description = "发票日期")
    private Date indicatedate;

    /**
     * 发票日期
     */
    @Schema(description = "发票日期")
    private String saletype;

    /**
     * 车辆用途
     */
    @Schema(description = "车辆用途")
    private String caruse;

    /**
     * 车辆颜色
     */
    @Schema(description = "车辆颜色")
    private String carcolor;

    /**
     * 车辆运营单位
     */
    @Schema(description = "车辆运营单位")
    private String carunit;

    /**
     * 运营单位地址
     */
    @Schema(description = "运营单位地址")
    private String address;

    /**
     * 行驶证注册日期
     */
    @Schema(description = "行驶证注册日期")
    private Date drivedate;

    /**
     * 车牌照号
     */
    @Schema(description = "车牌照号")
    private String carliscens;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    private String idcard;

    /**
     * 社会信用代码
     */
    @Schema(description = "社会信用代码")
    private String shxycode;

    /**
     * 客户类型
     */
    @Schema(description = "客户类型")
    private String custype;

    /**
     * 行驶证号
     */
    @Schema(description = "行驶证号")
    private String driveno;

    /**
     * 图片路径
     */
    @Schema(description = "图片路径")
    private String picurl;

    /**
     * AI识别结果
     */
    @Schema(description = "AI识别结果")
    private String airesult;

    /**
     * 大模型返回结果
     */
    @Schema(description = "大模型返回结果")
    private String rawResult;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private String taskType;

    /**
     * 客户代码
     */
    @Schema(description = "客户代码")
    private String cuscode;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String cusname;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BnzxRuleTest other = (BnzxRuleTest) that;
        return (this.getFiledesc() == null ? other.getFiledesc() == null : this.getFiledesc().equals(other.getFiledesc()))
            && (this.getVin() == null ? other.getVin() == null : this.getVin().equals(other.getVin()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getIndicate() == null ? other.getIndicate() == null : this.getIndicate().equals(other.getIndicate()))
            && (this.getIndicatedate() == null ? other.getIndicatedate() == null : this.getIndicatedate().equals(other.getIndicatedate()))
            && (this.getSaletype() == null ? other.getSaletype() == null : this.getSaletype().equals(other.getSaletype()))
            && (this.getCaruse() == null ? other.getCaruse() == null : this.getCaruse().equals(other.getCaruse()))
            && (this.getCarcolor() == null ? other.getCarcolor() == null : this.getCarcolor().equals(other.getCarcolor()))
            && (this.getCarunit() == null ? other.getCarunit() == null : this.getCarunit().equals(other.getCarunit()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getDrivedate() == null ? other.getDrivedate() == null : this.getDrivedate().equals(other.getDrivedate()))
            && (this.getCarliscens() == null ? other.getCarliscens() == null : this.getCarliscens().equals(other.getCarliscens()))
            && (this.getIdcard() == null ? other.getIdcard() == null : this.getIdcard().equals(other.getIdcard()))
            && (this.getShxycode() == null ? other.getShxycode() == null : this.getShxycode().equals(other.getShxycode()))
            && (this.getCustype() == null ? other.getCustype() == null : this.getCustype().equals(other.getCustype()))
            && (this.getDriveno() == null ? other.getDriveno() == null : this.getDriveno().equals(other.getDriveno()))
            && (this.getPicurl() == null ? other.getPicurl() == null : this.getPicurl().equals(other.getPicurl()))
            && (this.getAiresult() == null ? other.getAiresult() == null : this.getAiresult().equals(other.getAiresult()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getFiledesc() == null) ? 0 : getFiledesc().hashCode());
        result = prime * result + ((getVin() == null) ? 0 : getVin().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getIndicate() == null) ? 0 : getIndicate().hashCode());
        result = prime * result + ((getIndicatedate() == null) ? 0 : getIndicatedate().hashCode());
        result = prime * result + ((getSaletype() == null) ? 0 : getSaletype().hashCode());
        result = prime * result + ((getCaruse() == null) ? 0 : getCaruse().hashCode());
        result = prime * result + ((getCarcolor() == null) ? 0 : getCarcolor().hashCode());
        result = prime * result + ((getCarunit() == null) ? 0 : getCarunit().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getDrivedate() == null) ? 0 : getDrivedate().hashCode());
        result = prime * result + ((getCarliscens() == null) ? 0 : getCarliscens().hashCode());
        result = prime * result + ((getIdcard() == null) ? 0 : getIdcard().hashCode());
        result = prime * result + ((getShxycode() == null) ? 0 : getShxycode().hashCode());
        result = prime * result + ((getCustype() == null) ? 0 : getCustype().hashCode());
        result = prime * result + ((getDriveno() == null) ? 0 : getDriveno().hashCode());
        result = prime * result + ((getPicurl() == null) ? 0 : getPicurl().hashCode());
        result = prime * result + ((getAiresult() == null) ? 0 : getAiresult().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", filedesc=").append(filedesc);
        sb.append(", vin=").append(vin);
        sb.append(", price=").append(price);
        sb.append(", indicate=").append(indicate);
        sb.append(", indicatedate=").append(indicatedate);
        sb.append(", saletype=").append(saletype);
        sb.append(", caruse=").append(caruse);
        sb.append(", carcolor=").append(carcolor);
        sb.append(", carunit=").append(carunit);
        sb.append(", address=").append(address);
        sb.append(", drivedate=").append(drivedate);
        sb.append(", carliscens=").append(carliscens);
        sb.append(", idcard=").append(idcard);
        sb.append(", shxycode=").append(shxycode);
        sb.append(", custype=").append(custype);
        sb.append(", driveno=").append(driveno);
        sb.append(", picurl=").append(picurl);
        sb.append(", airesult=").append(airesult);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}