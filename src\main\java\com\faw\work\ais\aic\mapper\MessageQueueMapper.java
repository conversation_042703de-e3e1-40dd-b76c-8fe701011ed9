package com.faw.work.ais.aic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface MessageQueueMapper extends BaseMapper<MessageQueue> {
    /**
     * 按消息id选择
     *
     * @param messageId 消息id
     * @return {@link MessageQueue }
     */
    MessageQueue selectByMessageId(String messageId);
}