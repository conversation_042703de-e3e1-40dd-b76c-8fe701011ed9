package com.faw.work.ais.aic.model.domain;

import com.faw.work.ais.aic.model.dto.MilvusField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Milvus文档属性
 * 用于动态指定Milvus文档的属性和值
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MilvusRow {

    /**
     * 主键(必须)
     */
    private String vectorId;


    /**
     * 关键信息的embedding向量(必须)
     */
    private float[] embedding;

    /**
     * 动态属性列表
     */
    private List<MilvusField> properties;


    /**
     * 创建一个MilvusRow实例，封装向量ID、嵌入向量和自定义属性。
     *
     * @param vectorId   向量的唯一标识符。
     * @param embedding  向量的浮点数数组表示（例如，由机器学习模型生成）。
     * @param properties 一个包含额外元数据的Map。
     *                   键（String）代表属性名，值（Object）代表属性值。
     *                   例如：`Map.of("name", "document_1", "category", "financial", "length", 120)`
     *                   这些属性将被转换为MilvusField对象列表并关联到MilvusRow。
     * @return 封装了所有给定数据的MilvusRow实例。
     */
    MilvusRow create(String vectorId, float[] embedding, Map<String, Object> properties) {
        MilvusRow milvusRow = new MilvusRow();
        milvusRow.setVectorId(vectorId);
        milvusRow.setProperties(MilvusField.buildProperties(properties));
        milvusRow.setEmbedding(embedding);
        return milvusRow;
    }

} 