package com.faw.work.ais.controller;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.HumanResultDTO;
import com.faw.work.ais.entity.dto.ai.ProcessInfoDTO;
import com.faw.work.ais.entity.vo.ai.BizUnitAndTaskRuleInfoVO;
import com.faw.work.ais.entity.vo.ai.SystemVO;
import com.faw.work.ais.service.CommonService;
import com.faw.work.ais.service.HumanResultService;
import com.faw.work.ais.service.KanBanService;
import com.faw.work.ais.service.NumberEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Schema(description = "涉及多个能力中心的接口")
@Slf4j
@RestController("CommonController")
@RequestMapping("/common")
public class CommonController {

    @Autowired
    private HumanResultService humanResultService;

    @Autowired
    private NumberEmployeeService numberEmployeeService;

    @Autowired
    private CommonService commonService;

    @Operation(summary = "接收人工审核结果", description = "[author:10236535]")
    @PostMapping(value = "/getHumanResultByDay")
    public Response getHumanResultByDay(@RequestBody @Valid List<HumanResultDTO> humanResultList) {
        return humanResultService.obsiveHumanResultDataFromAbalityCenter(humanResultList);
    }

    @Autowired
    private KanBanService kanBanService;

    @Operation(summary = "系统下拉列表接口", description = "[author:10236535]")
    @PostMapping(value = "/getSystemInfo")
    public Response<List<SystemVO>> getSystemInfo(@RequestBody ProcessInfoDTO processInfoDTO) {
        List<SystemVO> systemVOS = numberEmployeeService.getSystemInfoByUnitCode(processInfoDTO);
        Response<List<SystemVO>> response = new Response<>();
        response.setMessage("");
        response.setData(systemVOS);
        return response;
    }

    @Operation(summary = "根据业务单元代码获取规则内容", description = "[author:10236535]")
    @PostMapping(value = "/getRuleInfoByUnitCode")
    public Response<List<BizUnitAndTaskRuleInfoVO>> getRuleInfoByUnitCode(@RequestBody List<String> bizUnitCodes) {
        Response<List<BizUnitAndTaskRuleInfoVO>> response = new Response<>();
        try {
            List<BizUnitAndTaskRuleInfoVO> vos = commonService.getBizUnitInfos(bizUnitCodes);
            response.setCode("200");
            response.setMessage("请求成功");
            response.setData(vos);
            return response;
        }catch (Exception e){
            log.error("获取规则内容异常" + e.getMessage(), e);
            response.setCode("-111");
            response.setMessage("请求失败" + e.getMessage());
            return response;
        }
    }



}
