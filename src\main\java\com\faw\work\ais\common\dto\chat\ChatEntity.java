package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 聊天实体
 *
 * <AUTHOR>
 * @since 2025-04-03 15:41
 */
@Data
@Schema(description = "聊天实体")
public class ChatEntity {

    /**
     * 聊天id
     */
    @Schema(description = "聊天id")
    private String chatId;

    /**
     * 聊天类型
     */
    @Schema(description = "聊天类型")
    private String type;

    /**
     * 聊天内容
     */
    @Schema(description = "聊天内容")
    private String text;

}
