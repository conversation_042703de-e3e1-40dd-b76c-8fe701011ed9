package com.faw.work.ais.common.dto.chat;

import com.faw.work.ais.model.chat.AppChatHistoryDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 智能问答聊天记录 数据传输实体
 *
 * <AUTHOR>
 * @since 2025-04-27 10:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "智能问答聊天记录表")
public class AppChatHistoryDto {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private String appId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private String sessionId;

    /**
     * 会话标题
     */
    @Schema(description = "会话标题")
    private String chatTitle;

    /**
     * 删除标识（0-否，1-是）
     */
    @Schema(description = "删除标识（0-否，1-是）")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private String createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private String updatedAt;

    /**
     * 详情列表
     */
    @Schema(description = "详情列表")
    private List<AppChatHistoryDetail> detailList;

}
