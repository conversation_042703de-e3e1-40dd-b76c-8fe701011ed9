package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * FAQ运营报表查询请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ运营报表查询请求")
public class FaqReportQueryRequest {

    @Schema(description = "机器人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "机器人ID不能为空")
    private String robotId;

    @Schema(description = "环境：prod-正式环境, test-测试环境", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "环境不能为空")
    private String env;

    @Schema(description = "查询开始时间 (格式: yyyy-MM-dd HH:mm:ss)")
    private String startTime;

    @Schema(description = "查询结束时间 (格式: yyyy-MM-dd HH:mm:ss)")
    private String endTime;
} 