package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "处理对话请求")
public class ProcessRequest {

    @Schema(description = "对话ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "对话ID不能为空")
    private String requestId;

    @Schema(description = "录音内容，格式如下：(start:0000) 接待专员：您好，我是一汽红旗的产品顾问，看您在网上关注我们。请问您是王鑫先生吗？ (end:5000)\n" +
            "(start:6000) 客户：是的！ (end:7000)\n" +
            "(start:8000) 客户：请问您最近有买车的需求吗？ (end:10000)\n" +
            "(start:11000) 客户：有，你们最右有什么车型有活动吗？ (end:14000)\n" +
            "(start:15000) 接待专员：我们有好几款车有活动，例如HS5，H5，EH7，天工08，请问您买车的需求是什么呢？ (end:21000)\n" +
            "(start:22000) 客户：我主要是用于城市通勤，上班下班。天工08都有啥活动？ (end:27000)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userInput;

    @Schema(description = "文本的腾讯云cos桶url")
    private String textUrl;

    @Schema(description = "语音的腾讯云cos桶url")
    private String audioUrl;

    @Schema(description = "ai请求")
    private AiRequest aiRequest;
} 