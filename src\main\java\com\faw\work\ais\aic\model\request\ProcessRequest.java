package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "处理对话请求")
public class ProcessRequest {

    @Schema(description = "对话ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "对话ID不能为空")
    private String requestId;

    @Schema(description = "录音内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userInput;

    @Schema(description = "文本的腾讯云cos桶url")
    private String textUrl;

    @Schema(description = "语音的腾讯云cos桶url")
    private String audioUrl;

    @Schema(description = "请求")
    private AiRequest aiRequest;
} 