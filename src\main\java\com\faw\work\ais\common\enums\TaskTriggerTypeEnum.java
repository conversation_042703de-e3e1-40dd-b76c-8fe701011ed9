package com.faw.work.ais.common.enums;

public enum TaskTriggerTypeEnum {
    MANUAL(0, "手动"),
    EVENT(1, "事件");

    TaskTriggerTypeEnum(Integer code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
