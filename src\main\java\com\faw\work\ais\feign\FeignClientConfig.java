package com.faw.work.ais.feign;

import feign.Request;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * feign配置
 *
 * <AUTHOR>
 * @since 2024-07-05 10:44
 */
@Configuration
public class FeignClientConfig {

    @Value("${feign.client.config.default.connectTimeoutMillis:1800000}")
    private int connectTimeoutMillis;

    @Value("${feign.client.config.default.readTimeoutMillis:1800000}")
    private int readTimeoutMillis;

    @Bean
    public Request.Options options() {
        return new Request.Options(connectTimeoutMillis, TimeUnit.MILLISECONDS, readTimeoutMillis, TimeUnit.MILLISECONDS, true);
    }
}
