package com.faw.work.ais.aic.common.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.slf4j.MDC;

import java.time.LocalDateTime;

/**
 * AI响应信息主体
 * <AUTHOR>
 */
@Schema(description = "AI响应信息主体")
@Data
public class AiResult<T> {

    @Schema(description = "响应代码 成功：200；失败：-5001", example = "200")
    private Integer code;

    @Schema(description = "响应描述", example = "请求成功")
    private String message;

    @Schema(description = "唯一码，查日志使用")
    private String traceId;

    @Schema(description = "响应的时间戳")
    private LocalDateTime timestamp;

    @Schema(description = "返回数据的结构体")
    private T data;

    private AiResult(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.traceId = MDC.get("traceId");
        this.timestamp = LocalDateTime.now();
    }

    public AiResult() {
        this.traceId = MDC.get("traceId");
        this.timestamp = LocalDateTime.now();
    }

    public Boolean isSuccess() {
        return 200 == this.code;
    }

    public static <T> AiResult<T> of() {
        return new AiResult<>();
    }

    public static <T> AiResult<T> of(T data) {
        AiResult<T> result = new AiResult<>();
        result.setData(data);
        return result;
    }

    public static <T> AiResult<T> of(String message, Integer code) {
        AiResult<T> result = new AiResult<>();
        result.setMessage(message);
        result.setCode(code);
        return result;
    }

    public static <T> AiResult<T> of(T data, String message) {
        AiResult<T> result = new AiResult<>();
        result.setData(data);
        result.setMessage(message);
        return result;
    }

    public static <T> AiResult<T> fail(String msg) {
        AiResult<T> result = new AiResult<>();
        result.setMessage(msg);
        result.setCode(-5001);
        return result;
    }

    public static <T> AiResult<T> success(T data) {
        AiResult<T> result = new AiResult<>();
        result.setData(data);
        result.setMessage("操作成功");
        result.setCode(200);
        return result;
    }

    public static <T> AiResult<T> success() {
        AiResult<T> result = new AiResult<>();
        result.setMessage("操作成功");
        result.setCode(200);
        return result;
    }

    public static <T> AiResult<T> successMsg(String msg) {
        AiResult<T> result = new AiResult<>();
        result.setMessage(msg);
        result.setCode(200);
        return result;
    }
}