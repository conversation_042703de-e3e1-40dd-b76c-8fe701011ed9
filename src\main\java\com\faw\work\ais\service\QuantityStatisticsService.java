package com.faw.work.ais.service;

import java.util.Map;
import java.util.List;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.*;
import org.springframework.web.bind.annotation.RequestBody;

/**
* 数量统计表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 14:12:35
*/
public interface QuantityStatisticsService {

    /**
    * 新增或修改
    */
    public Result<Integer> insertOrUpdate(QuantityStatistics quantityStatistics);

    /**
    * 新增
    */
    public Result<Integer> insert(QuantityStatistics quantityStatistics);


   /**
    * 分页全部查询
    */
    public Result<List<QuantityStatisticsDayValueRes>> getQuantityStatisticsList(QuantityStatistics quantityStatistics);
    /**
     * ai审核汇总数据
     */
    Result<QuantityStatisticsTotalValue> getAiQuantityStatistics(QuantityStatistics quantityStatistics);
    /**
     * 人工审核汇总数据
     */
    Result<QuantityStatisticsTotalValue> getHumQuantityStatistics(QuantityStatistics quantityStatistics);
    /**
     * 人工审核汇总数据 tp,np,tn,fn 數據
     */
    Result<QuantityStatisticsTotalTp> getHumTpStatistics(@RequestBody QuantityStatistics quantityStatistics);
}

