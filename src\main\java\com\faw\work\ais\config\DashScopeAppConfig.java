package com.faw.work.ais.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
@Data
@RefreshScope
public class DashScopeAppConfig {
    @Value("${spring.ai.app.car-identify.workspace:}")
    private String workspace;

    @Value("${spring.ai.app.car-identify.apiKey:}")
    private String apiKey;

    @Value("${spring.ai.app.car-identify.appId:}")
    private String appId;
}
