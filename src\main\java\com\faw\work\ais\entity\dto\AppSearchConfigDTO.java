package com.faw.work.ais.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 检索系统配置表实体类
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "检索系统配置表")
public class AppSearchConfigDTO {

    @Schema(description = "配置ID(主键自增)")
    private Integer id;

    @Schema(description = "配置ID(主键自增)")
    private Integer appId;

    /**
     * 互联网搜索开关(0关闭/1开启)
     */
    @Schema(description = "互联网搜索开关(0关闭/1开启)", example = "true")
    private Boolean webSearchEnabled;

    /**
     * 知识库拼装策略
     * count_based:按召回数量 smart:智能拼装
     */
    @Schema(description = "知识库拼装策略(count_based:按召回数量 smart:智能拼装)",
            example = "count_based")
    private String assemblyStrategy;

    /**
     * 召回片段数(范围1-20)
     */
    @Schema(description = "召回片段数(范围1-20)", example = "5")
    private Integer fragmentCount;

    /**
     * 知识库拼装最大长度(范围1-30720)
     */
    @Schema(description = "知识库拼装最大长度(范围1-30720)", example = "2000")
    private Integer maxAssemblyLength;

    /**
     * 回答范围
     * combined:知识库+大模型 knowledge_only:仅知识库
     */
    @Schema(description = "回答范围(combined:知识库+大模型 knowledge_only:仅知识库)",
            example = "combined")
    private String answerScope;

    /**
     * 展示回答来源(0关闭/1开启)
     */
    @Schema(description = "展示回答来源(0关闭/1开启)", example = "false")
    private Boolean showSources;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2023-01-01T00:00:00")
    private Date createdAt;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间", example = "2023-01-01T00:00:00")
    private Date updatedAt;

    // 移除了内部枚举类，改为直接使用字符串存储
    // 可以在Service层添加转换方法或使用MyBatis的类型处理器(TypeHandler)处理枚举
}