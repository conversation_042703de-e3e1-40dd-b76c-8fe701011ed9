package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import com.faw.work.ais.aic.model.request.FaqCategoryRequest;
import com.faw.work.ais.aic.model.response.FaqCategoryResponse;

import java.util.List;

/**
 * FaqCategoryService 接口，提供FAQ类目相关的服务。
 * <AUTHOR>
 */
public interface FaqCategoryService extends IService<FaqCategoryPO> {
    /**
     * 根据类目名称获取类目列表。
     *
     * @param categoryName 类目名称
     * @return 符合条件的类目列表
     */
    List<FaqCategoryPO> getCategoryListByName(String categoryName);


    /**
     * 根据名称获取类目ID。
     *
     * @param categoryName 类目名称
     * @return 类目ID，如果不存在则返回null
     */
    String getIdByName(String categoryName);

    /**
     * 创建新类目。
     *
     * @param categoryName 类目名称
     * @return 新创建的类目ID
     */
    String createCategory(String categoryName);


    /**
     * 灵活创建类目，支持一级类目、平级类目和下级类目。
     *
     * @param request 类目请求，包含类目创建的相关信息
     * @return 创建的类目ID
     */
    String createFlexibleCategory(FaqCategoryRequest request);


    /**
     * 根据名称搜索类目，并以树形结构返回。
     *
     * @param env 环境标识
     * @return 类目列表，以树形结构组织
     */
    List<FaqCategoryResponse> treeCategory(String env);

    /**
     * 根据类目ID删除所有相关数据。
     *
     * @param id 类目ID
     */
    void deleteAllDataByCategoryId(String id);
}
