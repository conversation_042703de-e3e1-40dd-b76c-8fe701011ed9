package com.faw.work.ais.common.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public class InputStreamMultipartFile implements MultipartFile {

    private final String name;
    private final String originalFilename;
    private final byte[] content;

    public InputStreamMultipartFile(String name, String originalFilename, InputStream inputStream) throws IOException {
        this.name = name;
        this.originalFilename = originalFilename;
        this.content = inputStream.readAllBytes();
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }

    @Override
    public String getContentType() {
        return "application/octet-stream"; // Set the appropriate content type
    }

    @Override
    public boolean isEmpty() {
        return content.length == 0;
    }

    @Override
    public long getSize() {
        return content.length;
    }

    @Override
    public byte[] getBytes() {
        return content;
    }

    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(content);
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        // Implement file transfer if needed
    }
}
