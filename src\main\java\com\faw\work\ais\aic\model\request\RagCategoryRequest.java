package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文档类目请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "文档类目请求")
public class RagCategoryRequest {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "类目名称")
    private String name;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "更新人")
    private String updatedBy;
} 