package com.faw.work.ais.controller;

import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Schema(description = "运行状况监视器控制器")
@Slf4j
@Api(tags = "healthMonitor")
@RestController("HealthMonitorController")
@RequestMapping("/health")
public class HealthMonitorController {

    @Operation(summary = "运行状况监视器控制器", description = "[author:10236535]")
    @GetMapping("/empty")
    public void dynamicUrlTest(){
        log.info("--------------empty接口开始执行--------------------");

        log.info("--------------empty接口执行完成--------------------");
    }
}
