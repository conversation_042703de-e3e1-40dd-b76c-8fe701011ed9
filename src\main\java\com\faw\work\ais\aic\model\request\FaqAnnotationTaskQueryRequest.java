package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * FAQ标注任务查询请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ标注任务查询请求对象")
public class FaqAnnotationTaskQueryRequest {

    @Schema(description = "任务名称（模糊搜索）")
    private String taskName;

    @Schema(description = "任务状态：processing-进行中，completed-已完成")
    private String status;

    @Schema(description = "机器人ID")
    private String robotId;

    @Schema(description = "创建人ID")
    private String creatorId;

    @Schema(description = "页码", defaultValue = "1")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @Schema(description = "每页大小", defaultValue = "10")
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize;
}
