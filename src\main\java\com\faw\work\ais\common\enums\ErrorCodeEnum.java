package com.faw.work.ais.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-01-09 10:21
 */
@AllArgsConstructor
@Getter
public enum ErrorCodeEnum {

    /**
     * 错误码枚举
     */
    INTERNAL_FAILED(5000, "服务器内部错误"),

    TOO_MANY_DATA(5001, "数据过长"),

    PARAMETER_ERROR(5002, "错误的参数"),

    DATA_EXCEPTION(5003, "数据异常"),

    AUTH_EXCEPTION(5004, "无权限"),

    DATA_EXIST(5005, "数据已存在"),

    DATE_FORMAT_ERROR(5006, "日期格式不正确"),

    DATE_RANGE_ERROR(5007, "日期范围不正确"),

    DISTRIBUTED_LOCK_FAILED(5008, "分布式锁失败"),

    SERVER_BUSY(5009, "服务器繁忙，请稍后再试！"),

    FILE_NUMBER_ERROR(5010, "导入数据过大"),

    RPC_FAILED(600, "rpc请求失败"),

    DATA_PERSISTENCE_FAILED(700, "数据持久化失败"),

    UPLOAD_FILE_ERROR(800, "上传文件失败"),

    DELETE_FILE_ERROR(801, "删除文件失败"),

    EMPTY_VALUE(5100, "存在空值"),

    REQUEST_PARAM_NULL(5101, "请求参数存在空值"),

    PROMPT_MESSAGE_FAILED(5120, "导入模版错误，导入失败"),

    UPLOAD_MATERIAL_MARK(5220, "请上传审批材料留痕"),

    DEALER_CODE_REQUIRED(5230, "经销商代码必填！"),

    CAR_SERIES_REQUIRED(5240, "车系必填"),

    REGIONAL_AREA_REQUIRED(5250, "大区必填"),

    INDEX_VALUE_REQUIRED(5260, "目标数必填，并且1000000以内"),

    REMARK_CHECK(5270, "备注不能超过50个字"),

    FINAL_TARGET_EXPORT_FAILED(5280, "终版目标导出失败"),

    INNER_RELEASE_FINAL_FAILED(5290, "当前状态不允许定版操作"),

    INNER_RELEASE_FINAL_UPDATE_FAILED(5300, "更新条数不正确，请确认"),

    SYNC_TDS_FINAL_TARGET_FAILED(5310, "更新条数不正确，请确认"),

    TIMED_TASK_EXECUTE_FAILED(6000, "定时任务执行失败"),

    CREATE_TASK_FAILED(6101, "创建任务失败"),

    COMPLETE_TASK_FAILED(6102, "结束任务失败"),

    ACCESS_FREQUENT(7000, "访问频繁"),

    ENCRYPTION_FAILURE(8000, "加密失败"),
    ;
    private final int code;

    private final String desc;
}
