package com.faw.work.ais.aic.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 应用级配置类
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@Component
@Data
@RefreshScope
public class AicConfig {
    /**
     * 服务环境
     */
    @Value("${aic.config.environment:uat}")
    private String environment;

    /**
     * 平台应用key
     */
    @Value("${aic.config.ucg.app-key:9601007f36ce46dda67701d99e623446}")
    private String appKey;

    /**
     * 平台应用secret
     */
    @Value("${aic.config.ucg.app-secret:21c40f91d0e64cbf8a23c1f683f324d5}")
    private String appSecret;

    /**
     * 网关地址
     */
    @Value("${aic.config.ucg.host:https://uat-api.faw.cn:30443}")
    private String host;

    /**
     * 知识中心地址
     */
    @Value("${aic.config.ucg.dms-knowledge-center-host:https://miw-coc-uat.faw.cn}")
    private String dmsKnowledgeCenter;



}
