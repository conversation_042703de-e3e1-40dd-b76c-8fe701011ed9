package com.faw.work.ais.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.base.PageResult;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.dto.ai.QueryHumanResultDTO;
import com.faw.work.ais.entity.vo.ai.*;
import com.faw.work.ais.mapper.ais.*;
import com.faw.work.ais.model.AiTaskResultNew;
import com.faw.work.ais.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class AiOperationCenterServiceImpl implements AiOperationCenterService {

    @Autowired
    private ViewAiCoverNumMapper viewAiCoverNumMapper;

    @Autowired
    private HumanResultMapper humanResultMapper;

    @Autowired
    private TaskRuleMapper taskRuleMapper;

    @Autowired
    private SystemCallBackUrlMapper systemCallBackUrlMapper;

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private FileInfoNewDao fileInfoNewDao;

    @Override
    public AiCoveringScenesVO getAiCoveringScenesInfo() {

        AiCoveringScenesVO aiCoveringScenesVO = viewAiCoverNumMapper.getViewAiCoverNum();

        if (ObjectUtil.isEmpty(aiCoveringScenesVO)) {
            throw new BizException("场景覆盖率未空，请确认数据");
        }

        if (CommonConstants.ZERO.equals(aiCoveringScenesVO.getAiCoverRuleNum())) {
            aiCoveringScenesVO.setOnlineCoverageRate(BigDecimal.ZERO);
        } else {
            BigDecimal onlineCoverageRate = BigDecimal.valueOf(aiCoveringScenesVO.getOnlineCount() * CommonConstants.HUNDRED)
                    .divide(BigDecimal.valueOf(aiCoveringScenesVO.getAiCoverRuleNum()), CommonConstants.TWO, RoundingMode.HALF_UP);
            aiCoveringScenesVO.setOnlineCoverageRate(onlineCoverageRate);
        }

        return aiCoveringScenesVO;
    }

    @Override
    public List<SystemVO> getSystemInfo() {
        return systemCallBackUrlMapper.getKanBanSystemList();
    }

    @Override
    public List<TaskRuleVO> getTaskRuleBySystemId(String systemId) {
        return taskRuleMapper.getTaskRuleBySystemId(systemId);
    }

    @Override
    public ApproveRightRateVo billApproveRightRate(QueryHumanResultDTO dto) {

        try {
            return approveRightRate(dto, CommonConstants.BATCH_ID);
        }
        catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            throw new BizException("获取单据准确率失败");
        }
        return new ApproveRightRateVo();
    }

    @Override
    public ApproveRightRateVo ruleApproveRightRate(QueryHumanResultDTO dto) {

        try {
            return approveRightRate(dto, CommonConstants.TRACE_ID);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            throw new BizException("获取单据准确率失败");
        }
        return new ApproveRightRateVo();
    }

    @Override
    public AiOperationCenterCountVo getCount(QueryHumanResultDTO dto) {

        try {
            return buildAiOperationCenterCountVo(dto);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            throw new BizException("获取数量失败");
        }

        return new AiOperationCenterCountVo();
    }

    @Override
    public List<BeCheckFileInfoVO> getFileInfosByTraceId(FileDTO fileDTO) {

        List<BeCheckFileInfoVO> files = new ArrayList<>();
        // 查询文件地址列表
        List<String> fileUrls = fileInfoNewDao.getFileInfosByTraceId(fileDTO.getTraceId());

        if(CollectionUtils.isNotEmpty(fileUrls)){
            fileUrls.forEach(url->{
                BeCheckFileInfoVO file = new BeCheckFileInfoVO();
                file.setFileDownUrl(url);
                files.add(file);
            });
        }

        return files;
    }

    private AiOperationCenterCountVo buildAiOperationCenterCountVo(QueryHumanResultDTO dto) throws InterruptedException {
        AiOperationCenterCountVo aiOperationCenterCountVo = new AiOperationCenterCountVo();

        CountDownLatch latch = new CountDownLatch(5);

        AtomicReference<BigDecimal> billApproveCount = new AtomicReference<>(BigDecimal.ZERO);
        // ai审核通过人工通过
        AtomicReference<BigDecimal> tpCount = new AtomicReference<>(BigDecimal.ZERO);
        // ai审核通过人工驳回
        AtomicReference<BigDecimal> tnCount = new AtomicReference<>(BigDecimal.ZERO);
        // ai审核驳回人工通过
        AtomicReference<BigDecimal> fpCount = new AtomicReference<>(BigDecimal.ZERO);
        // ai审核驳回人工驳回
        AtomicReference<BigDecimal> fnCount = new AtomicReference<>(BigDecimal.ZERO);

        //线程池代码--------------start------------------
        threadPoolTaskExecutor.execute(() -> {

            // 获取单据准确率
            Integer count = humanResultMapper.getApproveCount(dto, null);
            log.info("getCount() 单据审核数量 = " + count);
            billApproveCount.set(BigDecimal.valueOf(count));

            latch.countDown();
        });

        //线程池代码--------------start------------------
        threadPoolTaskExecutor.execute(() -> {

            // 获取单据准确率
            Integer count = humanResultMapper.getApproveCount(dto, CommonConstants.TP);
            log.info("getCount() ai审核通过人工通过 = " + count);
            tpCount.set(BigDecimal.valueOf(count));
            latch.countDown();
        });

        //线程池代码--------------start------------------
        threadPoolTaskExecutor.execute(() -> {

            // 获取单据准确率
            Integer count = humanResultMapper.getApproveCount(dto, CommonConstants.TN);
            log.info("getCount() ai审核通过人工驳回 = " + count);
            tnCount.set(BigDecimal.valueOf(count));

            latch.countDown();
        });

        //线程池代码--------------start------------------
        threadPoolTaskExecutor.execute(() -> {

            // 获取单据准确率
            Integer count = humanResultMapper.getApproveCount(dto, CommonConstants.FP);
            log.info("getCount() ai审核驳回人工通过 = " + count);
            fpCount.set(BigDecimal.valueOf(count));

            latch.countDown();
        });
        //线程池代码--------------start------------------
        threadPoolTaskExecutor.execute(() -> {

            // 获取单据准确率
            Integer count = humanResultMapper.getApproveCount(dto, CommonConstants.FN);
            log.info("getCount() ai审核驳回人工驳回 = " + count);
            fnCount.set(BigDecimal.valueOf(count));

            latch.countDown();
        });

        latch.await();

        aiOperationCenterCountVo.setAiBillApproveCount(billApproveCount.get());
        aiOperationCenterCountVo.setHumanBillApproveCount(billApproveCount.get());
        aiOperationCenterCountVo.setAiApprovePassCount(tpCount.get().add(tnCount.get()));
        aiOperationCenterCountVo.setAiApproveRejectCount(fpCount.get().add(fnCount.get()));
        aiOperationCenterCountVo.setTpCount(tpCount.get());
        aiOperationCenterCountVo.setTnCount(tnCount.get());

        if (tpCount.get().compareTo(BigDecimal.ZERO) == CommonConstants.ZERO) {
            aiOperationCenterCountVo.setTpRightRate(BigDecimal.ZERO);
        } else {
            aiOperationCenterCountVo.setTpRightRate(tpCount.get().multiply(BigDecimal.valueOf(100)).divide(aiOperationCenterCountVo.getAiApprovePassCount(), 2, RoundingMode.HALF_UP));
        }

        aiOperationCenterCountVo.setFpCount(fpCount.get());
        aiOperationCenterCountVo.setFnCount(fnCount.get());

        if (fnCount.get().compareTo(BigDecimal.ZERO) == CommonConstants.ZERO) {
            aiOperationCenterCountVo.setFnRightRate(BigDecimal.ZERO);
        } else {
            aiOperationCenterCountVo.setFnRightRate(fnCount.get().multiply(BigDecimal.valueOf(100)).divide(aiOperationCenterCountVo.getAiApproveRejectCount(), 2, RoundingMode.HALF_UP));
        }
        return aiOperationCenterCountVo;
    }

    public ApproveRightRateVo approveRightRate(QueryHumanResultDTO dto, String type) throws InterruptedException {
        ApproveRightRateVo approveRightRateVo = new ApproveRightRateVo();

        log.info("-------approveRightRate() 运营中心----单据准确率开始------dto={},type={}", JSONObject.toJSONString(dto), type);

        CountDownLatch latch = new CountDownLatch(2);
        AtomicReference<BigDecimal> dividend = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> divisor = new AtomicReference<>(BigDecimal.ZERO);

        // 如果分类包含抄送我的，查询分类是自定义的且抄送表数据 （前提是抄送我的分类没有隐藏）

        //线程池代码--------------start------------------
        threadPoolTaskExecutor.execute(() -> {

            // 获取单据准确率
            Integer dividendCount = humanResultMapper.getHumanResultDividendCount(dto, type);
            log.info("approveRightRate() 获取分子数量 = " + dividendCount);
            dividend.set(BigDecimal.valueOf(dividendCount));
            latch.countDown();
        });

        //线程池代码--------------start------------------
        threadPoolTaskExecutor.execute(() -> {

            // 获取单据准确率
            Integer divisorCount = humanResultMapper.getHumanResultDivisorCount(dto, type);
            log.info("approveRightRate() 获取分母数量 = " + divisorCount);
            divisor.set(BigDecimal.valueOf(divisorCount));
            latch.countDown();
        });

        latch.await();

        approveRightRateVo.setDividend(dividend.get());
        approveRightRateVo.setDivisor(divisor.get());

        if (dividend.get().compareTo(BigDecimal.ZERO) == CommonConstants.ZERO) {
            approveRightRateVo.setQuotient(BigDecimal.ZERO);
        } else {
            approveRightRateVo.setQuotient(dividend.get().multiply(BigDecimal.valueOf(100)).divide(divisor.get(), CommonConstants.TWO, RoundingMode.HALF_UP));
        }
        log.info("-------approveRightRate() 运营中心----单据准确率结束------dto={},type={},approveRightRateVo={}", JSONObject.toJSONString(dto), type, JSONObject.toJSONString(approveRightRateVo));
        return approveRightRateVo;
    }

    @Override
    public PageResult<HumanResultVo> getHumanResultList(QueryHumanResultDTO dto) {

        List<HumanResultVo> resultList = new ArrayList<>();

        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());

        List<HumanResultVo> humanResultList = humanResultMapper.getHumanResultList(dto);

        if (CollectionUtil.isEmpty(humanResultList)) {
            return PageResult.<HumanResultVo>builder()
                    .totalNum(0L)
                    .data(resultList)
                    .build();
        }

        List<String> traceIdList = humanResultList.stream().map(k -> k.getTraceId()).collect(Collectors.toList());
        List<String> taskTypeList = humanResultList.stream().map(k -> k.getTaskType()).collect(Collectors.toList());

        // traceId 不为空 查询ai审核原因
        if (CollectionUtil.isNotEmpty(traceIdList)) {
            List<AiTaskResultNew> aiResultList = humanResultMapper.getAiResultList(traceIdList);
            humanResultList.forEach(e -> {
                if (CollectionUtil.isNotEmpty(aiResultList)) {
                    Map<String, String> collect = aiResultList.stream().collect(Collectors.toMap(AiTaskResultNew::getTraceId, AiTaskResultNew::getAiExplain, (v1, v2) -> v1));
                    e.setAiExplain(ObjectUtil.isEmpty(collect.get(e.getTraceId())) ? "" : collect.get(e.getTraceId()));
                }
            });
        }

        // taskTypeList 不为空 查询规则名称
        if (CollectionUtil.isNotEmpty(taskTypeList)) {
            List<TaskRuleVO> taskRuleVOList = humanResultMapper.getTaskRuleName(taskTypeList);
            humanResultList.forEach(e -> {
                if (CollectionUtil.isNotEmpty(taskRuleVOList)) {
                    Map<String, String> taskNameMap = taskRuleVOList.stream().collect(Collectors.toMap(TaskRuleVO::getTaskType, TaskRuleVO::getTaskName, (v1, v2) -> v1));
                    e.setTaskName(ObjectUtil.isEmpty(taskNameMap.get(e.getTaskType())) ? "" : taskNameMap.get(e.getTaskType()));
                }
            });
        }

        PageInfo<HumanResultVo> humanResultVoPageInfo = new PageInfo<>(humanResultList);

        return PageResult.<HumanResultVo>builder()
                .data(humanResultVoPageInfo.getList())
                .totalNum(humanResultVoPageInfo.getTotal())
                .hasNextPage(humanResultVoPageInfo.isHasNextPage())
                .currentPage(humanResultVoPageInfo.getPageNum())
                .pageSize(humanResultVoPageInfo.getPageSize())
                .build();
    }
}
