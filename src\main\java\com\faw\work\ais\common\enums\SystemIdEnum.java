package com.faw.work.ais.common.enums;

/**
 * @Auther:         hp
 * 0-驳回；1-通过；单次
 * @Description:
 */

public enum SystemIdEnum {
    YKYY("YKYY","要客运营"),
    BNZX("BNZX","补能中心"),
    YSZC_ESC("YSZC-ESC","衍生支持-二手车"),

    ;


    private final String code;
    private final String message;


    SystemIdEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    private String code() {
        return this.code;
    }

    private String message() {
        return this.message;
    }



    /**
     * 根据key获取value
     *
     * @param code
     * @return
     */
    public static String getValue(String code) {
        SystemIdEnum[] LevelingStatusEnums = values();
        for (SystemIdEnum LevelingStatusEnum : LevelingStatusEnums) {
            if (LevelingStatusEnum.code().equals(code)) {
                return LevelingStatusEnum.message();
            }
        }
        return null;
    }



    /**
     * 根据value获取key
     *
     * @param message
     * @return
     */
    public static String getCode(String message) {
        SystemIdEnum[] LevelingStatusEnums = values();
        for (SystemIdEnum LevelingStatusEnum : LevelingStatusEnums) {
            if (LevelingStatusEnum.message().equals(message)) {
                return LevelingStatusEnum.code();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
