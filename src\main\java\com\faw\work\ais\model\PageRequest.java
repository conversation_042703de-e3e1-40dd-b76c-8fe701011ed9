package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "分页请求参数")
public class PageRequest<T> {
    
    @Schema(description = "当前页码", defaultValue = "1")
    private Integer pageNum = 1;
    
    @Schema(description = "每页记录数", defaultValue = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "查询条件")
    private T condition;
} 