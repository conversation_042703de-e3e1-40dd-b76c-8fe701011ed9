package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 重试失败消息队列响应
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "重试失败消息队列响应")
public class RetryFailedMessagesResponse {
    
    @Schema(description = "总共找到的失败消息数量", example = "10")
    private Integer totalFailedMessages;

    @Schema(description = "成功重试的消息数量", example = "8")
    private Integer successRetryCount;

    @Schema(description = "重试失败的消息数量", example = "1")
    private Integer failedRetryCount;

    @Schema(description = "跳过的消息数量（已完成状态）", example = "1")
    private Integer skippedCount;

    @Schema(description = "清理的数据数量", example = "8")
    private Integer cleanedCount;

    @Schema(description = "成功重试的请求ID列表")
    private List<String> successRequestIds;

    @Schema(description = "重试失败的请求ID列表")
    private List<String> failedRequestIds;
}
