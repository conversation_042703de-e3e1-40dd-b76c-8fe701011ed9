package com.faw.work.ais.common.enums;

import lombok.Getter;

/**
 * 文档解析状态枚举
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Getter
public enum RagDocumentParseStatusEnum {
    /**
     * 未解析
     */
    UN_PARSING("00", "未解析"),
    /**
     * 解析中
     */
    PARSING("01", "解析中"),
    /**
     * 解析完成
     */
    PARSED("02", "解析完成"),

    /**
     * 向量化完成
     */
    VECTOR_SUCCESS("03", "向量化成功"),

    /**
     * 向量化失败
     */
    VECTOR_FAILED("11", "向量化失败"),
    /**
     * 解析失败
     */
    FAIL("10", "解析失败");



    RagDocumentParseStatusEnum(String code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final String code;

    private final String msg;

}

