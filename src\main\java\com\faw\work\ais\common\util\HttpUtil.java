package com.faw.work.ais.common.util;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.common.exception.BizException;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.Objects;

/**
 * http工具类
 *
 * <AUTHOR>
 * @date 2022-08-24 10:11
 */
@Slf4j
@Component
public class HttpUtil {

    @Setter
    private static RequestConfig requestConfig;
    private static HttpClientBuilder httpClientBuilder;

    private static String getFormParam(Map<String, String> map) {
        StringBuilder sb = new StringBuilder();
        map.forEach((k, v) -> sb.append(k).append("=").append(v).append("&"));
        return sb.substring(0, sb.length() - 1);
    }

    /**
     * 忽略SSL安全认证(国补系统的图片因为证书不全无法直接读取)
     * @return
     */
    public static CloseableHttpClient createSSLClientDefault() {
        try {
            //使用 loadTrustMaterial() 方法实现一个信任策略，信任所有证书
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                // 信任所有
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            //NoopHostnameVerifier类:  作为主机名验证工具，实质上关闭了主机名验证，它接受任何
            //有效的SSL会话并匹配到目标主机。
            HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        }
        return HttpClients.createDefault();
    }

    /**
     * GET请求
     *
     * @param url      请求地址
     * @param paramMap 参数列表
     * @return 响应json字符串
     */
    public static HttpEntity sendGet(String url, Map<String, String> paramMap, Map<String, String> headers){
        // 创建SSLContext
        CloseableHttpResponse response = null;
        try {
//            SSLSocketFactory ssf = new SSLSocketFactory(ctx, SSLSocketFactory);
//            SSLContext sslContext = new SSLContextBuilder()
//                    .useProtocol("TLSv1.2") // 指定SSL/TLS版本
//                    .build();
//            // 创建SSLConnectionSocketFactory
//            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext);
//            // 使用CustomSSLConnectionSocketFactory创建CloseableHttpClient
//            CloseableHttpClient httpClient = HttpClients.custom()
//                    .setSSLSocketFactory(sslSocketFactory)
//                    .build();
//            CloseableHttpClient client = HttpClients.createDefault();
            CloseableHttpClient client = createSSLClientDefault();
            RequestBuilder requestBuilder = RequestBuilder.get(url);
            if (headers!= null && !headers.isEmpty()) {
                headers.forEach(requestBuilder::addHeader);
            }
            if (paramMap != null && !paramMap.isEmpty()) {
                paramMap.forEach(requestBuilder::addParameter);
            }
            HttpUriRequest get = requestBuilder
                    .setConfig(requestConfig)
                    .build();

            log.info("sendGet.url: {}", get.getRequestLine());
            response = client.execute(get);
            String statusCode = String.valueOf(response.getStatusLine().getStatusCode());
            if("200".equals(statusCode)){
                return response.getEntity();
            }else {
                throw new RuntimeException("---请求图片地址异常!-----状态码--" + statusCode);
            }
        } catch (IOException e) {
            log.error("---请求图片地址异常--" + e.getCause() + e.getMessage());
            throw new RuntimeException("请求图片地址异常!");
        }
//        return null;
    }

    /**
     * POST请求
     *
     * @param url    请求地址
     * @param params 参数集合
     * @return 响应json字符串
     */
    public static String sendPost(String url, Map<String, String> headerMap, Map<String, String> params, ContentType contentType) {

        CloseableHttpClient client = HttpClients.createDefault();

        HttpUriRequest post;
        RequestBuilder requestBuilder = RequestBuilder.post(url).setConfig(requestConfig);

        headerMap.forEach((k, v) -> requestBuilder.addHeader(k, v));

        if (ContentType.APPLICATION_JSON == contentType) {

            requestBuilder.setEntity(new StringEntity(JSON.toJSONString(params), contentType));
            post = requestBuilder.build();

        } else if (ContentType.APPLICATION_FORM_URLENCODED == contentType) {

            requestBuilder.setEntity(new StringEntity(getFormParam(params), "UTF-8"));

            requestBuilder.addHeader("Accept", "*/*");

            post = requestBuilder.build();
        } else {

            throw new BizException("不支持的content类型");

        }

        try {

            HttpResponse response = client.execute(post);

            return EntityUtils.toString(response.getEntity());

        } catch (IOException e) {
            e.printStackTrace();
        }

        return "";
    }

    public static String sendPost(String url, String bodyJson, Map<String, String> headers) {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpUriRequest post = RequestBuilder.post(url).setEntity(new StringEntity(bodyJson, ContentType.APPLICATION_JSON)).setConfig(requestConfig).build();
        if (Objects.nonNull(headers) && !headers.isEmpty()) {
            headers.forEach(post::addHeader);
        }

        HttpResponse response = null;

        try {
            // log.info("sendPost.url:{}", post.getRequestLine());
            response = client.execute(post);
            String var6 = EntityUtils.toString(response.getEntity());
            return var6;
        } catch (IOException var10) {
            var10.printStackTrace();
        } finally {
            HttpClientUtils.closeQuietly(response);
        }

        return "";
    }
}

