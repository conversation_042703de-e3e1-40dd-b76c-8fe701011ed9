package com.faw.work.ais.aic.common.enums;

import lombok.Getter;

/**
 * 文档解析状态枚举
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Getter
public enum RagDocumentParseStatusEnum {
    /**
     * 未解析
     */
    UN_PARSING("00", "未解析"),
    /**
     * 解析中
     */
    PARSING("01", "解析中"),
    /**
     * 解析完成
     */
    PARSED("02", "解析完成"),
    /**
     * 解析失败
     */
    FAIL("10", "解析失败");

    RagDocumentParseStatusEnum(String code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final String code;

    private final String msg;

}

