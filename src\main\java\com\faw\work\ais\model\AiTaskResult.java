package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ai任务结果记录表
 * @TableName ai_task_result
 */
@Schema(description = "ai任务结果记录表")
@TableName(value ="ai_task_result")
@Data
public class AiTaskResult implements Serializable {
    /**
     * 雪花算法主键
     */
    @Schema(description = "雪花算法主键")
    @TableId(type=IdType.ASSIGN_ID)
    private String id;

    /**
     * 文件原始url列表
     */
    @Schema(description = "文件原始url列表")
    private String fileRawList;

    /**
     * COS文件id
     */
    @Schema(description = "COS文件id")
    private String fileId;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private String taskType;

    /**
     * 任务结果
     */
    @Schema(description = "任务结果")
    private String taskResult;

    /**
     * 文件类型0表示文档1表示图片(多个文件，逗号分隔)
     */
    @Schema(description = "文件类型0表示文档1表示图片(多个文件，逗号分隔)")
    private String contentType;

    /**
     * 文档版本号
     */
    @Schema(description = "文档版本号")
    private String version;

    /**
     * 系统id
     */
    @Schema(description = "系统id")
    private String systemId;

    /**
     * 任务的状态0已收到，1AI已处理，2表示未回调，3表示已回调
     */
    @Schema(description = "任务的状态0已收到，1AI已处理，2表示未回调，3表示已回调")
    private Integer taskStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 业务主键
     */
    @Schema(description = "业务主键")
    private String bizId;

    /**
     * 业务主键
     */
    @Schema(description = "业务主键")
    private String bizType;

    /**
     * 提示词
     */
    @Schema(description = "提示词")
    private String rawMessage;

    /**
     * 响应结果；success-成功；false-失败
     */
    @Schema(description = "响应结果；success-成功；false-失败")
    private String success;

    /**
     * (value = "校的字段和内容{\"idcard\":\"123456456\",\"vin\":\"220122455577778884\",\"userType\":\"A类\"}")
     * 如果超过500字，存到cos桶中，此处只存桶的位置
     */
    @Schema(description = "校的字段和内容{\\\"idcard\\\":\\\"123456456\\\",\\\"vin\\\":\\\"220122455577778884\\\",\\\"userType\\\":\\\"A类\\\"}\"")
    private String givenInfoJson;

    @Schema(description = "每次请求的唯一id")
    private String traceId;

    @Schema(description = "请求的批次id")
    private String batchId;

    @Schema(description = "AI审核结果；true-审核通过；false-审核不通过或其他 异常")
    private String aiResult;

    @Schema(description = "AI审核说明")
    private String aiExplain;

    @Schema(description = "ai自定义字段")
    private String customField;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AiTaskResult other = (AiTaskResult) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFileRawList() == null ? other.getFileRawList() == null : this.getFileRawList().equals(other.getFileRawList()))
            && (this.getFileId() == null ? other.getFileId() == null : this.getFileId().equals(other.getFileId()))
            && (this.getTaskType() == null ? other.getTaskType() == null : this.getTaskType().equals(other.getTaskType()))
            && (this.getTaskResult() == null ? other.getTaskResult() == null : this.getTaskResult().equals(other.getTaskResult()))
            && (this.getContentType() == null ? other.getContentType() == null : this.getContentType().equals(other.getContentType()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getSystemId() == null ? other.getSystemId() == null : this.getSystemId().equals(other.getSystemId()))
            && (this.getTaskStatus() == null ? other.getTaskStatus() == null : this.getTaskStatus().equals(other.getTaskStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFileRawList() == null) ? 0 : getFileRawList().hashCode());
        result = prime * result + ((getFileId() == null) ? 0 : getFileId().hashCode());
        result = prime * result + ((getTaskType() == null) ? 0 : getTaskType().hashCode());
        result = prime * result + ((getTaskResult() == null) ? 0 : getTaskResult().hashCode());
        result = prime * result + ((getContentType() == null) ? 0 : getContentType().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getSystemId() == null) ? 0 : getSystemId().hashCode());
        result = prime * result + ((getTaskStatus() == null) ? 0 : getTaskStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", fileRawList=").append(fileRawList);
        sb.append(", fileId=").append(fileId);
        sb.append(", taskType=").append(taskType);
        sb.append(", taskResult=").append(taskResult);
        sb.append(", contentType=").append(contentType);
        sb.append(", version=").append(version);
        sb.append(", systemId=").append(systemId);
        sb.append(", taskStatus=").append(taskStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}