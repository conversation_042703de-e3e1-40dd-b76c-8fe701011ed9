package com.faw.work.ais.mapper.content;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.entity.domain.ContentRulePO;
import jakarta.validation.constraints.NotBlank;
import org.apache.ibatis.annotations.Mapper;

/**
 * 评论规则Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ContentRuleMapper extends BaseMapper<ContentRulePO> {

    void disableOtherRules(@NotBlank(message = "主键不能为空") Long id, @NotBlank(message = "类型不能为空") Integer type);
} 