package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FAQ相似问请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ相似问请求对象")
public class FaqSimilarKnowledgeRequest {
    
    @Schema(description = "主键ID")
    private String id;
    
    @Schema(description = "原问题")
    private String question;
    
    @NotBlank(message = "相似问不能为空")
    @Schema(description = "相似问")
    private String similarQuestion;
    
    @Schema(description = "答案")
    private String answer;
    
    @Schema(description = "类目ID")
    private String categoryId;
    
    @Schema(description = "类目名称")
    private String categoryName;
    
    @NotNull(message = "原问题ID不能为空")
    @Schema(description = "关联的原问题ID")
    private String knowledgeId;
    
    @Schema(description = "创建人")
    private String createdBy;
} 