package com.faw.work.ais.service.impl;

import com.alibaba.dashscope.aigc.multimodalconversation.AudioParameters;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.fastjson.JSON;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.PromptConstants;
import com.faw.work.ais.common.dto.chat.AiTestRequest;
import com.faw.work.ais.common.enums.ErrorCodeEnum;
import com.faw.work.ais.service.AiChatService;
import com.faw.work.ais.service.AiTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;
import java.util.UUID;

/**
 * AI测试 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-19 8:48
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiTestServiceImpl implements AiTestService {

    private final ChatClient universalClient;

    private final AiChatService aiChatService;


    @Value("${spring.ai.tts.model:}")
    private String ttsModel;

    @Value("${spring.ai.dashscope.api-key:}")
    private String apiKey;


    @Override
    public String verifyQuestionAnswer(AiTestRequest request) {
        log.info("[AiTestService][verifyQuestionAnswer][entrance] request: {}", JSON.toJSONString(request));
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getQuestion())
                || StringUtils.isEmpty(request.getAnswer()) || StringUtils.isEmpty(request.getAiAnswer())) {
            return "否，请求参数存在空值";
        }

        String verifyResult = universalClient
                .prompt(String.format(PromptConstants.VERIFY_INIT_PROMPT, request.getQuestion(), request.getAnswer(), request.getAiAnswer()))
                .call().content();

        log.info("[AiTestService][verifyQuestionAnswer][exit] verifyResult: {}", verifyResult);
        return verifyResult;
    }

    @Override
    public String textToVoice(AiTestRequest request) {
        log.info("[AiTestService][textToVoice][entrance] request: {}", JSON.toJSONString(request));
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getQuestion())) {
            return ErrorCodeEnum.REQUEST_PARAM_NULL.getDesc();
        }

        try {
            MultiModalConversation conv = new MultiModalConversation();
            MultiModalConversationParam param = MultiModalConversationParam.builder()
                    .model(ttsModel).apiKey(apiKey)
                    .text(request.getQuestion())
                    .voice(AudioParameters.Voice.CHERRY)
                    .build();
            MultiModalConversationResult result = conv.call(param);

            return result.getOutput().getAudio().getUrl();
        } catch (Exception e) {
            log.warn("[AiTestService][textToVoice] error: ", e);
        }

        return "";
    }

    @Override
    public String testAsr(AiTestRequest request) {
        log.info("[AiTestService][testAsr][entrance] request: {}", JSON.toJSONString(request));
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getNetworkUrl())) {
            return ErrorCodeEnum.REQUEST_PARAM_NULL.getDesc();
        }

        String result;
        String fileName = UUID.randomUUID() + CommonConstants.FILE_SUFFIX_VOICE;
        try {
            boolean downloadFlag = this.downloadFile(request.getNetworkUrl(), fileName);
            if (downloadFlag) {
                MultipartFile file = this.getFile(fileName);
                if (file != null) {
                    result = aiChatService.voiceToTextRectify(file);
                } else {
                    result = "文件读取失败";
                }
            } else {
                result = "文件下载失败";
            }
        } catch (Exception e) {
            log.warn("[AiTestService][testAsr] error: ", e);
            result = "文件下载失败";
        } finally {
            File file = new File(fileName);
            if (file.exists()) {
                boolean deleteFlag = file.delete();
                log.info("[AiTestService][testAsr] deleteFlag: {}", deleteFlag);
            }
        }

        return result;
    }

    /**
     * 下载文件
     *
     * @param networkUrl 网络地址
     * @param filePath   文件路径
     * @return boolean
     */
    private boolean downloadFile(String networkUrl, String filePath) {
        try (FileOutputStream fs = new FileOutputStream(filePath)) {
            URL url = new URL(networkUrl);
            URLConnection conn = url.openConnection();
            InputStream inStream = conn.getInputStream();

            int byteread;
            byte[] buffer = new byte[1204];
            while ((byteread = inStream.read(buffer)) != -1) {
                fs.write(buffer, 0, byteread);
            }
        } catch (Exception e) {
            log.warn("testFileDownload failed: ", e);
            return false;
        }

        return true;
    }

    /**
     * 获取文件
     *
     * @param filePath 文件路径
     * @return MultipartFile
     */
    private MultipartFile getFile(String filePath) {
        Path path = Paths.get(filePath);
        String fileName = path.getFileName().toString();

        String contentType;
        byte[] content;

        try (FileInputStream fis = new FileInputStream(filePath)) {
            contentType = Files.probeContentType(path);
            content = fis.readAllBytes();
            return new MockMultipartFile(fileName, fileName, contentType, content);
        } catch (IOException e) {
            return null;
        }
    }

}
