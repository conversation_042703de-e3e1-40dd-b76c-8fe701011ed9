package com.faw.work.ais.aic.common.mq;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ 配置类
 * 配置核心组件：队列、交换机、绑定关系、死信机制及消息监听容器
 * <AUTHOR>
 */
@Configuration
public class RabbitMqConfig {

    /**
     * 主队列名称，默认值：message-processing-queue
     */
    @Value("${rabbitmq.queue.name:message-processing-queue}")
    private String queueName;

    /**
     * 主交换机名称，默认值：message-processing-exchange
     */
    @Value("${rabbitmq.exchange.name:message-processing-exchange}")
    private String exchangeName;

    /**
     * 主路由键，默认值：message-processing
     */
    @Value("${rabbitmq.routing.key:message-processing}")
    private String routingKey;


    /**
     * 死信交换机名称，默认值：message-processing-dlx
     */
    @Value("${rabbitmq.dlx.exchange:message-processing-dlx}")
    private String dlxExchange;

    /**
     * 死信队列名称，默认值：message-processing-dlq
     */
    @Value("${rabbitmq.dlx.queue:message-processing-dlq}")
    private String dlxQueue;

    /**
     * 死信路由键，默认值：message-processing-dlq
     */
    @Value("${rabbitmq.dlx.routing.key:message-processing-dlq}")
    private String dlxRoutingKey;


    /**
     * 创建主队列Bean（持久化）
     * @return 配置好的队列实例
     */
    @Bean
    public Queue messageQueue() {
        return QueueBuilder.durable(queueName)
                .withArgument("x-dead-letter-exchange", dlxExchange)
                .withArgument("x-dead-letter-routing-key", dlxRoutingKey)
                .withArgument("x-message-ttl", 600000)
                .build();
    }

    /**
     * 创建主交换机Bean（直连类型，持久化）
     * @return 配置好的交换机实例
     */
    @Bean
    public DirectExchange messageExchange() {
        return new DirectExchange(exchangeName, true, false);
    }

    /**
     * 创建主队列与主交换机的绑定关系Bean
     * @return 绑定关系实例
     */
    @Bean
    public Binding messageBinding() {
        return BindingBuilder.bind(messageQueue())
                .to(messageExchange())
                .with(routingKey);
    }

    /**
     * 创建死信交换机Bean（直连类型，持久化）
     * @return 配置好的死信交换机实例
     */
    @Bean
    public DirectExchange dlxExchange() {
        return new DirectExchange(dlxExchange, true, false);
    }

    /**
     * 创建死信队列Bean（持久化）
     * @return 配置好的死信队列实例
     */
    @Bean
    public Queue dlxQueue() {
        return QueueBuilder.durable(dlxQueue).build();
    }

    /**
     * 创建死信队列与死信交换机的绑定关系Bean
     * @return 绑定关系实例
     */
    @Bean
    public Binding dlxBinding() {
        return BindingBuilder.bind(dlxQueue())
                .to(dlxExchange())
                .with(dlxRoutingKey);
    }

}
