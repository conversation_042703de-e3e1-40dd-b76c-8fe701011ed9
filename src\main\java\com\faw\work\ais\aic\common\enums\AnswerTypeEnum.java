package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 答案类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AnswerTypeEnum {
    /**
     * 纯文本
     */
    PLAIN_TEXT("00", "纯文本"),
    /**
     * 富文本
     */
    RICH_TEXT("01", "富文本");

    private final String code;
    private final String desc;

    public static AnswerTypeEnum getByCode(String code) {
        for (AnswerTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return PLAIN_TEXT;
    }
} 