package com.faw.work.ais.util;
import java.util.Collection;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 字符串工具类，提供常用的字符串操作。
 * 该类不能被实例化。
 */
public final class StringUtils {

    /**
     * 私有构造函数，防止类被实例化。
     */
    private StringUtils() {
        // 私有构造函数，阻止实例化
    }

    /**
     * 检查字符串是否为 null 或空（包括只包含空格）。
     *
     * @param s 要检查的字符串。
     * @return 如果字符串为 null 或空，则返回 true；否则返回 false。
     */
    public static boolean isNullOrEmpty(String s) {
        return s == null || s.trim().isEmpty();
    }

    /**
     * 使用指定的分隔符将集合中的所有元素连接成一个字符串。
     *
     * @param collection 要连接的元素集合。
     * @param delimiter  用于分隔元素的字符串。
     * @return 包含所有连接元素的单个字符串。
     */
    public static <T> String join(Collection<T> collection, String delimiter) {
        if (collection == null || collection.isEmpty()) {
            return "";
        }
        Objects.requireNonNull(delimiter, "分隔符不能为 null。");
        return collection.stream()
                .map(Objects::toString)
                .collect(Collectors.joining(delimiter));
    }

    /**
     * 将字符串的首字母大写。
     *
     * @param s 要处理的字符串。
     * @return 首字母大写后的字符串；如果输入为 null 或空，则返回原字符串。
     */
    public static String capitalize(String s) {
        if (isNullOrEmpty(s)) {
            return s;
        }
        return s.substring(0, 1).toUpperCase() + s.substring(1);
    }
}