package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "知识详情响应，包含知识本身和其相似问列表")
public class FaqKnowledgeDetailResponse {

    @Schema(description = "知识详情")
    private FaqKnowledgePO knowledge;

    @Schema(description = "相似问列表")
    private List<FaqSimilarKnowledgePO> similarKnowledges;
} 