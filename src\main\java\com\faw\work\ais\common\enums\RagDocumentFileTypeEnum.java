package com.faw.work.ais.common.enums;

import lombok.Getter;

/**
 * 文档类型枚举
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Getter
public enum RagDocumentFileTypeEnum {

    /**
     * PDF 文档
     */
    PDF("pdf", "PDF文档"),
    /**
     * DOC 文档
     */
    DOC("doc", "DOC文档"),
    /**
     * DOCX 文档
     */
    DOCX("docx", "DOCX文档"),
    /**
     * TXT 文档
     */
    TXT("txt", "TXT文档"),
    /**
     * CSV 文档
     */
    CSV("csv", "CSV文档");

    RagDocumentFileTypeEnum(String code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final String code;

    private final String msg;

}
