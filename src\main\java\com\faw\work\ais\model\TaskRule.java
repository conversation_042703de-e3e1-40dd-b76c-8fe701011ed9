package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ai任务算法表
 */
@Data
@Schema(description = "任务规则表")
@Accessors(chain = true)
public class TaskRule {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "规则名称")
    private String taskName;

    @Schema(description = "业务单元编码")
    private String bizType;

    @Schema(description = "每条规则AI节约的时间（单位分）")
    private String saveTime;

    @Schema(description = "任务类型；业务自定义")
    private String taskType;

    @Schema(description = "提示词")
    private String prompt;

    @Schema(description = "规则描述")
    private String desc;

    @Schema(description = "系统编号")
    private String systemId;
    @Schema(description = "是否满足上线标准")
    private Integer isReadyForDeployment;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;
}