package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.FaqHitLogPO;
import com.faw.work.ais.aic.model.response.FaqHitLogCleanResponse;

import java.time.LocalDateTime;
import java.util.List;

/**
 * FAQ命中日志Service接口
 *
 * <AUTHOR>
 */
public interface FaqHitLogService {

    /**
     * 清理一个月前的FAQ命中日志数据
     *
     * @return 清理结果
     */
    FaqHitLogCleanResponse cleanOldHitLogs();

    /**
     * 根据条件查询FAQ命中日志数据
     *
     * @param robotId 机器人ID
     * @param dataSource 数据来源
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 命中日志列表
     */
    List<FaqHitLogPO> findHitLogsByCondition(String robotId, String dataSource,
                                             LocalDateTime startTime, LocalDateTime endTime);
}
