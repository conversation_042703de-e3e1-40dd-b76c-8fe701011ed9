package com.faw.work.ais.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024-04-08
 * @description
 */
@Configuration
@ConfigurationProperties(prefix = "snowflake")
public class SnowflakeProperties {
    private long workerId = 1;
    private long datacenterId = 1;

    // getter 和 setter 方法
    public long getWorkerId() {
        return workerId;
    }

    public void setWorkerId(long workerId) {
        this.workerId = workerId;
    }

    public long getDatacenterId() {
        return datacenterId;
    }

    public void setDatacenterId(long datacenterId) {
        this.datacenterId = datacenterId;
    }
}
