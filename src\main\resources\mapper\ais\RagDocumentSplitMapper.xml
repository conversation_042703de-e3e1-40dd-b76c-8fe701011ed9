<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.rag.RagDocumentSplitMapper">


    <update id="updateHitCount">
        update rag_document_split
        set hit_count = hit_count + 1
        where id = #{id}
    </update>



    <select id="selectAppSearchConfig" resultType="com.faw.work.ais.entity.dto.AppSearchConfigDTO">
        select
        *
        from app_search_config
        where id = #{appId}
    </select>
    <select id="selectByDocumentId" resultType="com.faw.work.ais.aic.model.domain.RagDocumentSplitPO">
        select
            *
        from rag_document_split
        where document_id = #{documentId}
    </select>

    <!-- 根据文档ID删除片段 -->
    <delete id="deleteByDocumentId" parameterType="string">
        delete from rag_document_split
        where document_id = #{documentId}
    </delete>

</mapper> 