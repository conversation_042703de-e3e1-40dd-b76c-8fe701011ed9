package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 养狗话术知识入库请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "养狗话术知识入库请求对象")
public class FaqDogKnowledgeInsertRequest {

    @NotBlank(message = "机器人ID不能为空")
    @Schema(description = "机器人ID")
    private String robotId;


    @NotEmpty(message = "知识列表不能为空")
    @Valid
    @Schema(description = "知识列表")
    private List<KnowledgeItem> knowledgeList;

    /**
     * 知识项内部类
     */
    @Data
    @Schema(description = "知识项")
    public static class KnowledgeItem {
        @NotBlank(message = "FAQ题目不能为空")
        @Schema(description = "FAQ题目")
        private String question;

        @NotBlank(message = "答案不能为空")
        @Schema(description = "答案")
        private String answer;

        @NotNull(message = "知识版本号不能为空")
        @Schema(description =  "知识版本号")
        private Integer knowledgeVersion;
    }
}
