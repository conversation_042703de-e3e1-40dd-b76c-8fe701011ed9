package com.faw.work.ais.mapper.chat;

import com.faw.work.ais.model.chat.AppChatHistoryDetail;

import java.util.List;

/**
 * 智能问答聊天记录详情 Mapper
 *
 * <AUTHOR>
 * @since 2025-04-27 10:06
 */
public interface AppChatHistoryDetailMapper {

    /**
     * 批量新增智能问答聊天记录详情
     *
     * @param list 智能问答聊天记录详情列表
     * @return 影响行数
     */
    int insertList(List<AppChatHistoryDetail> list);

    /**
     * 根据智能问答聊天记录ID删除智能问答聊天记录详情
     *
     * @param chartId 智能问答聊天记录ID
     * @return 影响行数
     */
    int deleteByChatId(Long chartId);

    /**
     * 查询智能问答聊天记录详情列表
     *
     * @param po 查询条件
     * @return 智能问答聊天记录详情列表
     */
    List<AppChatHistoryDetail> selectList(AppChatHistoryDetail po);

}
