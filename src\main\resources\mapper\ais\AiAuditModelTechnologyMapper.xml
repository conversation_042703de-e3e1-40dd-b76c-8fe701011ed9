<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiAuditModelTechnologyDao">

    <resultMap id="AiAuditModelTechnology" type="com.faw.work.ais.model.AiAuditModelTechnology" >
        <result column="id" property="id" />
        <result column="audit_id" property="auditId" />
        <result column="technology_model_code" property="technologyModelCode" />
        <result column="technology_model_name" property="technologyModelName" />
        <result column="audit_point_name" property="auditPointName" />
        <result column="create_time" property="createTime" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_code" property="updateUserCode" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `audit_id`,
        `technology_model_code`,
        `technology_model_name`,
        `audit_point_name`,
        `create_time`,
        `create_user_code`,
        `create_user_name`,
        `update_time`,
        `update_user_code`,
        `update_user_name`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_audit_model_technology (
            `audit_id`,
            `technology_model_code`,
            `technology_model_name`,
            `audit_point_name`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES(
            #{aiAuditModelTechnology.auditId},
            #{aiAuditModelTechnology.technologyModelCode},
            #{aiAuditModelTechnology.technologyModelName},
            #{aiAuditModelTechnology.auditPointName},
            #{aiAuditModelTechnology.createUserCode},
            #{aiAuditModelTechnology.createUserName},
            #{aiAuditModelTechnology.updateUserCode},
            #{aiAuditModelTechnology.updateUserName}
        )
    </insert>
    <insert id="insertBatch"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_audit_model_technology (
            `audit_id`,
            `technology_model_code`,
            `technology_model_name`,
            `audit_point_name`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES
        <foreach collection="aiAuditModelTechnologys" item="aiAuditModelTechnology" separator=",">
        (
                  #{aiAuditModelTechnology.auditId},
                  #{aiAuditModelTechnology.technologyModelCode},
                  #{aiAuditModelTechnology.technologyModelName},
                  #{aiAuditModelTechnology.auditPointName},
                  #{aiAuditModelTechnology.createUserCode},
                  #{aiAuditModelTechnology.createUserName},
                  #{aiAuditModelTechnology.updateUserCode},
                  #{aiAuditModelTechnology.updateUserName}
              )
        </foreach>
    </insert>


    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM ai_audit_model_technology
        where  audit_id = #{id}

    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE ai_audit_model_technology
         <set>
            <if test="aiAuditModelTechnology.auditId != null and aiAuditModelTechnology.auditId != '' " >
                audit_id = #{aiAuditModelTechnology.auditId},
            </if>
            <if test="aiAuditModelTechnology.technologyModelCode != null and aiAuditModelTechnology.technologyModelCode != '' " >
                technology_model_code = #{aiAuditModelTechnology.technologyModelCode},
            </if>
            <if test="aiAuditModelTechnology.technologyModelName != null and aiAuditModelTechnology.technologyModelName != '' " >
                technology_model_name = #{aiAuditModelTechnology.technologyModelName},
            </if>
            <if test="aiAuditModelTechnology.auditPointName != null and aiAuditModelTechnology.auditPointName != '' " >
                audit_point_name = #{aiAuditModelTechnology.auditPointName},
            </if>
            <if test="aiAuditModelTechnology.createTime != null and aiAuditModelTechnology.createTime != '' " >
                create_time = #{aiAuditModelTechnology.createTime},
            </if>
            <if test="aiAuditModelTechnology.createUserCode != null and aiAuditModelTechnology.createUserCode != '' " >
                create_user_code = #{aiAuditModelTechnology.createUserCode},
            </if>
            <if test="aiAuditModelTechnology.createUserName != null and aiAuditModelTechnology.createUserName != '' " >
                create_user_name = #{aiAuditModelTechnology.createUserName},
            </if>
            <if test="aiAuditModelTechnology.updateTime != null and aiAuditModelTechnology.updateTime != '' " >
                update_time = #{aiAuditModelTechnology.updateTime},
            </if>
            <if test="aiAuditModelTechnology.updateUserCode != null and aiAuditModelTechnology.updateUserCode != '' " >
                update_user_code = #{aiAuditModelTechnology.updateUserCode},
            </if>
            <if test="aiAuditModelTechnology.updateUserName != null and aiAuditModelTechnology.updateUserName != '' " >
                update_user_name = #{aiAuditModelTechnology.updateUserName},
            </if>
         </set>
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </update>


    <select id="getAiAuditModelTechnologyList" resultMap="AiAuditModelTechnology">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_audit_model_technology
        <where>
          <if test="aiAuditModelTechnology.id != null" >
              AND  id = #{aiAuditModelTechnology.id}
          </if>
          <if test="aiAuditModelTechnology.auditId != null  " >
              AND  audit_id = #{aiAuditModelTechnology.auditId}
          </if>
          <if test="aiAuditModelTechnology.technologyModelCode != null and aiAuditModelTechnology.technologyModelCode != '' " >
              AND  technology_model_code = #{aiAuditModelTechnology.technologyModelCode}
          </if>
          <if test="aiAuditModelTechnology.technologyModelName != null and aiAuditModelTechnology.technologyModelName != '' " >
              AND  technology_model_name = #{aiAuditModelTechnology.technologyModelName}
          </if>
          <if test="aiAuditModelTechnology.auditPointName != null and aiAuditModelTechnology.auditPointName != '' " >
              AND  audit_point_name = #{aiAuditModelTechnology.auditPointName}
          </if>
          <if test="aiAuditModelTechnology.createTime != null and aiAuditModelTechnology.createTime != '' " >
              AND  create_time = #{aiAuditModelTechnology.createTime}
          </if>
          <if test="aiAuditModelTechnology.createUserCode != null and aiAuditModelTechnology.createUserCode != '' " >
              AND  create_user_code = #{aiAuditModelTechnology.createUserCode}
          </if>
          <if test="aiAuditModelTechnology.createUserName != null and aiAuditModelTechnology.createUserName != '' " >
              AND  create_user_name = #{aiAuditModelTechnology.createUserName}
          </if>
          <if test="aiAuditModelTechnology.updateTime != null and aiAuditModelTechnology.updateTime != '' " >
              AND  update_time = #{aiAuditModelTechnology.updateTime}
          </if>
          <if test="aiAuditModelTechnology.updateUserCode != null and aiAuditModelTechnology.updateUserCode != '' " >
              AND  update_user_code = #{aiAuditModelTechnology.updateUserCode}
          </if>
          <if test="aiAuditModelTechnology.updateUserName != null and aiAuditModelTechnology.updateUserName != '' " >
              AND  update_user_name = #{aiAuditModelTechnology.updateUserName}
          </if>

        </where>
     </select>

    <select id="getAiAuditModelTechnologyById" parameterType="java.util.Map" resultMap="AiAuditModelTechnology">
        SELECT <include refid="Base_Column_List" />
        FROM ai_audit_model_technology
        where  id = #{id}

    </select>



</mapper>

