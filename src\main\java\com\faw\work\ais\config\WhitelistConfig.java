package com.faw.work.ais.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 白名单配置
 */
@Configuration
@ConfigurationProperties(prefix = "whitelist")
@RefreshScope
@Data
public class WhitelistConfig {

    @Schema(description = "systemIds系统访问权限白名单")
    private List<String> systemIds;

    @Schema(description = "fileUndoChecks文件必填项校验白名单-白名单内文件不需要校验必填项")
    private List<String> fileUndoChecks;

    @Schema(description = "批次Id必填项校验白名单")
    private List<String> batchIdChecks;

}
