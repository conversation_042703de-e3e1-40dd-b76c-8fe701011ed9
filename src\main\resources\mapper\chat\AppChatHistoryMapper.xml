<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.chat.AppChatHistoryMapper">

    <resultMap id="AppChatHistory" type="com.faw.work.ais.model.chat.AppChatHistory">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="chat_title" property="chatTitle" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`, `app_id`, `user_id`, `session_id`, `chat_title`, `del_flag`, `created_at`, `updated_at`
    </sql>

    <insert id="insert" parameterType="com.faw.work.ais.model.chat.AppChatHistory" keyColumn="id" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO app_chat_history (`app_id`, `user_id`, `session_id`, `chat_title`, `del_flag`)
        VALUES (#{appId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR},
                #{chatTitle,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER})
    </insert>

    <update id="update" parameterType="com.faw.work.ais.model.chat.AppChatHistory">
        UPDATE app_chat_history
        <set>
            <if test="delFlag != null and delFlag != '' ">
                `del_flag` = #{delFlag,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectList" parameterType="com.faw.work.ais.model.chat.AppChatHistory" resultMap="AppChatHistory">
        SELECT <include refid="Base_Column_List"/>
        FROM app_chat_history
        <where>
            <if test="appId != null and appId != '' ">
                AND app_id = #{appId,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId != '' ">
                AND user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="sessionId != null and sessionId != '' ">
                AND session_id = #{sessionId,jdbcType=VARCHAR}
            </if>
            <if test="chatTitle != null and chatTitle != '' ">
                AND chat_title like concat('%', #{chatTitle,jdbcType=VARCHAR}, '%')
            </if>
            <if test="delFlag != null ">
                AND del_flag = #{delFlag,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

</mapper>

