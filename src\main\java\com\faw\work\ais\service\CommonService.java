package com.faw.work.ais.service;

import com.faw.work.ais.entity.dto.ai.BizUnitInfoDTO;
import com.faw.work.ais.entity.dto.ai.RePushAiDTO;
import com.faw.work.ais.entity.vo.ai.BizUnitAndTaskRuleInfoVO;
import com.faw.work.ais.entity.vo.ai.BizUnitInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommonService {

    /**
     * 根据单元获取规则信息
     * @param unitCodes
     * @return
     */
    List<BizUnitAndTaskRuleInfoVO> getBizUnitInfos(List<String> unitCodes);

    void rePushAiTask(RePushAiDTO rePushAiDTO);

}
