package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.dto.ai.RePushAiDTO;
import com.faw.work.ais.entity.vo.ai.RePushAiVO;
import com.faw.work.ais.model.LogInfoPythonCallback;
import com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LogInfoPythonCallbackMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(LogInfoPythonCallbackWithBLOBs record);

    int insertSelective(LogInfoPythonCallbackWithBLOBs record);

    LogInfoPythonCallbackWithBLOBs selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(LogInfoPythonCallbackWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(LogInfoPythonCallbackWithBLOBs record);

    int updateByPrimaryKey(LogInfoPythonCallback record);

    int updateByTranceId(LogInfoPythonCallbackWithBLOBs record);

    LogInfoPythonCallbackWithBLOBs selectByTraceId(String traceId);

    LogInfoPythonCallbackWithBLOBs selectByObj(LogInfoPythonCallbackWithBLOBs param);

    /**
     * 根据traceId获取python入参
     * @param fileDTO
     * @return
     */
    String getPythonInParamByTraceId(@Param("fileDTO") FileDTO fileDTO);

    /**
     * 获取没有AI结果或者没有回调的batchId集合
     * @return
     */
    List<RePushAiVO> getUnAiResultBatchIds(@Param("param") RePushAiDTO rePushAiDTO);

}