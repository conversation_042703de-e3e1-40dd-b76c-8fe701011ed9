package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文本分段配置请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "文本分段配置请求")
public class RagDocumentSplitConfigRequest {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "文档id")
    private Long documentId;

    @Schema(description = "分段策略(00-智能切分 01-自定义切分)")
    private String chunkStrategy;

    @Schema(description = "分段标识符(默认双换行符\\n，。！等)")
    private String chunkSeparator;

    @Schema(description = "分段最大长度(tokens)")
    private Integer chunkLength;

    @Schema(description = "分块大小(字符数)")
    private Integer chunkSize;

    @Schema(description = "分段重叠长度(tokens)")
    private Integer overlapLength;
} 