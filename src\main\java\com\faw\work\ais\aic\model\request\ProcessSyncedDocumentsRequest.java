package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 处理同步文档请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "处理同步文档请求")
public class ProcessSyncedDocumentsRequest {

    @NotEmpty(message = "文档类目id不能为空")
    @Schema(description = "文档类目id", required = true)
    private List<Long> categoryIds;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long ragKnowledgeId;
}
