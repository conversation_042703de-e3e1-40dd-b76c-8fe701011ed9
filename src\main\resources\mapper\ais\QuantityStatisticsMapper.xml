<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.QuantityStatisticsDao">

    <resultMap id="QuantityStatistics" type="com.faw.work.ais.model.QuantityStatistics" >
        <result column="id" property="id" />
        <result column="timing" property="timing" />
        <result column="system_id" property="systemId" />
        <result column="data_type" property="dataType" />
        <result column="quantity" property="quantity" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `timing`,
        `system_id`,
        `data_type`,
        `quantity`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO quantity_statistics (
            `timing`,
            `system_id`,
            `data_type`,
            `quantity`
        )
        VALUES(
            #{quantityStatistics.timing},
            #{quantityStatistics.systemId},
            #{quantityStatistics.dataType},
            #{quantityStatistics.quantity}
        )
    </insert>
    <insert id="insertBatch"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO quantity_statistics (
            `timing`,
            `system_id`,
            `data_type`,
            `quantity`
        )
        VALUES
        <foreach collection="quantityStatisticss" item="quantityStatistics" separator=",">
          (
                  #{quantityStatistics.timing},
                  #{quantityStatistics.systemId},
                  #{quantityStatistics.dataType},
                  #{quantityStatistics.quantity}
              )
        </foreach>
    </insert>

    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM quantity_statistics
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </delete>

    <delete id="deleteByIds" parameterType="java.util.Map" >
        DELETE FROM quantity_statistics
        where id  IN (
        <foreach collection="ids" item="code" separator=",">
            #{code}
        </foreach>
        )
    </delete>

    <delete id="deleteByTime" parameterType="java.util.Map" >
        DELETE FROM quantity_statistics
        where timing =  #{timing}
    </delete>


    <update id="update" parameterType="java.util.Map" >
        UPDATE quantity_statistics
         <set>
            <if test="quantityStatistics.timing != null and quantityStatistics.timing != '' " >
                timing = #{quantityStatistics.timing},
            </if>
            <if test="quantityStatistics.systemId != null and quantityStatistics.systemId != '' " >
                system_id = #{quantityStatistics.systemId},
            </if>
            <if test="quantityStatistics.dataType != null and quantityStatistics.dataType != '' " >
                data_type = #{quantityStatistics.dataType},
            </if>
            <if test="quantityStatistics.quantity != null and quantityStatistics.quantity != '' " >
                quantity = #{quantityStatistics.quantity},
            </if>
            <if test="quantityStatistics.createTime != null and quantityStatistics.createTime != '' " >
                create_time = #{quantityStatistics.createTime},
            </if>
            <if test="quantityStatistics.updateTime != null and quantityStatistics.updateTime != '' " >
                update_time = #{quantityStatistics.updateTime},
            </if>
         </set>
        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </update>


    <select id="getQuantityStatisticsList" resultMap="QuantityStatistics">
        SELECT
        <include refid="Base_Column_List" />
        FROM quantity_statistics
        <where>

          <if test="quantityStatistics.timing != null and quantityStatistics.timing != '' " >
              AND  timing = #{quantityStatistics.timing}
          </if>
          <if test="quantityStatistics.systemId != null and quantityStatistics.systemId != '' " >
              AND  system_id = #{quantityStatistics.systemId}
          </if>

        </where>
     </select>

    <select id="getQuantityIdsStatisticsList" resultMap="QuantityStatistics">
        SELECT
        `id`
        FROM quantity_statistics
        <where>

            <if test="quantityStatistics.timing != null and quantityStatistics.timing != '' " >
                AND  timing = #{quantityStatistics.timing}
            </if>
            <if test="quantityStatistics.systemId != null and quantityStatistics.systemId != '' " >
                AND  system_id = #{quantityStatistics.systemId}
            </if>

        </where>
    </select>



    <select id="getQuantityStatisticsById" parameterType="java.util.Map" resultMap="QuantityStatistics">
        SELECT <include refid="Base_Column_List" />
        FROM quantity_statistics

        <where>
             <if test="id != null and id != '' " >
                 AND  id = #{id}
             </if>

        </where>
    </select>

    <select id="getIntervalQuantityStatisticsList" parameterType="java.util.Map" resultMap="QuantityStatistics">
        SELECT <include refid="Base_Column_List" />
        FROM quantity_statistics
        where
            1=1
        <if test="quantityStatistics.startTime != null and quantityStatistics.startTime != '' " >
            and timing >= #{quantityStatistics.startTime}
        </if>
        <if test="quantityStatistics.endTime != null and quantityStatistics.endTime != '' " >
            and timing <![CDATA[<=]]>  #{quantityStatistics.endTime}
        </if>
        <if test="quantityStatistics.systemId != null and quantityStatistics.systemId != '' " >
            AND system_id = #{quantityStatistics.systemId}
        </if>
    </select>

    <select id="getAiCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT ifnull(sum(quantity),0)
        FROM quantity_statistics
        where
        1=1
        <if test="quantityStatistics.startTime != null and quantityStatistics.startTime != '' " >
            and timing >= #{quantityStatistics.startTime}
        </if>
        <if test="quantityStatistics.endTime != null and quantityStatistics.endTime != '' " >
            and timing <![CDATA[<=]]>  #{quantityStatistics.endTime}
        </if>
        <if test="quantityStatistics.systemId != null and quantityStatistics.systemId != '' " >
            AND system_id = #{quantityStatistics.systemId}
        </if>
        <if test="quantityStatistics.dataType != null" >
            AND data_type = #{quantityStatistics.dataType}
        </if>
    </select>



    <select id="getAiCountGroup" parameterType="java.util.Map" resultMap="QuantityStatistics">
        SELECT ifnull(sum(quantity),0) as quantity,data_type
        FROM quantity_statistics
        where
        1=1
        <if test="quantityStatistics.startTime != null and quantityStatistics.startTime != '' " >
            and timing >= #{quantityStatistics.startTime}
        </if>
        <if test="quantityStatistics.endTime != null and quantityStatistics.endTime != '' " >
            and timing <![CDATA[<=]]>  #{quantityStatistics.endTime}
        </if>
        <if test="quantityStatistics.systemId != null and quantityStatistics.systemId != '' " >
            AND system_id = #{quantityStatistics.systemId}
        </if>
        <if test="quantityStatistics.dataType != null" >
            AND data_type = #{quantityStatistics.dataType}
        </if>
        <if test = "quantityStatistics.dataTypeList != null and quantityStatistics.dataTypeList.size > 0 ">
            AND data_type IN
            <foreach collection="quantityStatistics.dataTypeList" item="dataType" open="(" separator="," close=")">
                #{dataType}
            </foreach>
        </if>
        group by data_type
    </select>



</mapper>

