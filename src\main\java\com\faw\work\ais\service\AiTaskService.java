package com.faw.work.ais.service;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.AiTaskDTO;
import com.faw.work.ais.entity.dto.ai.CosQueueDTO;
import com.faw.work.ais.entity.dto.python.AiTaskFormPythonDTO;

import java.util.List;

public interface AiTaskService {

    /**
     * 异步
     * @param aiTaskDTO
     * @return
     */
    Response asyncAiTask(AiTaskDTO aiTaskDTO);

    /**
     * 异步-批量
     * @param aiTaskDTOs
     * @return
     */
    Response asyncAiTaskBatch(List<AiTaskDTO> aiTaskDTOs);

    /**
     * 获取ai处理返回结果
     * @param cosQueueDTO
     * @return
     */
    Response getAiResult(CosQueueDTO cosQueueDTO) throws InterruptedException;

    void dynamicUrlTest(String urlType);

    /**
     * 处理异常消息队列信息
     * @param cosQueueDTO
     */
    void dealErrorQueueMsg(CosQueueDTO cosQueueDTO);

    /**
     * 获取ai处理返回结果
     * @param aiTaskFormPythonDTO
     * @return
     */
    Response saveAiTaskResultFromPython(AiTaskFormPythonDTO aiTaskFormPythonDTO);
}
