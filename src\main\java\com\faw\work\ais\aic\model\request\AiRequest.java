package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ai请求
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "请求参数 AbTest")
public class AiRequest {


    /**
     * 模型选择(三种固定为qwen-max，qwen-plus，qwen-turbo)
     */
    @Schema(description = "模型选择")
    private String modelName;

    /**
     * Prompt设置
     */
    @Schema(description = "提示词设置")
    private String prompt;

    /**
     * 温度系数 (0-1.99)
     */
    @Schema(description = "温度系数")
    private Double temperature;

    /**
     * 最长回复长度
     */
    @Schema(description = "最长回复长度")
    private Integer maxResponseLength;

    /**
     * 携带上下文轮数
     */
    @Schema(description = "携带上下文轮数")
    private Integer contextRounds;

    /**
     * 用户输入内容
     */
    @Schema(description = "用户输入内容")
    private String userInput;

}
