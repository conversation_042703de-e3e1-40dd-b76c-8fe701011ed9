package com.faw.work.ais.aic.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.dto.PocExcelDataDTO;
import com.faw.work.ais.aic.model.request.FaqSearchByRobotRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import com.faw.work.ais.aic.model.response.PocBatchSearchResponse;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * POC测试Controller
 * 用于POC测试相关功能
 * <AUTHOR>
 */
@RestController
@RequestMapping("/poc")
@Tag(name = "POC测试管理", description = "POC测试相关接口")
@Slf4j
public class PocController {

    @Autowired
    private FaqKnowledgeService faqKnowledgeService;

    /**
     * 批量搜索FAQ知识
     * 读取Excel文件中的query列，逐个调用search-by-robot接口
     *
     * @param file Excel文件，包含query列
     * @return 批量搜索结果
     */
    @PostMapping("/batch-search-faq")
    @Operation(summary = "批量搜索FAQ知识", description = "[author:10200571]")
    public AiResult<PocBatchSearchResponse> batchSearchFaq(@RequestParam("file") MultipartFile file) {
        log.info("接收到批量搜索FAQ知识请求，文件名: {}", file.getOriginalFilename());

        // 校验文件
        if (file == null || file.isEmpty()) {
            throw new BizException("上传文件不能为空");
        }

        // 校验文件类型
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename) ||
            (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            throw new BizException("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }

        // 读取Excel数据
        List<PocExcelDataDTO> excelDataList = readExcelData(file);
        
        if (CollUtil.isEmpty(excelDataList)) {
            throw new BizException("Excel文件中没有有效的query数据");
        }

        log.info("从Excel中读取到{}条query数据", excelDataList.size());

        // 批量调用search-by-robot接口
        List<PocBatchSearchResponse.SearchResult> searchResults = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < excelDataList.size(); i++) {
            PocExcelDataDTO excelData = excelDataList.get(i);
            String query = excelData.getQuery();
            
            try {
                // 构建搜索请求
                FaqSearchByRobotRequest searchRequest = new FaqSearchByRobotRequest();
                searchRequest.setRobotId("1922448048669437954");
                searchRequest.setEnv("prod");
                searchRequest.setQuery(query);

                log.info("正在处理第{}条query: {}", i + 1, query);

                // 调用搜索接口
                List<FaqKnowledgeResponse> knowledgeList = faqKnowledgeService.searchByRobotId(searchRequest);

                // 构建搜索结果
                PocBatchSearchResponse.SearchResult result = PocBatchSearchResponse.SearchResult.builder()
                        .rowIndex(i + 1)
                        .query(query)
                        .success(true)
                        .knowledgeList(knowledgeList)
                        .knowledgeCount(knowledgeList != null ? knowledgeList.size() : 0)
                        .build();

                searchResults.add(result);
                successCount++;

                log.info("第{}条query处理成功，返回{}条知识", i + 1, result.getKnowledgeCount());

            } catch (Exception e) {
                log.error("处理第{}条query失败: {}", i + 1, query, e);

                // 构建失败结果
                PocBatchSearchResponse.SearchResult result = PocBatchSearchResponse.SearchResult.builder()
                        .rowIndex(i + 1)
                        .query(query)
                        .success(false)
                        .errorMessage(e.getMessage())
                        .knowledgeCount(0)
                        .build();

                searchResults.add(result);
                failCount++;
            }
        }

        // 构建响应结果
        PocBatchSearchResponse response = PocBatchSearchResponse.builder()
                .totalCount(excelDataList.size())
                .successCount(successCount)
                .failCount(failCount)
                .searchResults(searchResults)
                .build();

        log.info("批量搜索完成，总计{}条，成功{}条，失败{}条", 
                response.getTotalCount(), response.getSuccessCount(), response.getFailCount());

        return AiResult.success(response);
    }

    /**
     * 读取Excel数据
     *
     * @param file Excel文件
     * @return Excel数据列表
     */
    private List<PocExcelDataDTO> readExcelData(MultipartFile file) {
        List<PocExcelDataDTO> dataList = new ArrayList<>();

        try {
            ExcelReader excelReader = EasyExcel.read(file.getInputStream(), PocExcelDataDTO.class, 
                new ReadListener<PocExcelDataDTO>() {
                    @Override
                    public void invoke(PocExcelDataDTO data, AnalysisContext context) {
                        if (data != null && StrUtil.isNotBlank(data.getQuery())) {
                            dataList.add(data);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("Excel读取完成，共{}条有效数据", dataList.size());
                    }
                }).build();

            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
            excelReader.finish();

        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            throw new BizException("读取Excel文件失败: " + e.getMessage());
        }

        return dataList;
    }
}
