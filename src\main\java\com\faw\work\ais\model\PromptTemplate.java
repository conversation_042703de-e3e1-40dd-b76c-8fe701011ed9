package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@TableName("prompt_template")
@Schema(name = "PromptTemplate", description = "提示词模板")
public class PromptTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "主键")
    private String id;

    @Schema(description = "提示词")
    private String prompt;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "创建时间")
    private Timestamp createTime;

    @Schema(description = "大模型返回的更新内容")
    private String updateContent;
}
