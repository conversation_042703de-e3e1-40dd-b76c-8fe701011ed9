package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 图片解析返回结果
 *
 * <AUTHOR>
 * @since 2025-04-22 9:56
 */
@Data
@Schema(description = "图片解析返回结果")
public class PhotoAnalysisResponse {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "模型")
    private String model;

    @Schema(description = "选择")
    private List<ChoiceEntity> choices;

    @Schema(description = "对象")
    private String object;

    @Schema(description = "使用情况")
    private UsageEntity usage;

    @Schema(description = "创建时间")
    private Date created;

}
