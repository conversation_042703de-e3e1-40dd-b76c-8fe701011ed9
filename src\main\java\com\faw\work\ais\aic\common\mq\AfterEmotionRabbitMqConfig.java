package com.faw.work.ais.aic.common.mq;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 售后接待情绪价值模型 RabbitMQ 配置类
 * 配置核心组件：队列、交换机、绑定关系、死信机制及消息监听容器
 * <AUTHOR>
 */
@Configuration
public class AfterEmotionRabbitMqConfig {

    /**
     * 主队列名称，默认值：after-emotion-processing-queue
     */
    @Value("${rabbitmq.after-emotion.queue.name:after-emotion-processing-queue}")
    private String queueName;

    /**
     * 主交换机名称，默认值：after-emotion-processing-exchange
     */
    @Value("${rabbitmq.after-emotion.exchange.name:after-emotion-processing-exchange}")
    private String exchangeName;

    /**
     * 主路由键，默认值：after-emotion-processing
     */
    @Value("${rabbitmq.after-emotion.routing.key:after-emotion-processing}")
    private String routingKey;

    /**
     * 死信交换机名称，默认值：after-emotion-processing-dlx
     */
    @Value("${rabbitmq.after-emotion.dlx.exchange:after-emotion-processing-dlx}")
    private String dlxExchange;

    /**
     * 死信队列名称，默认值：after-emotion-processing-dlq
     */
    @Value("${rabbitmq.after-emotion.dlx.queue:after-emotion-processing-dlq}")
    private String dlxQueue;

    /**
     * 死信路由键，默认值：after-emotion-processing-dlq
     */
    @Value("${rabbitmq.after-emotion.dlx.routing.key:after-emotion-processing-dlq}")
    private String dlxRoutingKey;

    /**
     * 创建主队列Bean（持久化）
     * @return 配置好的队列实例
     */
    @Bean
    public Queue afterEmotionQueue() {
        return QueueBuilder.durable(queueName)
                .withArgument("x-dead-letter-exchange", dlxExchange)
                .withArgument("x-dead-letter-routing-key", dlxRoutingKey)
                .withArgument("x-message-ttl", ********)
                .build();
    }

    /**
     * 创建主交换机Bean（直连类型，持久化）
     * @return 配置好的交换机实例
     */
    @Bean
    public DirectExchange afterEmotionExchange() {
        return new DirectExchange(exchangeName, true, false);
    }

    /**
     * 创建主队列与主交换机的绑定关系Bean (推荐的写法)
     * @param afterEmotionQueue 通过参数注入Queue Bean
     * @param afterEmotionExchange 通过参数注入Exchange Bean
     * @return 绑定关系实例
     */
    @Bean
    public Binding afterEmotionBinding(Queue afterEmotionQueue, DirectExchange afterEmotionExchange) {
        return BindingBuilder.bind(afterEmotionQueue)
                .to(afterEmotionExchange)
                .with(routingKey);
    }

    /**
     * 创建死信交换机Bean（直连类型，持久化）
     * @return 配置好的死信交换机实例
     */
    @Bean
    public DirectExchange afterEmotionDlxExchange() {
        return new DirectExchange(dlxExchange, true, false);
    }

    /**
     * 创建死信队列Bean（持久化）
     * @return 配置好的死信队列实例
     */
    @Bean
    public Queue afterEmotionDlxQueue() {
        return QueueBuilder.durable(dlxQueue).build();
    }

    /**
     * 创建死信队列与死信交换机的绑定关系Bean (推荐的写法)
     * @return 绑定关系实例
     */
    @Bean
    public Binding afterEmotionDlxBinding(
            @Qualifier("afterEmotionDlxQueue") Queue dlxQueue,
            @Qualifier("afterEmotionDlxExchange") DirectExchange dlxExchange) {
        return BindingBuilder.bind(dlxQueue)
                .to(dlxExchange)
                .with(dlxRoutingKey);
    }
}
