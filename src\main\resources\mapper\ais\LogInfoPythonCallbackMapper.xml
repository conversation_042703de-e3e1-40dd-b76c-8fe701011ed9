<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.LogInfoPythonCallbackMapper">
  <resultMap id="BaseResultMap" type="com.faw.work.ais.model.LogInfoPythonCallback">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="audit_model_scene_id" jdbcType="BIGINT" property="auditModelSceneId" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="python_call_time" jdbcType="TIMESTAMP" property="pythonCallTime" />
    <result column="python_result_time" jdbcType="TIMESTAMP" property="pythonResultTime" />
    <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="callback_result_time" jdbcType="TIMESTAMP" property="callbackResultTime" />
    <result column="coskey" jdbcType="VARCHAR" property="coskey" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="push_count" jdbcType="INTEGER" property="pushCount" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs">
    <result column="python_param" jdbcType="LONGVARCHAR" property="pythonParam" />
    <result column="python_result_json" jdbcType="LONGVARCHAR" property="pythonResultJson" />
    <result column="callback_param" jdbcType="LONGVARCHAR" property="callbackParam" />
    <result column="callback_result" jdbcType="LONGVARCHAR" property="callbackResult" />
  </resultMap>
  <sql id="Base_Column_List">
    id, batch_id, trace_id, python_call_time, python_result_time, callback_time, update_time, 
    callback_result_time, coskey, create_time, push_count
  </sql>
  <sql id="Blob_Column_List">
    python_param, python_result_json, callback_param, callback_result
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from log_info_python_callback_new
    where id = #{id}
  </select>

  <select id="selectByTraceId" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from log_info_python_callback_new
    where trace_id = #{traceId}
  </select>

  <select id="selectByObj" parameterType="com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from log_info_python_callback_new
    <where>
      <if test="traceId != null and traceId != ''">
        AND trace_id = #{traceId,jdbcType=VARCHAR}
      </if>
      <if test="batchId != null and batchId != ''">
        AND batch_id = #{batchId,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from log_info_python_callback_new
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs" useGeneratedKeys="true">
    insert into log_info_python_callback_new (batch_id, trace_id, python_call_time,
      python_result_time, callback_time, update_time, 
      callback_result_time, coskey, create_time, 
      python_param, python_result_json, 
      callback_param, callback_result, push_count, audit_model_scene_id)
    values (#{batchId,jdbcType=VARCHAR}, #{traceId,jdbcType=VARCHAR}, #{pythonCallTime,jdbcType=TIMESTAMP},
      #{pythonResultTime,jdbcType=TIMESTAMP}, #{callbackTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{callbackResultTime,jdbcType=TIMESTAMP}, #{coskey,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{pythonParam,jdbcType=LONGVARCHAR}, #{pythonResultJson,jdbcType=LONGVARCHAR}, 
      #{callbackParam,jdbcType=LONGVARCHAR}, #{callbackResult,jdbcType=LONGVARCHAR}, #{pushCount,jdbcType=BIGINT}, #{auditModelSceneId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs" useGeneratedKeys="true">
    insert into log_info_python_callback_new
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="traceId != null">
        trace_id,
      </if>
      <if test="pythonCallTime != null">
        python_call_time,
      </if>
      <if test="pythonResultTime != null">
        python_result_time,
      </if>
      <if test="callbackTime != null">
        callback_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="callbackResultTime != null">
        callback_result_time,
      </if>
      <if test="coskey != null">
        coskey,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="pythonParam != null">
        python_param,
      </if>
      <if test="pythonResultJson != null">
        python_result_json,
      </if>
      <if test="callbackParam != null">
        callback_param,
      </if>
      <if test="callbackResult != null">
        callback_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="traceId != null">
        #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="pythonCallTime != null">
        #{pythonCallTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pythonResultTime != null">
        #{pythonResultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callbackTime != null">
        #{callbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callbackResultTime != null">
        #{callbackResultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coskey != null">
        #{coskey,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pythonParam != null">
        #{pythonParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="pythonResultJson != null">
        #{pythonResultJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="callbackParam != null">
        #{callbackParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="callbackResult != null">
        #{callbackResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs">
    update log_info_python_callback_new
    <set>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="traceId != null">
        trace_id = #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="pythonCallTime != null">
        python_call_time = #{pythonCallTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pythonResultTime != null">
        python_result_time = #{pythonResultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callbackTime != null">
        callback_time = #{callbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callbackResultTime != null">
        callback_result_time = #{callbackResultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coskey != null">
        coskey = #{coskey,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pythonParam != null">
        python_param = #{pythonParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="pythonResultJson != null">
        python_result_json = #{pythonResultJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="callbackParam != null">
        callback_param = #{callbackParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="callbackResult != null">
        callback_result = #{callbackResult,jdbcType=LONGVARCHAR},
      </if>
      <if test="pushCount != null">
        push_count = #{pushCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs">
    update log_info_python_callback_new
    set batch_id = #{batchId,jdbcType=VARCHAR},
      trace_id = #{traceId,jdbcType=VARCHAR},
      python_call_time = #{pythonCallTime,jdbcType=TIMESTAMP},
      python_result_time = #{pythonResultTime,jdbcType=TIMESTAMP},
      callback_time = #{callbackTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      callback_result_time = #{callbackResultTime,jdbcType=TIMESTAMP},
      coskey = #{coskey,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      python_param = #{pythonParam,jdbcType=LONGVARCHAR},
      python_result_json = #{pythonResultJson,jdbcType=LONGVARCHAR},
      callback_param = #{callbackParam,jdbcType=LONGVARCHAR},
      callback_result = #{callbackResult,jdbcType=LONGVARCHAR},
      push_count = #{pushCount,jdbcType=INTEGER}
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.faw.work.ais.model.LogInfoPythonCallback">
    update log_info_python_callback_new
    set
        trace_id = #{traceId,jdbcType=VARCHAR},
        batch_id = #{batchId,jdbcType=VARCHAR},
        python_call_time = #{pythonCallTime,jdbcType=TIMESTAMP},
        python_result_time = #{pythonResultTime,jdbcType=TIMESTAMP},
        callback_time = #{callbackTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        callback_result_time = #{callbackResultTime,jdbcType=TIMESTAMP},
        coskey = #{coskey,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        python_param = #{pythonParam,jdbcType=LONGVARCHAR},
        python_result_json = #{pythonResultJson,jdbcType=LONGVARCHAR},
        callback_param = #{callbackParam,jdbcType=LONGVARCHAR},
        callback_result = #{callbackResult,jdbcType=LONGVARCHAR},
        push_count = #{pushCount,jdbcType=INTEGER}
    where id = #{id}
  </update>

  <update id="updateByTranceId" parameterType="com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs">
    update log_info_python_callback_new
    set
        id = #{id},
        batch_id = #{batchId,jdbcType=VARCHAR},
        python_call_time = #{pythonCallTime,jdbcType=TIMESTAMP},
        python_result_time = #{pythonResultTime,jdbcType=TIMESTAMP},
        callback_time = #{callbackTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        callback_result_time = #{callbackResultTime,jdbcType=TIMESTAMP},
        coskey = #{coskey,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        python_param = #{pythonParam,jdbcType=LONGVARCHAR},
        python_result_json = #{pythonResultJson,jdbcType=LONGVARCHAR},
        callback_param = #{callbackParam,jdbcType=LONGVARCHAR},
        callback_result = #{callbackResult,jdbcType=LONGVARCHAR},
        push_count = #{pushCount,jdbcType=INTEGER}
    where trace_id = #{traceId,jdbcType=VARCHAR}
  </update>

  <select id="getPythonInParamByTraceId"  resultType="java.lang.String">
          select
              t.python_param
          from log_info_python_callback_new t
          where
              t.trace_id = #{fileDTO.traceId}
              and t.batch_id = #{fileDTO.batchId}
  </select>

  <select id="getUnAiResultBatchIds" resultType="com.faw.work.ais.entity.vo.ai.RePushAiVO">
      select
          t.batch_id batchId,
          t.trace_id traceId,
          t.coskey
      from log_info_python_callback_new t
      <where>
        AND t.callback_time is null
        AND t.push_count &lt; 3
        <if test="param.beginDate != null and param.beginDate != '' ">
          AND t.create_time &gt;= #{param.beginDate}
        </if>
        <if test="param.endDate != null and param.endDate != '' ">
          AND t.create_time &lt;= #{param.endDate}
        </if>
      </where>
      GROUP BY t.batch_id, t.trace_id, t.coskey
  </select>
</mapper>