package com.faw.work.ais.entity.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/11
 * @description 补推AI数据接口入参
 */
@Data
public class RePushAiDTO {

    @Schema(description = "开始时间")
    private String beginDate;

    @Schema(description = "结束时间")
    private String endDate;

    @Schema(description = "系统id")
    private String systemId;

    @Schema(description = "补推从现在到多少分钟之前的数据，该参数为时间间隔，单位为分钟")
    private String dynamaicMinute;

    @Schema(description = "是否补推送所有数据；1-是；空或0；否")
    private String pushAllFlag;

}
