package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.dto.ai.AiKanBanDTO;
import com.faw.work.ais.entity.dto.ai.AiTaskDTO;
import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.vo.ai.*;
import com.faw.work.ais.mapper.ais.*;
import com.faw.work.ais.service.AiTaskService;
import com.faw.work.ais.service.KanBanService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import java.time.DayOfWeek;
import java.time.temporal.TemporalAdjusters;
import java.util.concurrent.atomic.AtomicReference;

/**
 * AI运营看板Service
 */
@Service
public class KanBanServiceImpl implements KanBanService {

    @Autowired
    private SystemCallBackUrlMapper systemCallBackUrlMapper;

    @Autowired
    private AiTaskResultMapper aiTaskResultMapper;

    @Autowired
    private FileInfoNewDao fileInfoNewDao;

    @Autowired
    private LogInfoPythonCallbackMapper logInfoPythonCallbackMapper;

    @Autowired
    private LogInfoJavaMapper logInfoJavaMapper;

    @Autowired
    private TaskRuleMapper taskRuleMapper;

    @Override
    public KanbanTopDataVO getAiCheckDetails(AiKanBanDTO aiKanBanDTO) {
        // 统计表格中列表上部信息
        KanbanTopDataVO topDataVO = aiTaskResultMapper.getTopData(aiKanBanDTO);
        // 计算规则审核准确率
        KanBanTopDataRuleVO topDataRuleVO = aiTaskResultMapper.getTopDataRule(aiKanBanDTO);
        if(topDataRuleVO != null){
            topDataVO.setRuleData(topDataRuleVO);
        }
        // 统计列表数据
        if(topDataVO != null){
            List<KanBanBillListVO> list = aiTaskResultMapper.getBillList(aiKanBanDTO);
            if(CollectionUtils.isNotEmpty(list)){
                list.stream().forEach(x->{
                    List<KanBanRuleListVO> ruleList = aiTaskResultMapper.getRuleList(x.getBatchId(), aiKanBanDTO.getTraceId(), aiKanBanDTO.getTaskType());
                    if(CollectionUtils.isNotEmpty(ruleList)){
                        x.setRuleListVOList(ruleList);
                    }
                });
                topDataVO.setKanBanBillDatas(list);
            }
        }
        return topDataVO;
    }

    @Override
    public List<SystemVO> getSystemInfo() {
        return systemCallBackUrlMapper.getKanBanSystemList();
    }

    @Override
    public List<TaskRuleVO> getTaskRule() {
        return taskRuleMapper.getTaskRule();
    }


    @Override
    public List<BeCheckFileInfoVO> getfileInfosByTraceId(FileDTO fileDTO) {
        List<BeCheckFileInfoVO> files = new ArrayList<>();
        // 查询文件地址列表
        List<String> fileUrls = fileInfoNewDao.getFileInfosByTraceId(fileDTO.getTraceId());

        if(CollectionUtils.isNotEmpty(fileUrls)){
            fileUrls.forEach(url->{
                BeCheckFileInfoVO file = new BeCheckFileInfoVO();
                file.setFileDownUrl(url);
                files.add(file);
            });
        }

        return files;
    }

    @Override
    public List<RateTrendChartVO> getTrendChard(AiKanBanDTO aiKanBanDTO) {
        if(StringUtils.isEmpty(aiKanBanDTO.getTotalType())){
            throw new RuntimeException("统计类型不能为空！");
        }
        List<RateTrendChartVO> list = new ArrayList<>();
        aiKanBanDTO.setSystemId("YSZC-ESC");
        List<RateAndDateVO> erShouCheVos = getRateAndDate(aiKanBanDTO);
        if(CollectionUtils.isNotEmpty(erShouCheVos)){
            RateTrendChartVO  rateTrendChartVO = new RateTrendChartVO();
            rateTrendChartVO.setSystemId("YSZC-ESC");
            rateTrendChartVO.setSystemName("衍生支持-二手车");
            rateTrendChartVO.setRateAndDateVOList(erShouCheVos);
            list.add(rateTrendChartVO);
        }
        aiKanBanDTO.setSystemId("BNZX");
        List<RateAndDateVO> guobus = getRateAndDate(aiKanBanDTO);
        if(CollectionUtils.isNotEmpty(guobus)){
            RateTrendChartVO  rateTrendChartVO = new RateTrendChartVO();
            rateTrendChartVO.setSystemId("BNZX");
            rateTrendChartVO.setSystemName("补能中心");
            rateTrendChartVO.setRateAndDateVOList(guobus);
            list.add(rateTrendChartVO);
        }
        aiKanBanDTO.setSystemId("YKYY");
        List<RateAndDateVO> yaokes = getRateAndDate(aiKanBanDTO);
        if(CollectionUtils.isNotEmpty(yaokes)){
            RateTrendChartVO  rateTrendChartVO = new RateTrendChartVO();
            rateTrendChartVO.setSystemId("YKYY");
            rateTrendChartVO.setSystemName("要客运营中心");
            rateTrendChartVO.setRateAndDateVOList(yaokes);
            list.add(rateTrendChartVO);
        }
        return list;
    }

    /**
     * 根据维度结算统计日期
     * @param aiKanBanDTO
     * @return
     */
    private List<RateAndDateVO> getRateAndDate(AiKanBanDTO aiKanBanDTO){
        List<RateAndDateVO> totalList = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 天维度计算准确率
        if("1".equals(aiKanBanDTO.getTotalType())){
            // 使用DateTimeFormatter来解析和格式化日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            DateTimeFormatter formatterDay = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startDate = null;
            LocalDate endDate = null;
            if (StringUtils.isNotEmpty(aiKanBanDTO.getHumanCheckTimeStart()) && StringUtils.isNotEmpty(aiKanBanDTO.getHumanCheckTimeEnd())) {
                startDate = LocalDateTime.parse(aiKanBanDTO.getHumanCheckTimeStart(), formatter).toLocalDate();
                endDate = LocalDateTime.parse(aiKanBanDTO.getHumanCheckTimeEnd(), formatter).toLocalDate();
                // 计算并打印两个日期之间相差的天数
                long daysBetween = ChronoUnit.DAYS.between(endDate, startDate);
                long absoluteDaysBetween = Math.abs(daysBetween);
                if(absoluteDaysBetween > 30){
                    throw new RuntimeException("日期间隔不能大于30天，请重新选择！");
                }
            }
            if (startDate != null && endDate != null) {
                // 两个日期都不为空，输出两个日期之间的所有日期（包括起始和结束日期）
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    RateAndDateVO rateAndDateVO = new RateAndDateVO();
                    // 将LocalDate对象格式化为字符串
                    String formattedDate = date.format(formatterDay);
                    rateAndDateVO.setBillDateBegin(formattedDate.concat(" 00:00:00"));
                    rateAndDateVO.setBillDateEnd(formattedDate.concat(" 23:59:59"));
                    // 计算每天的准确率 单据维度
                    String rightRate = aiTaskResultMapper.getAiRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId());
                    rateAndDateVO.setBillRate(rightRate);
                    totalList.add(rateAndDateVO);
                }
            } else {
                // 任一日期为空，输出从当前日期开始到前30天的所有日期
                LocalDate currentDate = LocalDate.now();
                for (int i = 30; i >= 0; i--) {
                    LocalDate pastDate = currentDate.minusDays(i + 1); // 加1是因为我们需要从当前日期开始的前30天
                    RateAndDateVO rateAndDateVO = new RateAndDateVO();
                    // 将LocalDate对象格式化为字符串
                    String formattedDate = pastDate.format(formatterDay);
                    rateAndDateVO.setBillDateBegin(formattedDate.concat(" 00:00:00"));
                    rateAndDateVO.setBillDateEnd(formattedDate.concat(" 23:59:59"));
                    // 计算每天的准确率 单据维度
                    String rightRate = aiTaskResultMapper.getAiRate(rateAndDateVO.getBillDateBegin(), rateAndDateVO.getBillDateEnd(), aiKanBanDTO.getSystemId());
                    rateAndDateVO.setBillRate(rightRate);
                    totalList.add(rateAndDateVO);
                }
            }
        }
        // 周维度计算单据准确率
        if("2".equals(aiKanBanDTO.getTotalType())){
            // 找出当前周的第一天（星期日）
            LocalDate currentWeekStart = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            // 遍历前4周
            for (int i = 0; i < 4; i++) {
                LocalDate startOfWeek = currentWeekStart.minusWeeks(i);
                LocalDate endOfWeek = startOfWeek.plusDays(6); // 周日作为开始，所以加6天是周六
                // 日期转为string类型
                String endDate = endOfWeek.format(formatter);
                String afterStartDate = startOfWeek.format(formatter);
                // 计算准确率
                String rightRate = aiTaskResultMapper.getAiRate(afterStartDate, endDate, aiKanBanDTO.getSystemId());
                // 组装准确率数据
                RateAndDateVO rateAndDateVO = new RateAndDateVO();
                rateAndDateVO.setBillDateBegin(afterStartDate);
                rateAndDateVO.setBillDateEnd(endDate);
                rateAndDateVO.setBillRate(rightRate);

                totalList.add(rateAndDateVO);
            }
        }

        // 计算月维度单据准确率
        if("3".equals(aiKanBanDTO.getTotalType())){
            // 获取当前年份和月份
            YearMonth currentYearMonth = YearMonth.from(today);
            // 遍历前4个月
            for (int i = 0; i < 4; i++) {
                YearMonth targetMonth = currentYearMonth.minusMonths(i);
                // 获取该月的开始日期（即该月的第一天）
                LocalDate startOfMonth = targetMonth.atDay(1);
                // 获取该月的结束日期（即该月的最后一天，可以通过TemporalAdjusters.lastDayOfMonth()实现）
                LocalDate endOfMonth = startOfMonth.with(TemporalAdjusters.lastDayOfMonth());
                // 计算准确率
                String rightRate = aiTaskResultMapper.getAiRate(startOfMonth.toString(), endOfMonth.toString(), aiKanBanDTO.getSystemId());
                // 组装准确率数据
                RateAndDateVO rateAndDateVO = new RateAndDateVO();
                rateAndDateVO.setBillDateBegin(startOfMonth.toString());
                rateAndDateVO.setBillDateEnd(endOfMonth.toString());
                rateAndDateVO.setBillRate(rightRate);

                totalList.add(rateAndDateVO);
            }
        }

        return totalList;
    }

    /**
     * 根据开始时间和时间间隔计算结束日期
     * @param startDate
     * @return
     */
    private RateAndDateVO countDate(String startDate, long beginIntervalDays, long endIntervalDays){
        RateAndDateVO countDate = new RateAndDateVO();
        LocalDate startDateLocalDate = LocalDate.now();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if(StringUtils.isNotEmpty(startDate)){
            startDateLocalDate = LocalDate.parse(startDate, formatter);
        }
        // 根据时间间隔计算开始日期
        LocalDate afterCountStartDate = startDateLocalDate.plusDays(beginIntervalDays);
        // 根据时间间隔计算结束日期
        LocalDate endDateLocalDate = startDateLocalDate.plusDays(endIntervalDays);
        // 将结束日期格式化为字符串并返回
        String endDate = endDateLocalDate.format(formatter);
        String afterStartDate = afterCountStartDate.format(formatter);
        countDate.setBillDateBegin(afterStartDate);
        countDate.setBillDateEnd(endDate);
        return countDate;
    }

    @Override
    public String getPythonInParamByTraceId(FileDTO fileDTO) {
        if(StringUtils.isEmpty(fileDTO.getTraceId())){
            throw new BizException("traceId不能为空！");
        }
        if (StringUtils.isEmpty(fileDTO.getBatchId())){
            throw new BizException("batchId不能为空！");
        }
        // 根据trackeId判断是不是国补
        if(fileDTO.getTraceId().startsWith("TID_")){
            AtomicReference<String> result = new AtomicReference<>("");
            String javaParam = logInfoJavaMapper.getJavaLogByBatchId(fileDTO.getBatchId());
            if(StringUtils.isEmpty(javaParam)){
                throw new BizException("找不到国补入参！");
            }else {
                List<AiTaskDTO> javaParamMap = JSON.parseArray(javaParam, AiTaskDTO.class);
                // 取出javaParamMap中traceId是fileDTO.getTraceId的对象
                javaParamMap.stream().forEach(aiTaskDTO -> {
                    if(aiTaskDTO.getTraceId().equals(fileDTO.getTraceId())){
                        result.set(JSON.toJSONString(aiTaskDTO));
                    }
                });
                return result.get();
            }
        }else {
            return logInfoPythonCallbackMapper.getPythonInParamByTraceId(fileDTO);
        }
    }
}
