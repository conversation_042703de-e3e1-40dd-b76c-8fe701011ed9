package com.faw.work.ais.entity.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 回调接口入参
 */
@Data
public class CallBackDTO {

    @Schema(description = "AI大模型校验校验结果")
    private String taskResult;// ai任务结果

    @Schema(description = "系统id")
    private String systemId;

    @Schema(description = "业务主键")
    private String bizId;

    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "回调动态参数；谁调用谁提供")
    private String callbackCustomParam;

    @Schema(description = "返回结果；true-成功；false-失败")
    private String success;

    @Schema(description = "AI大模型返回的消息")
    private String message;

}
