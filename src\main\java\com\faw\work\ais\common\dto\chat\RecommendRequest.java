package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "猜你喜欢请求")
public class RecommendRequest {

    /**
     * 机器人id
     */
    @NotBlank(message = "机器人id不能为空")
    @Schema(description = "机器人id")
    private String robotId;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    @Schema(description = "用户id")
    private String userId;

    /**
     * 角色(车主1， 粉丝0)
     */
    @Schema(description = "角色(车主1， 粉丝0)")
    private String role;

    /**
     * 车系
     */
    @Schema(description = "车系")
    private String series;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private List<String> label;

    /**
     * 历史问题列表
     */
    @Schema(description = "历史问题列表")
    private List<String> historyQuestions;

    /**
     * 排除的问题id列表
     */
    @Schema(description = "排除的问题id列表")
    private List<String> excludeIds;

}
