package com.faw.work.ais.feign;

import com.alibaba.fastjson2.JSONObject;
import com.faw.work.ais.common.dto.roleworkbench.RoleWorkBenchCloseTaskDto;
import com.faw.work.ais.common.dto.roleworkbench.RoleWorkBenchCreateTaskDto;
import com.faw.work.ais.common.dto.user.QueryUserByLoginAccount;
import com.faw.work.ais.common.vo.iworkUser.IworkUserInfo;
import com.faw.work.ais.entity.domain.ContentRulePO;
import com.faw.work.ais.feign.interceptor.IworkOpenApiFeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * iwork开放api  feign调用接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "openApiFeignClient", url = "${ugcconfig.hostUrl}" , configuration = IworkOpenApiFeignInterceptor.class)
public interface OpenApiFeignClient {

    /**
     * 域账号登录账号查询用户信息
     * @return Response<JSONObject>
     * @param queryUserByLoginAccount 查询参数
     * */
    @PostMapping("/JT/BA/BA-0222/QFC/user/getCenterUserInfo")
    IworkResponse<IworkUserInfo> getCenterUserInfo(@RequestBody QueryUserByLoginAccount queryUserByLoginAccount);

    /**
     * 调用角色工作台触发任务事件
     * @return Response<JSONObject>
     * @param roleWorkBenchCreateTaskDto 触发任务参数
     * */
    @PostMapping("/JT/SA/SA-0214/RWG/openapi/task/create")
    IworkResponse<JSONObject> roleWorkBenchTaskCreate(@RequestBody RoleWorkBenchCreateTaskDto roleWorkBenchCreateTaskDto);

    /**
     * 调用角色工作台关闭任务事件
     * @return Response<JSONObject>
     * @param roleWorkBenchCloseTaskDto 触发任务参数
     * */
    @PostMapping("/JT/SA/SA-0214/RWG/openapi/task/complete")
    IworkResponse<JSONObject> roleWorkBenchTaskClose(@RequestBody RoleWorkBenchCloseTaskDto roleWorkBenchCloseTaskDto);


    /**
     * 调用角色工作台查询当前流程详情
     * @return Response<JSONObject>
     * @param taskInstanceCode 流程code
     * @param ignoreUser 是否忽略当前用户
     * */
    @GetMapping("/JT/SA/SA-0214/RWG/DEFAULT/taskDetail")
    IworkResponse<JSONObject> roleWorkBenchTaskInstanceDetails(@RequestParam("taskInstanceCode") String taskInstanceCode,@RequestParam("ignoreUser") Boolean ignoreUser);

    /**
     * 生成策略回调接口
     * @param contentRulePO
     * @return
     */
    @PostMapping("/JT/SA/SA-0403/108/DEFAULT/saveContentRule")
    IworkResponse<String> generateRule(@RequestBody ContentRulePO contentRulePO);
}
