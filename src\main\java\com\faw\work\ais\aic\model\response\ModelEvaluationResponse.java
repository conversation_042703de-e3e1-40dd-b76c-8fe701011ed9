package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 大模型评测响应
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "大模型评测响应")
public class ModelEvaluationResponse {
    
    @Schema(description = "总数量")
    private Integer total;
    
    @Schema(description = "成功评测数量")
    private Integer successCount;
    
    @Schema(description = "失败评测数量")
    private Integer failCount;
    
    @Schema(description = "输出文件下载URL")
    private String downloadUrl;
    
    @Schema(description = "错误信息列表")
    private List<String> errorMessages;
    
    @Schema(description = "处理耗时（毫秒）")
    private Long processingTime;
}
