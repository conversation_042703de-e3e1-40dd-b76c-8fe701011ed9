package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.AiTaskResultNew;
import com.faw.work.ais.service.AiTaskResultNewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* AI任务结果记录表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 11:20:13
*/
@Schema(description = "AI任务结果记录表")
@RestController
@Slf4j
public class AiTaskResultNewController {

    @Autowired
    private AiTaskResultNewService aiTaskResultNewService;

    /**
    * 新增或修改AI任务结果记录表
    */
    @Operation(summary = "新增或修改AI任务结果记录表-数据迁移", description = "[author:10236535]")
    @RequestMapping(value="/aiTaskResultNew/insertOrUpdate" , method = RequestMethod.POST)
    public Result<Integer> insertOrUpdate(@RequestBody AiTaskResultNew aiTaskResultNew){

        return   aiTaskResultNewService.insertOrUpdate(aiTaskResultNew);

    }


    /**
    * 分页查询AI任务结果记录表
    */
    @Operation(summary = "分页查询AI任务结果记录表", description = "[author:10236535]")
    @RequestMapping(value="/aiTaskResultNew/getAiTaskResultNewList" , method = RequestMethod.POST)
    public Result<List<AiTaskResultNew>> getAiTaskResultNewList(@RequestBody AiTaskResultNew aiTaskResultNew){
    return    aiTaskResultNewService.getAiTaskResultNewList(aiTaskResultNew);

   }

}

