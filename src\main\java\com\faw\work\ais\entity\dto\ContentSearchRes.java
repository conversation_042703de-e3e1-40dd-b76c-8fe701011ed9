package com.faw.work.ais.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 向量搜索结果实体类
 * <AUTHOR>
 */
@Schema(description = "向量搜索结果实体类")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentSearchRes {

    /**
     * 相似内容
     */
    @Schema(description = "相似内容")
    private String content;

    /**
     * embedding
     */
    @Schema(description = "embedding")
    private float[] embedding;
} 