package com.faw.work.ais.service;


import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.OcrDTO;
import com.tencentcloudapi.ocr.v20181119.models.MixedInvoiceItem;
import com.tencentcloudapi.ocr.v20181119.models.TextVehicleBack;
import com.tencentcloudapi.ocr.v20181119.models.TextVehicleFront;

public interface OcrService {

    public String plateLicense(OcrDTO ocrDTO);

    public String vinLicense(OcrDTO ocrDTO);

    Response<TextVehicleFront> driverLicenseFront(OcrDTO ocrDTO);

    Response<TextVehicleFront> driverLicenseFrontForCut(OcrDTO ocrDTO, feign.Response responseEntity, Boolean cutFlag);

    Response<TextVehicleBack> driverLicenseBack(OcrDTO ocrDTO);

    Response<TextVehicleBack> driverLicenseBackForCut(OcrDTO ocrDTO, feign.Response  responseEntity, Boolean cutFlag);

    Response<MixedInvoiceItem[]> motorVehicleSaleInvoice(OcrDTO ocrDTO);

}
