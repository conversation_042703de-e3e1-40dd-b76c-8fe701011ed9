package com.faw.work.ais.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 帖子请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "帖子评分请求对象")
public class PostRequest {


    @Schema(description = "主键ID")
    private Long id;

//    @NotBlank(message = "帖子主题不能为空")
    @Schema(description = "帖子主题")
    private String topic;

    @NotBlank(message = "帖子内容不能为空")
    @Schema(description = "帖子内容")
    private String content;

//    @NotEmpty(message = "图片不能为空")
    @Schema(description = "图片内容")
    private List<String> picUrls;

}
