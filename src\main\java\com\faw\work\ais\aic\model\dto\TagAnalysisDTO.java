package com.faw.work.ais.aic.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 标签分析返回结果DTO
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TagAnalysisDTO {
    /**
     * 输入
     */
    private String input;
    /**
     * 分片ID
     */
    private Long llmRecordId;

    /**
     * 开始时间戳
     */
    private String start;


    /**
     * 结束时间戳
     */
    private String end;

    /**
     * 问题总结
     */

    private List<TopicSummary> customerQuestionSummaries;

    /**
     * 话题总结
     */

    private List<TopicSummary> topicSummaries;
    /**
     * 客户情绪
     */

    private String customerEmotion;

    /**
     * 需求标签
     */

    private List<DemandTag> demandTags;

    /**
     * 情绪总结
     */
    private String reason;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TopicSummary {
        @JsonProperty("question")
        private String question;

        @JsonProperty("answer")
        private String answer;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DemandTag {
        @JsonProperty("level1")
        private String level1;

        @JsonProperty("level2")
        private String level2;

        @JsonProperty("level3")
        private String level3;

        @JsonProperty("tagEmotion")
        private String tagEmotion;

        @JsonProperty("tagEmotionReason")
        private String tagEmotionReason;
    }
} 