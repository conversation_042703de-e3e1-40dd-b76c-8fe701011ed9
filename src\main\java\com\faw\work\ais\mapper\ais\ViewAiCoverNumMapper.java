package com.faw.work.ais.mapper.ais;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeAiCoverInfoDTO;
import com.faw.work.ais.entity.vo.ai.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * ai覆盖场景mapper
 * <AUTHOR>
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface ViewAiCoverNumMapper {

    /**
     * 更新AI覆盖信息
     *
     * @param numberEmployeeAiCoverInfoDTO 更新对象
     * @return 条数
     */
    int updateAiCoverInfo(@Param("numberEmployeeAiCoverInfoDTO") NumberEmployeeAiCoverInfoDTO numberEmployeeAiCoverInfoDTO);

    /**
     * 查询AI覆盖信息
     * @return AiCoveringScenesVO AI覆盖信息
     */
    AiCoveringScenesVO getViewAiCoverNum();

}
