package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Excel数据初始化到非结构化文档响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "Excel数据初始化到非结构化文档响应")
public class RagDocumentExcelInitResponse {

    @Schema(description = "文档ID")
    private Long documentId;

    @Schema(description = "处理的总行数")
    private Integer totalRows;

    @Schema(description = "成功处理的行数")
    private Integer successRows;

    @Schema(description = "失败的行数")
    private Integer failRows;

    @Schema(description = "是否向量化成功")
    private Boolean vectorized;

    @Schema(description = "处理结果消息")
    private String message;
}
