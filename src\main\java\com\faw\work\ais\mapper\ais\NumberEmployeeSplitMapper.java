package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.entity.dto.ai.NumberEmployeeAiCoverInfoDTO;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.vo.ai.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数字员工-优化后的mapper
 */
@Mapper
public interface NumberEmployeeSplitMapper {

    /**
     * 获取AI覆盖规则数-实时计算
     * @param bizUnitInfos 处理人名字
     * @return
     */
    Integer getAiCoverRuleNum(@Param("bizUnitInfos") List<BizUnitInfoVO> bizUnitInfos);

    /**
     * 获取AI触发任务数信息-单据维度
     * @Param numberEmployeeDTO 查询入参
     */
    NumberEmployeeVO getAiTouchTaskInfo(@Param("numberEmployeeDTO") NumberEmployeeDTO numberEmployeeDTO, @Param("bizTypes") List<String> bizTypes);

    /**
     * 获取AI触发规则信息-规则维度
     * @Param numberEmployeeDTO 查询入参
     */
    NumberEmployeeVO getAiTouchRuleInfo(@Param("numberEmployeeDTO") NumberEmployeeDTO numberEmployeeDTO, @Param("bizTypes") List<String> bizTypes);

    /**
     * 获取AI触发规则信息-规则维度-里面的底层数据，service需要加工处理
     * @param numberEmployeeDTO
     * @param bizTypes
     * @return
     */
    List<AiPassResultVO> getAiTouchTaskInfoSplit(@Param("numberEmployeeDTO") NumberEmployeeDTO numberEmployeeDTO, @Param("bizTypes") List<String> bizTypes);

    /**
     * 获取抽检任务数信息
     * @Param numberEmployeeDTO 查询入参
     */
    HumanSampleRightRateVO getSampleTaskInfo(@Param("numberEmployeeDTO") NumberEmployeeDTO numberEmployeeDTO, @Param("bizTypes") List<String> bizTypes);

    /**
     * 获取人工抽检准确率（单据维度）
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    String getSampleRate(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("unitCodes") List<String> unitCodes);

    /**
     * 获取人工抽检准确率（单据维度）- 按天分组，返回多天
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    List<RateAndDateVO> getSampleRateMonth(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("bizTypes") List<String> bizTypes);


    /**
     * 获取AI通过率-1天的
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    String getAiPassRate(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("unitCodes") List<String> unitCodes);

    /**
     * 获取AI通过率-按天分组
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    List<RateAndDateVO> getAiPassRateMonth(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("bizTypes") List<String> bizTypes);

    /**
     * 获取人工抽检准确率（规则维度）
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    String getSampleRuleRate(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("unitCodes") List<String> unitCodes);

    /**
     * 获取人工抽检准确率（规则维度）
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    List<RateAndDateVO> getSampleRuleRateMonth(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("bizTypes") List<String> bizTypes);

    /**
     * ai节省的工时（规则维度）
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    List<RateAndDateVO> aiSaveTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("bizTypes") List<String> bizTypes, @Param("saveTime") Integer saveTime);

    /**
     * ai节省的费用（规则维度）
     * @param beginDate
     * @param endDate
     * @param systemId
     * @return
     */
    List<RateAndDateVO> aiSaveCost(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("systemId") String systemId, @Param("bizTypes") List<String> bizTypes, @Param("saveCost") Integer saveCost);


    /**
     * 获取抽检任务数信息（规则）
     * @Param numberEmployeeDTO 查询入参
     */
    HumanSampleRightRateRuleVO getSampleTaskRuleInfo(@Param("numberEmployeeDTO") NumberEmployeeDTO numberEmployeeDTO, @Param("bizTypes") List<String> bizTypes);

    List<TaskRuleVO> getTaskRuleByBizUintCode(@Param("bizUnitCode") String bizUnitCode);

    /**
     * 获取AI覆盖角色数
     */
    Integer getAiCoverRoleNum(@Param("dealName") String dealName);

    /**
     * 获取AI覆盖业务单元数
     */
    Integer getAiCoverBizNum(@Param("dealName") String dealName);

    /**
     * 获取AI覆盖规则数-预计算
     */
    Integer getAiCoverRuleNum(@Param("dealName") String dealName);

    /**
     * 获取l3流程信息
     * @return
     */
    List<FlowInfoVO> getFlowInfos();

    /**
     * 获取业务单元信息
     * @return
     */
    List<BizUnitInfoVO> getl3BizUnitInfos(@Param("l3FlowBusinessCode") String l3FlowBusinessCode);

    /**
     * 根据业务单元获取bizType信息
     * @param unitCodes
     * @return
     */
    List<String> getBizTypesByUnitCode(@Param("unitCodes") List<String> unitCodes);

    /**
     * 更新AI覆盖信息
     * @param numberEmployeeAiCoverInfoDTO
     * @return
     */
    int updateAiCoverInfo(@Param("numberEmployeeAiCoverInfoDTO") NumberEmployeeAiCoverInfoDTO numberEmployeeAiCoverInfoDTO);
}
