package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.AiAuditModel;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* ai审核模型配置表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 10:36:35
*/
@InterceptorIgnore(tenantLine = "true")
public interface AiAuditModelDao {

    /**
    * 新增
    */
    public int insert(@Param("aiAuditModel") AiAuditModel aiAuditModel);

    /**
    * 删除
    */
    public int delete(@Param("id") Long id);

    /**
    * 修改
    */
    public int update(@Param("aiAuditModel") AiAuditModel aiAuditModel);


    /**
    * 根据id查询 getAiAuditModelById
    */
    public AiAuditModel getAiAuditModelById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<AiAuditModel> getAiAuditModelList(@Param("aiAuditModel")AiAuditModel aiAuditModel);
    public List<AiAuditModel> getExitNameAiAuditModelList(@Param("aiAuditModel")AiAuditModel aiAuditModel);



}

