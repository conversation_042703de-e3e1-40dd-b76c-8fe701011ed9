package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 生效类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EffectiveTypeEnum {
    /**
     * 永久有效
     */
    PERMANENT("00", "永久有效"),
    /**
     * 临时有效
     */
    TEMPORARY("01", "临时有效");

    private final String code;
    private final String desc;

    public static EffectiveTypeEnum getByCode(String code) {
        for (EffectiveTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return PERMANENT;
    }
} 