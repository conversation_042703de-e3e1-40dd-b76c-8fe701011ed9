package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * FAQ命中日志清理响应
 *
 * <AUTHOR>
 */
@Data
@Builder
@Schema(description = "FAQ命中日志清理响应")
public class FaqHitLogCleanResponse {
    
    @Schema(description = "清理的记录数量")
    private Integer cleanCount;
    
    @Schema(description = "清理执行时间")
    private LocalDateTime cleanTime;
    
    @Schema(description = "清理说明")
    private String description;
}
