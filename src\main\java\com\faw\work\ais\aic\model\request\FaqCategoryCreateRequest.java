package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 类目创建请求实体
 * <AUTHOR>
 */
@Data
@Schema(description = "类目创建请求")
public class FaqCategoryCreateRequest {
    
    @Schema(description = "类目名称")
    @NotBlank(message = "类目名称不能为空")
    @Size(max = 255, message = "类目名称长度不能超过255")
    private String name;
    
    @Schema(description = "空间ID")
    private Integer spaceId;
    
    @Schema(description = "父类ID，如果是一级类目则为null")
    private String parentId;
    
    @Schema(description = "创建人")
    private String createdBy;
} 