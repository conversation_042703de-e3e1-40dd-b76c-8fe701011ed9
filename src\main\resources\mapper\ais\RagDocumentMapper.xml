<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.rag.RagDocumentMapper">


    <select id="getDocumentListByAppId" resultType="com.faw.work.ais.aic.model.domain.RagDocumentPO">
        SELECT t4.*
        FROM app t1
                 INNER JOIN app_knowledge_joins t2 ON t1.id = t2.app_id
                 INNER JOIN rag_knowledge_document_joins t3 ON t2.rag_knowledge_id = t3.rag_knowledge_id
                 INNER JOIN rag_document t4 ON t3.document_id = t4.id
        where t1.id = #{agentId}
          and t4.biz_info = #{bizInfo}
    </select>
    <select id="getDetailById" resultType="com.faw.work.ais.aic.model.domain.RagDocumentPO">
        SELECT t1.*,
               t3.collection_name,
               t3.embedding_model,
               t3.similarity_threshold
        FROM rag_document t1 INNER JOIN rag_knowledge_document_joins t2
        ON t1.id=t2.document_id INNER JOIN rag_knowledge t3 ON t2.rag_knowledge_id = t3.id
        WHERE t1.id = #{documentId}

    </select>
    <select id="getDocumentList" resultType="com.faw.work.ais.aic.model.domain.RagDocumentPO">
        select
        *
        from rag_document
        <where>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="fileType != null and fileType != ''">
                and file_type = #{fileType}
            </if>
            <if test="parseStatus != null and parseStatus != ''">
                and parse_status = #{parseStatus}
            </if>
        </where>
    </select>
    <select id="getDocumentsByCategoryIdsWithNoParsedAndFailed" resultType="com.faw.work.ais.aic.model.domain.RagDocumentPO">
        select
        *
        from rag_document
        where parse_status in ('00','10') and category_id in
        <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper> 