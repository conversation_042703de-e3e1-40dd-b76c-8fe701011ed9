package com.faw.work.ais.common.enums;

import lombok.Getter;

/**
 * 车型系列枚举
 *
 * <AUTHOR>
 * @date 2025/04/14
 */
@Getter
public enum SeriesEnum {

    /**
     * H5 车型
     */
    H5("H5", "红旗H5"),

    /**
     * H5-2 车型
     */
    H5_2("H5-2", "红旗H5-2"),

    /**
     * H6 车型
     */
    H6("H6", "红旗H6"),

    /**
     * H7 车型
     */
    H7("H7", "红旗H7"),

    /**
     * H9 车型
     */
    H9("H9", "红旗H9"),

    /**
     * H9+ 车型
     */
    H9_PLUS("H9+", "红旗H9+"),

    /**
     * HS5 车型
     */
    HS5("HS5", "红旗HS5"),

    /**
     * HS7 车型
     */
    HS7("HS7", "红旗HS7"),

    /**
     * HS7-2 车型
     */
    HS7_2("HS7-2", "红旗HS7-2"),

    /**
     * HS3 车型
     */
    HS3("HS3", "红旗HS3"),

    /**
     * 红旗国礼 车型
     */
    HONG_QI_GUO_LI("红旗国礼", "红旗国礼"),

    /**
     * 红旗国耀 车型
     */
    HONG_QI_GUO_YAO("红旗国耀", "红旗国耀"),

    /**
     * 红旗国悦 车型
     */
    HONG_QI_GUO_YUE("红旗国悦", "红旗国悦"),

    /**
     * 红旗国雅 车型
     */
    HONG_QI_GUO_YA("红旗国雅", "红旗国雅"),

    /**
     * 全新L5 车型
     */
    QUAN_XIN_L5("全新L5", "红旗全新L5"),

    /**
     * HQ9 车型
     */
    HQ9("HQ9", "红旗HQ9"),

    /**
     * E007 车型
     */
    E007("E007", "红旗E007"),

    /**
     * E009 车型
     */
    E009("E009", "红旗E009"),

    /**
     * EH7 车型
     */
    EH7("EH7", "红旗EH7"),

    /**
     * E-HS3 车型
     */
    E_HS3("E-HS3", "红旗E-HS3"),

    /**
     * E-HS9 车型
     */
    E_HS9("E-HS9", "红旗E-HS9"),

    /**
     * E-QM5 车型
     */
    E_QM5("E-QM5", "红旗E-QM5"),

    /**
     * 天工05 车型
     */
    TIAN_GONG_05("天工05", "红旗天工05"),

    /**
     * 天工06 车型
     */
    TIAN_GONG_06("天工06", "红旗天工06"),

    /**
     * 天工08 车型
     */
    TIAN_GONG_08("天工08", "红旗天工08");

    SeriesEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final String code;
    private final String msg;
}
