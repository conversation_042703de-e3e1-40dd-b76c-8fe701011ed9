package com.faw.work.ais.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.faw.work.ais.config.WhitelistConfig;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.List;
import java.util.Map;

@Component
public class WhiteListInterceptor implements HandlerInterceptor {
    Logger logger = LoggerFactory.getLogger(AuthorizationInterceptor.class);

    @Autowired
    private WhitelistConfig whitelistConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String systemId = "";
        String url = request.getRequestURI();
        byte[] bodyBytes = StreamUtils.copyToByteArray(request.getInputStream());
        String body = new String(bodyBytes, request.getCharacterEncoding());
        if("/v1/asyncAiTaskBatch".equals(url)){
            List<Map<String, Object>> list = JSON.parseObject(body, new TypeReference<List<Map<String, Object>>>() {});
            if(!CollectionUtils.isEmpty(list)){
                systemId = list.get(0).get("systemId").toString();
            }
        }else {
            Map mapTypes = JSON.parseObject(body);
            systemId = mapTypes.get("systemId").toString();
        }
        List<String> domains = whitelistConfig.getSystemIds();
        if (domains.contains(systemId)) {
            return true; // 域名在白名单中，继续处理请求
        } else {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "该ip禁止访问此地址，请先授权！");
            return false; // 域名不在白名单中，禁止访问
        }
    }
}
