package com.faw.work.ais.common.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain =true)
@Schema(description = "iwork通过用户登录账号查询用户信息参数实体")
public class QueryUserByLoginAccount {

    @Schema(description ="登录账号")
    private String loginName;


}
