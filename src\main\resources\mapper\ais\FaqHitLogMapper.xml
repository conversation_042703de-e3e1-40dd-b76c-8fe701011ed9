<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper">

    <!-- 统计有效知识命中数 -->
    <select id="countHitEffectiveKnowledge" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT knowledge_id)
        FROM faq_hit_log
        WHERE robot_id = #{robotId}
        AND environment = #{env}
        AND is_effective = 1
        <if test="startTime != null and startTime != ''">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND created_at &lt;= #{endTime}
        </if>
    </select>

    <!-- 统计总会话量 -->
    <select id="countTotalChat" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT chat_id)
        FROM faq_hit_log
        WHERE robot_id = #{robotId}
        AND environment = #{env}
        AND is_hit = #{isHit}
        <if test="startTime != null and startTime != ''">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND created_at &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询TOP10命中知识 -->
    <select id="findTop10HitKnowledge"
            resultType="com.faw.work.ais.aic.model.response.FaqReportResponse$FaqHitRankItem">
        SELECT
        fk.question AS knowledgeName,
        COUNT(DISTINCT fhl.id) AS hitCount
        FROM faq_hit_log fhl
        INNER JOIN
        <choose>
            <when test="env == 'prod'">
                faq_knowledge_prod fk ON fhl.knowledge_id = fk.id
                INNER JOIN faq_robot_knowledge_joins_prod rj ON fhl.knowledge_id = rj.knowledge_id
            </when>
            <otherwise>
                faq_knowledge fk ON fhl.knowledge_id = fk.id
                INNER JOIN faq_robot_knowledge_joins rj ON fhl.knowledge_id = rj.knowledge_id
            </otherwise>
        </choose>
        WHERE fhl.robot_id = #{robotId}
        AND fhl.environment = #{env}
        AND rj.source = 'original' -- 只统计原始知识
        <if test="startTime != null and startTime != ''">
            AND fhl.created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND fhl.created_at &lt;= #{endTime}
        </if>
        GROUP BY fhl.knowledge_id, fk.question
        ORDER BY hitCount DESC
        LIMIT 10
    </select>

    <!-- 查询时间范围内的命中知识ID -->
    <select id="findHitKnowledgeIdsInDateRange" resultType="java.lang.String">
        SELECT id
        FROM faq_hit_log
        WHERE robot_id = #{robotId}
        AND environment = #{env}
        <if test="startTime != null and startTime != ''">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND created_at &lt;= #{endTime}
        </if>
    </select>
    <select id="countHitEffectiveOriginalKnowledge" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT fhl.knowledge_id)
        FROM faq_hit_log fhl
        INNER JOIN
        <choose>
            <when test="env == 'prod'">
                faq_robot_knowledge_joins_prod rj ON fhl.knowledge_id = rj.knowledge_id
            </when>
            <otherwise>
                faq_robot_knowledge_joins rj ON fhl.knowledge_id = rj.knowledge_id
            </otherwise>
        </choose>
        WHERE fhl.robot_id = #{robotId}
        AND fhl.environment = #{env}
        AND fhl.is_effective = 1
        AND rj.source = 'original' -- 只统计原始知识
        <if test="startTime != null and startTime != ''">
            AND fhl.created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND fhl.created_at &lt;= #{endTime}
        </if>
    </select>
    <select id="findHitOriginalKnowledgeIdsInDateRange" resultType="java.lang.String">
        SELECT DISTINCT fhl.knowledge_id  <!-- 修复：查询knowledge_id而不是id -->
        FROM faq_hit_log fhl
        INNER JOIN
        <choose>
            <when test="env == 'prod'">
                faq_robot_knowledge_joins_prod rj ON fhl.knowledge_id = rj.knowledge_id
            </when>
            <otherwise>
                faq_robot_knowledge_joins rj ON fhl.knowledge_id = rj.knowledge_id
            </otherwise>
        </choose>
        WHERE fhl.robot_id = #{robotId}
        AND fhl.environment = #{env}
        AND rj.source = 'original' -- 只查询原始知识的命中
        <if test="startTime != null and startTime != ''">
            AND fhl.created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND fhl.created_at &lt;= #{endTime}
        </if>
    </select>
    <select id="countByKnowledgeId" resultType="java.lang.Long">
        SELECT COUNT(id)
        FROM faq_hit_log
        WHERE knowledge_id = #{id}
          and is_hit = '1'
    </select>

    <!-- 清理一个月前的FAQ命中日志数据 -->
    <delete id="cleanOldHitLogs">
        DELETE
        FROM faq_hit_log
        WHERE created_at &lt; DATE_SUB(NOW(), INTERVAL 1 MONTH)
    </delete>

    <!-- 根据条件更新标注任务ID -->
    <update id="updateAnnotationTaskId">
        UPDATE faq_hit_log
        SET annotation_task_id = #{annotationTaskId},
        updated_at = NOW()
        WHERE robot_id = #{robotId}
        <if test="dataSource != null and dataSource != '' and dataSource != 'all'">
            AND environment = #{dataSource}
        </if>
        <if test="startTime != null and startTime != ''">
            AND hit_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND hit_time &lt;= #{endTime}
        </if>
        AND annotation_task_id IS NULL
    </update>

    <!-- 根据标注任务ID分页查询命中日志详情 -->
    <select id="findByAnnotationTaskId" resultType="com.faw.work.ais.aic.model.domain.FaqHitLogPO">
        SELECT *
        FROM faq_hit_log
        WHERE annotation_task_id = #{annotationTaskId}
        <if test="annotationType != null and annotationType != ''">
            AND annotation_type = #{annotationType}
        </if>
        <if test="isLocked != null">
            AND is_locked = #{isLocked}
        </if>
        ORDER BY created_at DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计标注任务的总数据量 -->
    <select id="countByAnnotationTaskId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM faq_hit_log
        WHERE annotation_task_id = #{annotationTaskId}
    </select>

    <!-- 统计标注任务的已标注数量 -->
    <select id="countAnnotatedByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM faq_hit_log
        WHERE annotation_task_id = #{annotationTaskId}
          AND is_locked = 1
    </select>

    <!-- 根据标注类型统计数量 -->
    <select id="countByAnnotationType" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM faq_hit_log
        WHERE annotation_task_id = #{annotationTaskId}
        <if test="annotationType != null and annotationType != ''">
            AND annotation_type = #{annotationType}
        </if>
        <if test="annotationSubtype != null and annotationSubtype != ''">
            AND annotation_subtype = #{annotationSubtype}
        </if>
    </select>

    <!-- 更新标注信息 -->
    <update id="updateAnnotation">
        UPDATE faq_hit_log
        SET annotation_type    = #{annotationType},
            annotation_subtype = #{annotationSubtype},
            annotator_id       = #{annotatorId},
            annotator_name     = #{annotatorName},
            annotated_at       = NOW(),
            is_locked          = 1,
            updated_at         = NOW()
        WHERE id = #{hitLogId}
    </update>

    <!-- 解锁标注（重新标注） -->
    <update id="unlockAnnotation">
        UPDATE faq_hit_log
        SET annotation_type    = NULL,
            annotation_subtype = NULL,
            annotator_id       = NULL,
            annotator_name     = NULL,
            annotated_at       = NULL,
            is_locked          = 0,
            updated_at         = NOW()
        WHERE id = #{hitLogId}
    </update>

    <!-- 根据任务ID查询所有标注数据 -->
    <select id="findAllByAnnotationTaskId" resultType="com.faw.work.ais.aic.model.domain.FaqHitLogPO">
        SELECT *
        FROM faq_hit_log
        WHERE annotation_task_id = #{annotationTaskId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据条件查询FAQ命中日志 -->
    <select id="findByCondition" resultType="com.faw.work.ais.aic.model.domain.FaqHitLogPO">
        SELECT *
        FROM faq_hit_log
        WHERE robot_id = #{robotId}
        <if test="dataSource != null and dataSource != '' and dataSource != 'all'">
            AND environment = #{dataSource}
        </if>
        <if test="startTime != null and startTime != ''">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND created_at &lt;= #{endTime}
        </if>
        ORDER BY created_at DESC
        limit 10000
    </select>

</mapper>