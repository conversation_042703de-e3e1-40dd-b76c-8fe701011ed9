package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * FAQ标注详情实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("faq_annotation_detail")
@Accessors(chain = true)
public class FaqAnnotationDetailPO {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("task_id")
    private String taskId;

    @TableField("hit_log_id")
    private String hitLogId;

    @TableField("user_question")
    private String userQuestion;

    @TableField("match_type")
    private String matchType;

    @TableField("matched_content")
    private String matchedContent;

    @TableField("knowledge_id")
    private String knowledgeId;

    @TableField("faq_title")
    private String faqTitle;

    @TableField("match_score")
    private BigDecimal matchScore;

    @TableField("annotation_type")
    private String annotationType;

    @TableField("annotation_subtype")
    private String annotationSubtype;

    @TableField("is_locked")
    private Boolean isLocked;

    @TableField("annotator_id")
    private String annotatorId;

    @TableField("annotator_name")
    private String annotatorName;

    @TableField("annotated_at")
    private LocalDateTime annotatedAt;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
