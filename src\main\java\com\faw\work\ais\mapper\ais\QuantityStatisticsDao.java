package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.QuantityStatistics;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* 数量统计表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 14:12:35
*/
@InterceptorIgnore(tenantLine = "true")
public interface QuantityStatisticsDao {

    /**
    * 新增
    */
    public int insert(@Param("quantityStatistics") QuantityStatistics quantityStatistics);

    /**
     * 新增
     */
    public int insertBatch(@Param("quantityStatisticss") List<QuantityStatistics> quantityStatistics);
    /**
    * 删除
    */
    public int delete(@Param("id") Long id);
    /**
     * 删除
     */
    public int deleteByIds(@Param("ids") List<Long> id);
    /**
     * 删除
     */
    public int deleteByTime(@Param("timing") String timing);
    /**
    * 修改
    */
    public int update(@Param("quantityStatistics") QuantityStatistics quantityStatistics);


    /**
    * 根据id查询 getQuantityStatisticsById
    */
    public QuantityStatistics getQuantityStatisticsById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<QuantityStatistics> getQuantityStatisticsList(@Param("quantityStatistics")QuantityStatistics quantityStatistics);
    /**
     * 全部查询
     */
    public List<QuantityStatistics> getQuantityIdsStatisticsList(@Param("quantityStatistics")QuantityStatistics quantityStatistics);
    /**
     * 全部查询
     */
    public List<QuantityStatistics> getIntervalQuantityStatisticsList(@Param("quantityStatistics")QuantityStatistics quantityStatistics);
    Long getAiCount(@Param("quantityStatistics")QuantityStatistics quantityStatistics);

    List<QuantityStatistics>  getAiCountGroup(@Param("quantityStatistics")QuantityStatistics quantityStatistics);

}

