package com.faw.work.ais.aic.common.enums;

import lombok.Getter;

/**
 * 文档分段策略枚举
 *
 * <AUTHOR>
 * @date 2025/04/07
 */
@Getter
public enum RagDocumentChunkStrategyEnum {
    /**
     * 智能切分
     */
    SMART("00", "智能切分"),
    /**
     * 自定义切分
     */
    CUSTOM("01", "自定义切分");

    RagDocumentChunkStrategyEnum(String code, String codeDesc) {
        this.code = code;
        this.msg = codeDesc;
    }
    private final String code;

    private final String msg;
} 