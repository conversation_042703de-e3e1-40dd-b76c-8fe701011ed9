package com.faw.work.ais.entity.dto.ai.score;

import com.faw.work.ais.entity.dto.ai.BeCheckFileDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> AI 对外服务
 */
@Schema(description = "大模型请求")
@Data
@Builder
public class AiRequestDTO {

    /**
     * 系统id，调用AI的业务系统
     */
    @Schema(description = "系统id，调用AI的业务系统")
    private String systemId;

    /**
     * 业务主键
     */
    @Schema(description = "业务主键")
    private String bizId;
    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String bizType;

    /**
     * 被校验文件信息
     */
    @Schema(description = "被校验文件信息")
    private List<BeCheckFileDTO> contents;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private String taskType;

    /**
     * 给定信息,用于AI校验的信息
     * 例子1: {"idCardName":"姓名",“idCardNo”:“220112198310101456”,“idCardTime”:“当前时间在有效期范围内”}
     * 例子2: [{"validate":"姓名"，“correctValue”:“正确值”},{"validate":"教师资格证"，“correctValue”:“证件号不为空”}]
     */
    @Schema(description = "给定信息")
    private String givenInfoJson;

    /**
     * 给定信息说明,用于描述给定信息中的字段说明
     * 例子1：validate是需要验证的内容，correctValue是给定的正确值，需要验证内容是否与给定的值一致，当前日期是否在有效期范围内
     * 例子2：validate是需要验证的内容，correctValue是给定的正确值或正确值文字说明进行校验
     */
    @Schema(description = "给定信息说明")
    private String givenInfoJsonDesc;

    /**
     * 回调地址
     */
    @Schema(description = "回调地址")
    private String callbackUrl;

    /**
     * 回调鉴权方式
     */
    @Schema(description = "回调鉴权方式")
    private Integer callbackType;

    /**
     * 自定义回调参数
     */
    @Schema(description = "自定义回调参数")
    private String callBackCustomParam;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private String version;

    /**
     * 提供一个标识uuid，查日志用
     */
    @Schema(description = "标识uuid，查日志用")
    private String traceId;

    /**
     * 提示词；替换完占位符的提示词
     */
    @Schema(description = "提示词；替换完占位符的提示词")
    private String prompt;
}
