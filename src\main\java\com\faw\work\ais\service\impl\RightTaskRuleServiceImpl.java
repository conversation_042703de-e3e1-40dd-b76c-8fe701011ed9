package com.faw.work.ais.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.vo.ai.AiCoverDicVO;
import com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleVO;
import com.faw.work.ais.mapper.ais.RightTaskRuleMapper;
import com.faw.work.ais.service.RightTaskRuleService;
import com.faw.work.ais.model.RightTaskRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RightTaskRuleServiceImpl extends ServiceImpl<RightTaskRuleMapper, RightTaskRule> implements RightTaskRuleService {
    @Autowired
    private  RightTaskRuleMapper rightTaskRuleMapper;
    @Override
    public HumanSampleRightRateRuleVO getSampleTaskRuleInfo(NumberEmployeeDTO numberEmployeeDTO) {
        HumanSampleRightRateRuleVO aiSampleRuleInfo =rightTaskRuleMapper.getSampleTaskRuleInfo(numberEmployeeDTO, numberEmployeeDTO.getBizTypes());
        return aiSampleRuleInfo;
    }

    @Override
    public AiCoverDicVO getCover(NumberEmployeeDTO numberEmployeeDTO) {
        return null;
    }
    // 可以在这里实现自定义的业务方法
}
