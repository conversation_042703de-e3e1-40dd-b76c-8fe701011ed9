package com.faw.work.ais.util;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 通用工具类，包含各种常用工具方法
 */
public class CommonUtils {
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 私有构造方法防止实例化
     */
    private CommonUtils() {
        throw new IllegalStateException("Utility class cannot be instantiated");
    }

    /**
     * 检查字符串是否为空或null
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查集合是否为空或null
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 检查Map是否为空或null
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 格式化日期为字符串
     */
    public static String formatDate(Date date) {
        if (date == null) return null;
        return new SimpleDateFormat(DATE_FORMAT).format(date);
    }

    /**
     * 格式化日期时间为字符串
     */
    public static String formatDateTime(Date date) {
        if (date == null) return null;
        return new SimpleDateFormat(DATE_TIME_FORMAT).format(date);
    }

    /**
     * 解析字符串为日期
     */
    public static Date parseDate(String dateStr) throws ParseException {
        if (isEmpty(dateStr)) return null;
        return new SimpleDateFormat(DATE_FORMAT).parse(dateStr);
    }

    /**
     * 解析字符串为日期时间
     */
    public static Date parseDateTime(String dateTimeStr) throws ParseException {
        if (isEmpty(dateTimeStr)) return null;
        return new SimpleDateFormat(DATE_TIME_FORMAT).parse(dateTimeStr);
    }

    /**
     * 验证电子邮件格式
     */
    public static boolean isValidEmail(String email) {
        if (isEmpty(email)) return false;
        return EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        if (isEmpty(phone)) return false;
        return PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 生成随机字符串
     */
    public static String generateRandomString(int length) {
        if (length <= 0) return "";

        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        return sb.toString();
    }

    /**
     * 将List转换为分页结果
     */
    public static <T> List<T> paginateList(List<T> list, int pageNum, int pageSize) {
        if (isEmpty(list)) return Collections.emptyList();

        int fromIndex = (pageNum - 1) * pageSize;
        if (fromIndex >= list.size()) return Collections.emptyList();

        int toIndex = Math.min(fromIndex + pageSize, list.size());
        return list.subList(fromIndex, toIndex);
    }

    /**
     * 计算文件大小的人类可读格式
     */
    public static String humanReadableByteCount(long bytes) {
        if (bytes < 1024) return bytes + " B";

        int exp = (int) (Math.log(bytes) / Math.log(1024));
        char unit = "KMGTPE".charAt(exp - 1);
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), unit);
    }

    /**
     * 深度复制对象（对象必须实现Serializable接口）
     */
//    实现Serializable接口）

    @SuppressWarnings("unchecked")
    public static <T> T deepCopy(T object) {
        if (object == null) return null;

        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(object);
            oos.close();

            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bais);
            return (T) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException("Deep copy failed", e);
        }
    }

    /**
     * 计算两个日期之间的天数差
     */
    public static int daysBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) return 0;

        long diffInMillis = Math.abs(endDate.getTime() - startDate.getTime());
        return (int) (diffInMillis / (1000 * 60 * 60 * 24));
    }

    /**
     * 将驼峰命名转换为下划线命名
     */
    public static String camelToUnderline(String str) {
        if (isEmpty(str)) return str;

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isUpperCase(c)) {
                result.append("_").append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 将下划线命名转换为驼峰命名
     */
    public static String underlineToCamel(String str) {
        if (isEmpty(str)) return str;

        StringBuilder result = new StringBuilder();
        boolean nextUpper = false;

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '_') {
                nextUpper = true;
            } else {
                if (nextUpper) {
                    result.append(Character.toUpperCase(c));
                    nextUpper = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        return result.toString();
    }
}