package com.faw.work.ais.common.dto.chat;

import com.alibaba.dashscope.audio.asr.recognition.timestamp.Sentence;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 语音转文字实体
 *
 * <AUTHOR>
 * @since 2025-04-21 10:01
 */
@Data
@Schema(description = "语音转文字 实体类")
public class VoiceToTextEntity {

    /**
     * 识别结果
     */
    @Schema(description = "识别结果")
    private List<Sentence> sentences;

}
