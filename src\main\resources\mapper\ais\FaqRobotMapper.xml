<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqRobotMapper">
    <insert id="savePublishRecord">
        INSERT INTO faq_robot_publish_record (publisher, publish_time, status, version, robotId)
        VALUES (#{publisher}, #{publishTime}, #{status}, #{version}, #{robotId})
    </insert>
    <update id="updateRobotStatus">
        UPDATE faq_robot SET status = #{status} WHERE id = #{robotId}
    </update>

    <!-- 分页查询机器人列表 -->
    <select id="selectPageWithRobotName" resultType="com.faw.work.ais.aic.model.domain.FaqRobotPO">
        SELECT
        *
        FROM faq_robot
        <where>
            <if test="robotName != null and robotName != ''">
                AND robot_name LIKE CONCAT('%', #{robotName}, '%')
            </if>
        </where>
        ORDER BY updated_at DESC
    </select>
    <select id="getRobotByName" resultType="com.faw.work.ais.aic.model.domain.FaqRobotPO">
        select * from faq_robot where robot_name = #{robotName}
    </select>

</mapper> 