package com.faw.work.ais.common.exception;


import com.faw.work.ais.common.enums.IEnum;
import com.faw.work.ais.common.enums.ResEnum;

public class BizException extends RuntimeException {

	private static final long serialVersionUID = 5188502951761537232L;
	
	private IEnum errorEnum;

    public BizException(IEnum errorEnum, Throwable throwable) {
        super(throwable);
        this.errorEnum = errorEnum;
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
        this.errorEnum = new CustomErrorWrapper(ResEnum.FAIL_CODE.code(), message);
    }

    public BizException(IEnum errorEnum) {
        this.errorEnum = errorEnum;
    }

    public BizException(String code ,String msg) {
        this.errorEnum = new CustomErrorWrapper(code,msg);
    }

    public BizException(String msg) {
        this.errorEnum = new CustomErrorWrapper(ResEnum.FAIL_CODE.code(),msg);
    }

    public IEnum getErrorEnum() {
        return errorEnum;
    }
}
