package com.faw.work.ais.aic.mapper.rag;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档表 Mapper 接口
 *
 * <AUTHOR> Assistant
 */
@Mapper
public interface RagDocumentMapper extends BaseMapper<RagDocumentPO> {

    /**
     * 条件查询文档列表
     *
     * @param request 查询条件
     * @return 文档列表
     */
    List<RagDocumentPO> getDocumentList(RagDocumentPO request);

    /**
     * 按应用程序id获取列表
     *
     * @param agentId   应用ID
     * @param bizInfo 我们信息
     * @return {@link List }<{@link RagDocumentPO }>
     */
    List<RagDocumentPO> getDocumentListByAppId(Long agentId, String bizInfo);

    /**
     * 根据文档ID获取文档详情
     *
     * @param documentId 文档ID
     * @return 文档详情
     */
    RagDocumentPO getDetailById(Long documentId);

    /**
     * 根据分类ID列表获取文档列表
     *
     * @param categoryIds 分类ID列表
     * @return 文档列表
     */
    List<RagDocumentPO> getDocumentsByCategoryIdsWithNoParsedAndFailed(@Param("categoryIds") List<Long> categoryIds);
}