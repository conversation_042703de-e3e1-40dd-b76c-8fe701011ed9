package com.faw.work.ais.aic.model.request;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 文本重排序 API 的请求体
 * <AUTHOR>
 */
@Data
@Builder
public class RerankRequest {

    /**
     * 用于重排序的模型名称
     * 例如: "gte-rerank-v2"
     */
    private String model;

    /**
     * 输入数据，包含查询和文档
     */
    private Input input;

    /**
     * 可选参数
     */
    private Parameters parameters;

    /**
     * 文本重排序 API 的输入数据
     */
    @Data
    @Builder
    public static class Input {
        /**
         * 查询文本
         */
        private String query;

        /**
         * 要排序的文档列表
         */
        private List<String> documents;
    }

    /**
     * 文本重排序 API 的参数
     */
    @Data
    @Builder
    public static class Parameters {
        /**
         * 是否在响应中返回文档内容
         */
        private Boolean return_documents;

        /**
         * 返回的排序结果数量
         */
        private Integer top_n;
    }
} 