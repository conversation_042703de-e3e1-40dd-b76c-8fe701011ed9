package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * FAQ知识查询请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ知识查询请求对象")
public class FaqKnowledgeQueryRequest {

    @Schema(description = "查询类型 00-题目 01-答案")
    private String type;

    @Schema(description = "用户的输入")
    private String text;
    
    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum ;
    
    @Schema(description = "每页大小", defaultValue = "10")
    private Integer pageSize ;

    @Schema(description = "环境 test-测试环境 prod-生产环境")
    private String env;

    @Schema(description = "最底层分类ID")
    private List<String> categoryIds;

    @Schema(description = "机器人id")
    private String robotId;
} 