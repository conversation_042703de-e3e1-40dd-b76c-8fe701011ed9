package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.AiAuditModel;
import com.faw.work.ais.model.AiScene;
import com.faw.work.ais.model.base.PageList;
import com.faw.work.ais.service.AiSceneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * ai场景配置表
 * Created  by Mr.hp
 *
 * <AUTHOR> Mr.hp
 * DateTime on 2025-05-08 10:57:36
 */
@Schema(name = "人工智能ai场景配置表")
@RestController
@Slf4j
public class AiSceneController {

    @Autowired
    private AiSceneService aiSceneService;

    /**
     * 新增或修改ai场景配置表
     */
    @Operation(summary = "新增或修改ai场景配置表", description = "[author:50013081]")
    @RequestMapping(value = "/aiScene/insertOrUpdate", method = RequestMethod.POST)
    public Result<Integer> insertOrUpdate(@RequestBody AiScene aiScene) {

        return aiSceneService.insertOrUpdate(aiScene);

    }

    /**
     * 新增ai场景配置表
     */
    @Operation(summary = "新增ai场景配置表", description = "[author:50013081]")
    @RequestMapping(value = "/aiScene/insert", method = RequestMethod.POST)
    public Result<Integer> insert(@RequestBody AiScene aiScene) {
        return aiSceneService.insert(aiScene);

    }


    /**
     * 修改ai场景配置表
     */
    @Operation(summary = "修改ai场景配置表", description = "[author:50013081]")
    @RequestMapping(value = "/aiScene/update", method = RequestMethod.POST)
    public Result<Integer> update(@RequestBody AiScene aiScene) {
        return aiSceneService.update(aiScene);

    }

    /**
     * 根据Id查询ai场景配置表
     */
    @Operation(summary = "根据Id查询ai场景配置表", description = "[author:50013081]")
    @RequestMapping(value = "/aiScene/getAiSceneById", method = RequestMethod.POST)
    public Result<AiScene> getAiSceneById(@RequestBody AiScene aiScene) {
        return aiSceneService.getAiSceneById(aiScene.getId());

    }

    /**
     * 分页查询ai场景配置表
     */
    @Operation(summary = "分页查询ai场景配置表", description = "[author:50013081]")
    @RequestMapping(value = "/aiScene/getAiSceneList", method = RequestMethod.POST)
    public Result<PageList<AiAuditModel>> getAiSceneList(@RequestBody AiScene aiScene) {
        return aiSceneService.getAiSceneList(aiScene);

    }

    /**
     * 根据场景编码查询场景详情
     */
    @Operation(summary = "根据场景编码查询场景详情", description = "[author:50013081]")
    @RequestMapping(value = "/aiScene/getAiSceneByCode", method = RequestMethod.POST)
    public Result<AiScene> getAiSceneByCode(@RequestBody AiScene aiScene) {
        return aiSceneService.getAiSceneByCode(aiScene.getSceneCode());

    }
}

