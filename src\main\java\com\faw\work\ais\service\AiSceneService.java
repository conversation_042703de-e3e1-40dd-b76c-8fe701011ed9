package com.faw.work.ais.service;

import java.util.Map;
import java.util.List;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.AiAuditModel;
import com.faw.work.ais.model.AiScene;
import com.faw.work.ais.model.base.PageList;

/**
* ai场景配置表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 10:57:36
*/
public interface AiSceneService {

    /**
    * 新增或修改
    */
    public Result<Integer> insertOrUpdate(AiScene aiScene);

    /**
    * 新增
    */
    public Result<Integer> insert(AiScene aiScene);

    /**
    * 删除
    */
    public Result<Integer> delete(Long id);

    /**
    * 修改
    */
    public Result<Integer> update(AiScene aiScene);

    /**
    * 根据Id查询
    */
    public Result<AiScene> getAiSceneById(Long id);

   /**
    * 分页全部查询
    */
    public Result<PageList<AiAuditModel>> getAiSceneList(AiScene aiScene);


    /**
     * 根据场景编码查询场景信息
     * @param sceneCode 场景编码
     * @return 场景对象
     */
    Result<AiScene> getAiSceneByCode(String sceneCode);
}

