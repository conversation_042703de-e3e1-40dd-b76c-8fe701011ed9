package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 养狗话术知识入库请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "养狗话术知识查询请求对象")
public class FaqLingKnowledgeQueryRequest {

    @NotBlank(message = "问题不能为空")
    @Schema(description = "问题")
    private String question;

    @NotNull(message = "返回数量不能为空")
    @Schema(description =  "返回数量")
    private Integer topK;

}
