package com.faw.work.ais.aic.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.faw.work.ais.aic.model.domain.RagCategoryPO;
import com.faw.work.ais.aic.model.dto.FaqKnowledgeExcelDataDTO;
import com.faw.work.ais.aic.model.request.FaqKnowledgeRequest;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.aic.service.RagCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * FAQ知识Excel导入监听器
 *
 * <AUTHOR> Assistant
 */
@Slf4j
public class FaqKnowledgeExcelListener extends AnalysisEventListener<FaqKnowledgeExcelDataDTO> {

    /**
     * 批量处理的大小
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 数据列表
     */
    private final List<FaqKnowledgeExcelDataDTO> dataList = new ArrayList<>();

    /**
     * FAQ知识服务
     */
    private final FaqKnowledgeService faqKnowledgeService;

    private final RagCategoryService ragCategoryService;

    /**
     * 成功导入数量
     */
    private int successCount = 0;

    /**
     * 失败导入数量
     */
    private int failCount = 0;

    /**
     * 错误信息列表
     */
    private final List<String> errorMessages = new ArrayList<>();

    /**
     * 构造函数
     *
     * @param faqKnowledgeService FAQ知识服务
     */
    public FaqKnowledgeExcelListener(FaqKnowledgeService faqKnowledgeService, RagCategoryService ragCategoryService) {
        this.faqKnowledgeService = faqKnowledgeService;
        this.ragCategoryService = ragCategoryService;
    }

    @Override
    public void invoke(FaqKnowledgeExcelDataDTO data, AnalysisContext context) {
        // 添加到数据列表
        dataList.add(data);

        // 达到BATCH_SIZE时，进行批量处理
        if (dataList.size() >= BATCH_SIZE) {
            saveData();
            // 清空列表
            dataList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余数据
        saveData();
        log.info("Excel导入完成，成功导入{}条，失败{}条", successCount, failCount);
    }

    /**
     * 保存数据
     */
    private void saveData() {
        if (dataList.isEmpty()) {
            return;
        }

        // 用于批量保存的请求列表
        List<FaqKnowledgeRequest> requestList = new ArrayList<>();

        // 先进行数据校验，将有效数据添加到请求列表
        int currentRow = successCount + failCount + 1;
        for (FaqKnowledgeExcelDataDTO data : dataList) {
            try {
                // 数据校验
                if (StringUtils.isBlank(data.getCategoryId())) {
                    failCount++;
                    errorMessages.add("第" + currentRow + "行：类目ID不能为空");
                    currentRow++;
                    continue;
                }
                if (StringUtils.isBlank(data.getQuestion())) {
                    failCount++;
                    errorMessages.add("第" + currentRow + "行：问题不能为空");
                    currentRow++;
                    continue;
                }
                if (StringUtils.isBlank(data.getAnswer())) {
                    failCount++;
                    errorMessages.add("第" + currentRow + "行：答案不能为空");
                    currentRow++;
                    continue;
                }

                // 获取类目信息
                RagCategoryPO category = ragCategoryService.getById(data.getCategoryId());
                if (category == null) {
                    failCount++;
                    errorMessages.add("第" + currentRow + "行：类目ID不存在");
                    currentRow++;
                    continue;
                }

                // 构建请求对象
                FaqKnowledgeRequest request = new FaqKnowledgeRequest();
                request.setCategoryName(category.getName());
                request.setQuestion(data.getQuestion());
                request.setAnswer(data.getAnswer());

                // 添加到批量请求列表
                requestList.add(request);
                currentRow++;
            } catch (Exception e) {
                failCount++;
                errorMessages.add("第" + currentRow + "行：数据校验失败，原因：" + e.getMessage());
                log.error("导入FAQ知识数据校验失败", e);
                currentRow++;
            }
        }

        // 批量保存有效数据
        if (!requestList.isEmpty()) {
            try {
                // 使用批量保存方法
                int savedCount = faqKnowledgeService.batchSaveKnowledgeWithVector(requestList);
                successCount += savedCount;
                log.info("批量保存FAQ知识成功，数量: {}", savedCount);
            } catch (Exception e) {
                // 批量保存失败，所有数据都算作失败
                failCount += requestList.size();
                errorMessages.add("批量保存失败，原因：" + e.getMessage());
                log.error("批量保存FAQ知识失败", e);
            }
        }
    }

    /**
     * 获取成功导入数量
     *
     * @return 成功导入数量
     */
    public int getSuccessCount() {
        return successCount;
    }

    /**
     * 获取失败导入数量
     *
     * @return 失败导入数量
     */
    public int getFailCount() {
        return failCount;
    }

    /**
     * 获取错误信息列表
     *
     * @return 错误信息列表
     */
    public List<String> getErrorMessages() {
        return errorMessages;
    }
}
