package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智联APP 响应参数类
 *
 * <AUTHOR>
 * @since 2025-06-25 11:04
 */
@Data
@Schema(description = "智联APP 响应参数类")
public class AppResponse<T> {

    /**
     * 状态码：1成功，其他为失败
     */
    @Schema(description = "状态码：1成功，其他为失败")
    private String code;

    /**
     * 成功为success，其他为失败原因
     */
    @Schema(description = "成功为success，其他为失败原因")
    private String msg;

    /**
     * 数据结果集
     */
    @Schema(description = "数据结果集")
    private T data;

}
