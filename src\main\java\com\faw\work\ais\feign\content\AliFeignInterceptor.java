package com.faw.work.ais.feign.content;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

/**
 * 阿里云Feign调用 拦截器
 *
 * <AUTHOR>
 * @since 2025-05-16 10:22
 */
@Slf4j
public class AliFeignInterceptor implements RequestInterceptor {

    private static final String HEADER_TOKEN_KEY = "Authorization";

    private static final String HEADER_TYPE_KEY = "Content-Type";

    private static final String HEADER_TYPE_VALUE = "application/json";


    @Value("${spring.ai.dashscope.content-key:}")
    private String headerTokenValue;


    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header(HEADER_TYPE_KEY, HEADER_TYPE_VALUE);
        requestTemplate.header(HEADER_TOKEN_KEY, headerTokenValue);
    }

}
