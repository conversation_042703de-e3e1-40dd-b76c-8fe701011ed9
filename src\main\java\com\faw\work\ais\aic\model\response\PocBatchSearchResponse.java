package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * POC批量搜索响应对象
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "POC批量搜索响应对象")
public class PocBatchSearchResponse {

    @Schema(description = "总处理数量")
    private Integer totalCount;

    @Schema(description = "成功数量")
    private Integer successCount;

    @Schema(description = "失败数量")
    private Integer failCount;

    @Schema(description = "搜索结果列表")
    private List<SearchResult> searchResults;

    /**
     * 单个搜索结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "单个搜索结果")
    public static class SearchResult {

        @Schema(description = "行索引")
        private Integer rowIndex;

        @Schema(description = "查询内容")
        private String query;

        @Schema(description = "是否成功")
        private Boolean success;

        @Schema(description = "错误信息")
        private String errorMessage;

        @Schema(description = "知识数量")
        private Integer knowledgeCount;

        @Schema(description = "知识列表")
        private List<FaqKnowledgeResponse> knowledgeList;
    }
}
