package com.faw.work.ais.entity.vo.ai;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI任务返回结果
 */

@Schema(description = "AI任务返回结果")
@Data
public class AiTaskResultVO {

    @Schema(description = "同上")
    private String id;

    /**
     * 文件原始url列表
     */
    @Schema(description = "文件原始url列表")
    private String fileRawList;

    /**
     * COS文件id
     */
    @Schema(description = "COS文件id")
    private String fileId;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private String taskType;

    /**
     * 任务结果
     */
    @Schema(description = "任务结果")
    private String taskResult;

    /**
     * 文件类型0表示文档1表示图片
     */
    @Schema(description = "文件类型0表示文档1表示图片")
    private String contentType;

    /**
     * 文档版本号
     */
    @Schema(description = "文档版本号")
    private String version;

    /**
     * 系统id
     */
    @Schema(description = "系统id")
    private String systemId;

    /**
     * 任务的状态0已收到，1AI已处理，2表示未回调，3表示已回调
     */
    @Schema(description = "任务的状态0已收到，1AI已处理，2表示未回调，3表示已回调")
    private Integer taskStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 提示词
     */
    @Schema(description = "提示词")
    private String promot;

    /**
     * 业务主键
     */
    @Schema(description = "业务主键")
    private String bizId;

}
