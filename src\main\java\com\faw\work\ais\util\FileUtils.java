package com.faw.work.ais.util;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.List;

/**
 * 文件工具类，提供常用的文件读写操作。
 * 该类使用现代的 java.nio.file API。
 */
public final class FileUtils {

    /**
     * 私有构造函数，防止类被实例化。
     */
    private FileUtils() {
        // 私有构造函数，阻止实例化
    }

    /**
     * 从文件中读取所有行，并返回一个字符串列表。
     *
     * @param filePath 文件的路径。
     * @return 包含文件中所有行的字符串列表。
     * @throws IOException 如果发生 I/O 错误。
     */
    public static List<String> readAllLines(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        return Files.readAllLines(path);
    }

    /**
     * 将字符串列表写入文件，如果文件已存在则覆盖其内容。
     *
     * @param filePath 文件的路径。
     * @param lines    要写入的行列表。
     * @throws IOException 如果发生 I/O 错误。
     */
    public static void writeLines(String filePath, List<String> lines) throws IOException {
        Path path = Paths.get(filePath);
        Files.write(path, lines, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
    }

    /**
     * 将字符串列表追加到现有文件的末尾。
     *
     * @param filePath 文件的路径。
     * @param lines    要追加的行列表。
     * @throws IOException 如果发生 I/O 错误。
     */
    public static void appendLines(String filePath, List<String> lines) throws IOException {
        Path path = Paths.get(filePath);
        Files.write(path, lines, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
    }
}