package com.faw.work.ais.aic.feign;

import com.faw.work.ais.aic.feign.dto.DgwResult;
import com.faw.work.ais.aic.model.request.AfterEmotionCallbackRequest;
import com.faw.work.ais.feign.FeignClientConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 售后接待情绪价值模型回调Feign客户端
 * <AUTHOR>
 */
@FeignClient(value = "AfterEmotionCallbackFeignClient",url = "${feign.coc.hostUrl:}",configuration = {FeignClientConfig.class, AicAuthFeignInterceptor.class})
public interface AfterEmotionCallbackFeignClient {

    /**
     * 售后接待情绪价值模型回调
     *
     * @param request 回调请求
     * @return 回调结果
     */
    @PostMapping("/serviceInspection/1234")
    DgwResult callBackAfterEmotion(@RequestBody AfterEmotionCallbackRequest request);
}
