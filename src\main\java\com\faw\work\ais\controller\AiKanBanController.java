package com.faw.work.ais.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.AiKanBanDTO;
import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.dto.ai.TaskRuleDTO;
import com.faw.work.ais.entity.vo.ai.*;
import com.faw.work.ais.model.TaskRule;
import com.faw.work.ais.service.KanBanService;
import com.faw.work.ais.service.TaskRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Schema(description = "AI看板")
@Slf4j
@RestController("AiKanBanController")
@RequestMapping("/kanBan")
public class AiKanBanController {

    @Autowired
    private KanBanService kanBanService;
    @Autowired
    private TaskRuleService taskRuleService;

    @Operation(summary = "系统下拉列表接口", description = "[author:10236535]")
    @PostMapping(value = "/getSystemInfo")
    public Response<List<SystemVO>> getSystemInfo() {
        List<SystemVO> systemVOS = kanBanService.getSystemInfo();
        return Response.success(systemVOS);
    }

    @Operation(summary = "规则名称下拉列表接口", description = "[author:10236535]")
    @PostMapping(value = "/getTaskRules")
    public Response<List<TaskRuleVO>> getTaskRules() {
        List<TaskRuleVO> taskRules = kanBanService.getTaskRule();
        return Response.success(taskRules);
    }

    @Operation(summary = "根据traceId获取文件列表信息", description = "[author:10236535]")
    @PostMapping(value = "/getfileInfosByTraceId")
    public Response<List<BeCheckFileInfoVO>> getfileInfosByTraceId(@RequestBody @Valid FileDTO fileDTO){
        List<BeCheckFileInfoVO> list = kanBanService.getfileInfosByTraceId(fileDTO);
        return Response.success(list);
    }

    @Operation(summary = "智能准确率看板", description = "[author:10236535]")
    @PostMapping(value = "/aiRateShow")
    public Response<KanbanTopDataVO> aiRateShow(@RequestBody @Valid AiKanBanDTO aiKanBanDTO){
        KanbanTopDataVO kanbanInfo = kanBanService.getAiCheckDetails(aiKanBanDTO);
        return Response.success(kanbanInfo);
    }

    @Operation(summary = "智能准确率趋势图", description = "[author:10236535]")
    @PostMapping(value = "/aiTrendChard")
    public Response<List<RateTrendChartVO>> aiTableShow(@RequestBody @Valid AiKanBanDTO aiKanBanDTO){
        List<RateTrendChartVO> list = kanBanService.getTrendChard(aiKanBanDTO);
        return Response.success(list);
    }

    @Operation(summary = "根据traceId获取python入参", description = "[author:10236535]")
    @PostMapping(value = "/getPythonInParamByTraceId")
    public Response<String> getPythonInParamByTraceId(@RequestBody @Valid FileDTO fileDTO){
        String pythonInParamByTraceId = kanBanService.getPythonInParamByTraceId(fileDTO);
        return Response.success(pythonInParamByTraceId);
    }
    @Operation(summary = "智能规则覆盖查询", description = "[author:10236535]")
    @PostMapping(value = "/aiRuleCoverageList")
    public Response<IPage<TaskRule>> aiRuleCoverageList(@RequestBody @Valid TaskRuleDTO taskRuleDTO){
        QueryWrapper<TaskRule> ruleQueryWrapper = new QueryWrapper<>();
        Page<TaskRule> page = new Page<>(taskRuleDTO.getCurrentPage(), taskRuleDTO.getPageSize());
        IPage<TaskRule> listTaskRule=taskRuleService.page(page,ruleQueryWrapper.lambda()
                .like(TaskRule::getTaskName,taskRuleDTO.getTaskName())
                .eq(TaskRule::getId,taskRuleDTO.getId())
                .eq(TaskRule::getSystemId,taskRuleDTO.getSystemId()));
        return Response.success(listTaskRule);
    }
    @Operation(summary = "智能规则覆盖保存", description = "[author:10236535]")
    @PostMapping(value = "/aiRuleCoverageSaveList")
    public Response<Boolean> aiRuleCoverageSave(@RequestBody @Valid List<TaskRule> list){
        Boolean result= taskRuleService.saveOrUpdateBatch(list);
        return Response.success(result);
    }
}
