package com.faw.work.ais.model.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 智能问答聊天记录详情
 *
 * <AUTHOR>
 * @since 2025-04-27 10:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "智能问答聊天记录详情表")
public class AppChatHistoryDetail {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 聊天记录ID
     */
    @Schema(description = "聊天记录ID")
    private Long chatId;

    /**
     * 类型（1-AI，0-用户）
     */
    @Schema(description = "类型（1-AI，0-用户）")
    private Integer type;

    /**
     * 聊天内容
     */
    @Schema(description = "聊天内容")
    private String chatText;

    /**
     * 点赞标识（1-点赞，0-点踩）
     */
    @Schema(description = "点赞标识（1-点赞，0-点踩）")
    private Integer likeFlag;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private String createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private String updateTime;

}
