package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发布状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PublishStatusEnum {
    /**
     * 未发布
     */
    UNPUBLISHED("00", "未发布"),
    /**
     * 已发布
     */
    PUBLISHED("01", "已发布");

    private final String code;
    private final String desc;

    public static PublishStatusEnum getByCode(String code) {
        for (PublishStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNPUBLISHED;
    }
} 