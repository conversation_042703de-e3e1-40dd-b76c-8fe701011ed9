package com.faw.work.ais.service;

import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.entity.dto.ContentSearchResult;
import io.milvus.v2.service.vector.response.InsertResp;

import java.util.List;

public interface ContentMilvusService {


    /**
     * 根据向量查询最相似的内容（用于评论）
     *
     * @param collectionName 集合名称
     * @param embedding      查询向量
     * @param topK           返回的结果数量
     * @return 包含ID和相似度分数的向量搜索结果列表
     */
    List<ContentSearchResult> searchByEmbedding(String collectionName, float[] embedding, int topK, float similarityThreshold, String filterString);

    /**
     * 存储评论内容向量（用于评论）
     * @param collectionName 集合名称
     * @param embedding 查询向量
     * @return 包含ID和相似度分数的向量搜索结果列表*
     */
    InsertResp storeContentEmbedding(String collectionName, Long vectorId, List<MilvusField> properties, float[] embedding);

}
