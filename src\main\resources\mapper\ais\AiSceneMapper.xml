<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiSceneDao">

    <resultMap id="AiScene" type="com.faw.work.ais.model.AiScene" >
        <result column="id" property="id" />
        <result column="scene_code" property="sceneCode" />
        <result column="scene_name" property="sceneName" />
        <result column="l3_process_code" property="l3ProcessCode" />
        <result column="l3_process_name" property="l3ProcessName" />
        <result column="l4_process_code" property="l4ProcessCode" />
        <result column="l4_process_name" property="l4ProcessName" />
        <result column="biz_unit_code" property="bizUnitCode" />
        <result column="biz_unit_name" property="bizUnitName" />
        <result column="call_back_url" property="callBackUrl" />
        <result column="reject_num" property="rejectNum" />
        <result column="comparison_results" property="comparisonResults" />
        <result column="create_time" property="createTime" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_code" property="updateUserCode" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `scene_code`,
        `scene_name`,
        `l3_process_code`,
        `l3_process_name`,
        `l4_process_code`,
        `l4_process_name`,
        `biz_unit_code`,
        `biz_unit_name`,
        `call_back_url`,
        `reject_num`,
        `comparison_results`,
        `create_time`,
        `create_user_code`,
        `create_user_name`,
        `update_time`,
        `update_user_code`,
        `update_user_name`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_scene (
            `scene_code`,
            `scene_name`,
            `l3_process_code`,
            `l3_process_name`,
            `l4_process_code`,
            `l4_process_name`,
            `biz_unit_code`,
            `biz_unit_name`,
            `call_back_url`,
            `reject_num`,
            `comparison_results`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES(
            #{aiScene.sceneCode},
            #{aiScene.sceneName},
            #{aiScene.l3ProcessCode},
            #{aiScene.l3ProcessName},
            #{aiScene.l4ProcessCode},
            #{aiScene.l4ProcessName},
            #{aiScene.bizUnitCode},
            #{aiScene.bizUnitName},
            #{aiScene.callBackUrl},
            #{aiScene.rejectNum},
            #{aiScene.comparisonResults},
            #{aiScene.createUserCode},
            #{aiScene.createUserName},
            #{aiScene.updateUserCode},
            #{aiScene.updateUserName}
        )
    </insert>

    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM ai_scene
        where  id = #{id}

    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE ai_scene
         <set>
            <if test="aiScene.sceneCode != null and aiScene.sceneCode != '' " >
                scene_code = #{aiScene.sceneCode},
            </if>
            <if test="aiScene.sceneName != null and aiScene.sceneName != '' " >
                scene_name = #{aiScene.sceneName},
            </if>
            <if test="aiScene.l3ProcessCode != null and aiScene.l3ProcessCode != '' " >
                l3_process_code = #{aiScene.l3ProcessCode},
            </if>
            <if test="aiScene.l3ProcessName != null and aiScene.l3ProcessName != '' " >
                l3_process_name = #{aiScene.l3ProcessName},
            </if>
            <if test="aiScene.l4ProcessCode != null and aiScene.l4ProcessCode != '' " >
                l4_process_code = #{aiScene.l4ProcessCode},
            </if>
            <if test="aiScene.l4ProcessName != null and aiScene.l4ProcessName != '' " >
                l4_process_name = #{aiScene.l4ProcessName},
            </if>
            <if test="aiScene.bizUnitCode != null and aiScene.bizUnitCode != '' " >
                biz_unit_code = #{aiScene.bizUnitCode},
            </if>
            <if test="aiScene.bizUnitName != null and aiScene.bizUnitName != '' " >
                biz_unit_name = #{aiScene.bizUnitName},
            </if>
            <if test="aiScene.callBackUrl != null and aiScene.callBackUrl != '' " >
                call_back_url = #{aiScene.callBackUrl},
            </if>
            <if test="aiScene.rejectNum !=  null" >
                reject_num = #{aiScene.rejectNum},
            </if>
            <if test="aiScene.comparisonResults != null" >
                comparison_results = #{aiScene.comparisonResults},
            </if>


            <if test="aiScene.updateUserCode != null and aiScene.updateUserCode != '' " >
                update_user_code = #{aiScene.updateUserCode},
            </if>
            <if test="aiScene.updateUserName != null and aiScene.updateUserName != '' " >
                update_user_name = #{aiScene.updateUserName},
            </if>
         </set>
          where  id = #{aiScene.id}

    </update>


    <select id="getAiSceneList" resultMap="AiScene">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_scene
        <where>
          <if test="aiScene.id != null  " >
              AND  id = #{aiScene.id}
          </if>
          <if test="aiScene.sceneCode != null and aiScene.sceneCode != '' " >
              AND  scene_code = #{aiScene.sceneCode}
          </if>
          <if test="aiScene.sceneName != null and aiScene.sceneName != '' " >
              AND  scene_name   like concat('%',#{aiScene.sceneName},'%')
          </if>
          <if test="aiScene.l3ProcessCode != null and aiScene.l3ProcessCode != '' " >
              AND  l3_process_code = #{aiScene.l3ProcessCode}
          </if>
          <if test="aiScene.l3ProcessName != null and aiScene.l3ProcessName != '' " >
              AND  l3_process_name = #{aiScene.l3ProcessName}
          </if>
          <if test="aiScene.l4ProcessCode != null and aiScene.l4ProcessCode != '' " >
              AND  l4_process_code = #{aiScene.l4ProcessCode}
          </if>
          <if test="aiScene.l4ProcessName != null and aiScene.l4ProcessName != '' " >
              AND  l4_process_name = #{aiScene.l4ProcessName}
          </if>
          <if test="aiScene.bizUnitCode != null and aiScene.bizUnitCode != '' " >
              AND  biz_unit_code = #{aiScene.bizUnitCode}
          </if>
          <if test="aiScene.bizUnitName != null and aiScene.bizUnitName != '' " >
              AND  biz_unit_name = #{aiScene.bizUnitName}
          </if>
          <if test="aiScene.callBackUrl != null and aiScene.callBackUrl != '' " >
              AND  call_back_url = #{aiScene.callBackUrl}
          </if>
          <if test="aiScene.rejectNum != null " >
              AND  reject_num = #{aiScene.rejectNum}
          </if>
          <if test="aiScene.comparisonResults != null  " >
              AND  comparison_results = #{aiScene.comparisonResults}
          </if>

        </where>
        order by id desc
     </select>

    <select id="getAiSceneById" parameterType="java.util.Map" resultMap="AiScene">
        SELECT <include refid="Base_Column_List" />
        FROM ai_scene

        where id = #{id}

    </select>

    <select id="getExitNameAiSceneList" resultMap="AiScene">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_scene
        where scene_name = #{aiScene.sceneName}
            <if test="aiScene.id != null  " >
                AND  id != #{aiScene.id}
            </if>
    </select>

    <select id="getExitCodeAiSceneList" resultMap="AiScene">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_scene
        where
        scene_code = #{aiScene.sceneCode}
            <if test="aiScene.id != null  " >
                AND  id != #{aiScene.id}
            </if>

    </select>

    <select id="getAiSceneByCode" resultMap="AiScene">
      SELECT
      <include refid="Base_Column_List" />
      FROM ai_scene
      where
      scene_code = #{sceneCode}
      limit 1
    </select>

</mapper>

