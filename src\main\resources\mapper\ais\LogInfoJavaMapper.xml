<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.LogInfoJavaMapper">
  <resultMap id="BaseResultMap" type="com.faw.work.ais.model.LogInfoJava">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="java_call_time" jdbcType="TIMESTAMP" property="javaCallTime" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.faw.work.ais.model.LogInfoJava">
    <result column="java_param" jdbcType="LONGVARCHAR" property="javaParam" />
  </resultMap>
  <sql id="Base_Column_List">
    id, system_id, java_call_time, batch_id, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    java_param
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from log_info_java_new
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from log_info_java_new
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.LogInfoJava" useGeneratedKeys="true">
    insert into log_info_java_new (system_id, java_call_time, batch_id,
      create_time, update_time, java_param
      )
    values (#{systemId,jdbcType=VARCHAR}, #{javaCallTime,jdbcType=TIMESTAMP}, #{batchId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{javaParam,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.LogInfoJava" useGeneratedKeys="true">
    insert into log_info_java_new
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        system_id,
      </if>
      <if test="javaCallTime != null">
        java_call_time,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="javaParam != null">
        java_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="javaCallTime != null">
        #{javaCallTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="javaParam != null">
        #{javaParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.faw.work.ais.model.LogInfoJava">
    update log_info_java_new
    <set>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="javaCallTime != null">
        java_call_time = #{javaCallTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="javaParam != null">
        java_param = #{javaParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.faw.work.ais.model.LogInfoJava">
    update log_info_java_new
    set system_id = #{systemId,jdbcType=VARCHAR},
      java_call_time = #{javaCallTime,jdbcType=TIMESTAMP},
      batch_id = #{batchId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      java_param = #{javaParam,jdbcType=LONGVARCHAR}
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.faw.work.ais.model.LogInfoJava">
    update log_info_java_new
    set system_id = #{systemId,jdbcType=VARCHAR},
      java_call_time = #{javaCallTime,jdbcType=TIMESTAMP},
      batch_id = #{batchId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id}
  </update>

  <select id="getInfoByBatchId" resultType="com.faw.work.ais.model.LogInfoJava">
    select
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
    from log_info_java_new
      <where>
        <if test="batchId != null">
          AND batch_id = #{batchId}
        </if>
      </where>
  </select>

  <select id="getInfoByBatchIds" resultType="com.faw.work.ais.model.LogInfoJava">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from log_info_java_new
    <where>
      <if test = "batchIds != null and batchIds.size > 0 ">
        AND batch_id IN
        <foreach collection="batchIds" item="batchId" open="(" separator="," close=")">
          #{batchId}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getJavaLogByBatchId" resultType="java.lang.String">
    SELECT
     t.java_param
    FROM log_info_java_new t
    <where>
      <if test="batchId != null">
        AND batch_id = #{batchId}
      </if>
    </where>
  </select>

</mapper>