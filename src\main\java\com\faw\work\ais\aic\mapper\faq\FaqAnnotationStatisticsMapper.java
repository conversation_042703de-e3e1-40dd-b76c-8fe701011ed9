package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationStatisticsPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FAQ标注统计Mapper接口
 * 提供FAQ标注统计相关的数据库操作
 *
 * <AUTHOR>
 */
@Mapper
public interface FaqAnnotationStatisticsMapper extends BaseMapper<FaqAnnotationStatisticsPO> {

    /**
     * 根据任务ID查询统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息列表
     */
    List<FaqAnnotationStatisticsPO> findByTaskId(@Param("taskId") String taskId);

    /**
     * 更新或插入统计信息
     *
     * @param taskId 任务ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @param count 数量
     * @return 影响的记录数
     */
    int upsertStatistics(@Param("taskId") String taskId,
                        @Param("annotationType") String annotationType,
                        @Param("annotationSubtype") String annotationSubtype,
                        @Param("count") Integer count);

    /**
     * 删除任务的所有统计信息
     *
     * @param taskId 任务ID
     * @return 删除的记录数
     */
    int deleteByTaskId(@Param("taskId") String taskId);

    /**
     * 批量插入统计信息
     *
     * @param statistics 统计信息列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("statistics") List<FaqAnnotationStatisticsPO> statistics);

    /**
     * 重新计算并更新任务的统计信息
     *
     * @param taskId 任务ID
     * @return 影响的记录数
     */
    int recalculateStatistics(@Param("taskId") String taskId);
}
