package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * RAG知识库配置请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "RAG知识库配置请求")
public class RagKnowledgeBaseRequest {

    @Schema(description = "知识库ID")
    private Long id;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "数据类型（00-非结构文档pdf doc ，01-结构化文档 excel）")
    private String dataType;

    @Schema(description = "是否改写上下文（0-不 1-改写）")
    private String rewriteQuery;

    @Schema(description = "Embedding模型名称")
    private String embeddingModel;

    @Schema(description = "Embedding端点")
    private String embeddingUrl;

    @Schema(description = "是否使用rerank")
    private String rerankOpen;

    @Schema(description = "重排序模型")
    private String rerankModel;

    @Schema(description = "重排序模型端点")
    private String rerankUrl;

    @Schema(description = "相似度阈值(0-1)")
    private Double similarityThreshold;
} 