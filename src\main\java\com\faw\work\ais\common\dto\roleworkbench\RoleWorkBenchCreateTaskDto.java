package com.faw.work.ais.common.dto.roleworkbench;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 角色工作台创建任务参数实体类
 * <AUTHOR>
 */
@Data
@Schema(description = "角色工作台创建任务参数实体类")
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class RoleWorkBenchCreateTaskDto {

    @Schema(description = "事件编码,对应唯一业务单元编码 String(0-64)")
    private String eventCode;

    @Schema(description = "能力中心ClientId, String(0-64),获取位置：工作流管理中心->注册能力中心->App key")
    private String clientId;

    @Schema(description = "自定义参数集合")
    private List<TriggerInfoList> triggerInfoList;

    @Schema(description = "自定义url路由")
    private String customUrl;

    @Schema(description = "triggerInfoList这个参数为空的时候，用这个自定义Url参数, 自动拼接参数到模板配置URL或者自定义Url")
    private Map<String,String> customUrlParams;

    @Schema(description = "请求id,接口幂等参数 String(0-64)")
    private String requestId;

    @Schema(description = "上游业务单元,能力中心侧业务ID, String(20-64),  (起始任务-可空;), (中间任务:preTaskInstanceCode-优先查找," +
            "preBizId-其次查找, 必填其中之一查询任务流实例山下文并创建当前业务单元任务实例);")
    @Size(min = 20, max = 64, message = "preBizId字符串长度限制为20-64")
    private String preBizId;

    @Schema(description = "任务创建人,云原生用户name, String(0-64), 不传默认system")
    private String userName;

    @Schema(description = "当前业务单元编码, String(0-64)")
    private String bizUnitCode;

    @Schema(description = "上游业务单元任务实例编码, String(0-64), (起始任务-可空;), (中间任务:preTaskInstanceCode-优先查找, preBizId-其次查找," +
            "必填其中之一查询任务流实例山下文并创建当前业务单元任务实例);")
    private String preTaskInstanceCode;

    @Schema(description = "任务创建人,云原生用户code, String(0-64), 不传默认system")
    private String userCode;


    @Data
    public static class TriggerInfoList {

        @Schema(description = "自定义Url")
        private String customUrl;

        @Schema(description = "自定义Url参数, 自动拼接参数到模板配置URL或者自定义Url")
        private Map<String,String> customUrlParams;

        @Schema(description = "能力中心侧业务ID, String(20-64), 业务单元下所有任务实例唯一")
        @Size(min = 20, max = 64, message = "bizId字符串长度限制为20-64")
        private String bizId;

        @Schema(description = "创建任务描述, String(0-500), (触发器-可空;)")
        private String description;

        @Schema(description = "自定义逾期天数配置索引, 取值(1,2,3) ")
        private Integer customOverdueDaysIndex;

        @Schema(description = "云原生用户code, String(0-64)，必填")
        private String userCode;
    }

}


