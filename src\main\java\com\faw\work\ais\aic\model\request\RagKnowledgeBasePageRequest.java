package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * RAG知识库配置分页请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "RAG知识库配置分页请求")
public class RagKnowledgeBasePageRequest {

    @Schema(description = "知识库ID")
    private Long id;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "数据类型（00-非结构文档pdf doc ，01-结构化文档 excel）")
    private String dataType;
    
    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页数量", defaultValue = "10")
    private Integer pageSize = 10;
} 