package com.faw.work.ais.entity.vo.ai;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "AI Python 任务 VO")
public class AiPythonTaskVO {

    @Schema(description = "家")
    @JSONField(name = "isT")
    private boolean isT;

    @Schema(description = "解释")
    private String explanation;
    public boolean getIsT() {
        return isT;
    }
    public boolean isT() {
        return isT;
    }

    public void setT(boolean t) {
        isT = t;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }
}
