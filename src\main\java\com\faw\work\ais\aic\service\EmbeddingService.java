package com.faw.work.ais.aic.service;

import com.alibaba.cloud.ai.model.RerankRequest;
import com.alibaba.cloud.ai.model.RerankResponse;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;

import java.util.List;

/**
 * 向量嵌入服务接口
 *
 * <AUTHOR>
 */
public interface EmbeddingService {

    /**
     * 获取单个文本的向量嵌入表示 (使用默认模型)
     *
     * @param text 输入文本
     * @return 返回文本对应的向量列表
     */
    float[] getEmbedding(String text);

    /**
     * 获取单个文本的向量嵌入表示 (指定模型类型)
     *
     * @param modelType 模型类型枚举 {@link EmbeddingModelTypeEnum}
     * @param text 输入文本
     * @return 返回文本对应的向量列表
     */
    float[] getEmbeddingByModel(EmbeddingModelTypeEnum modelType, String text);


    /**
     * 获取多个文本的向量嵌入表示列表
     * 不建议文本的数量超过100，否则可能出现内存溢出
     *
     * @param modelType 模型类型枚举 {@link EmbeddingModelTypeEnum}
     * @param textList  输入文本列表
     * @return 返回每个文本对应的EmbeddingProperty列表，包含文本和对应的向量
     */
    List<EmbeddingPropertyDTO> getEmbeddingList(EmbeddingModelTypeEnum modelType,List<String> textList);


    /**
     * 获取文本重排序结果
     *
     * @param request 文本重排序请求 {@link RerankRequest}
     * @return {@link RerankResponse } 文本重排序响应
     */
    RerankResponse getReRankResult(RerankRequest request);
}
