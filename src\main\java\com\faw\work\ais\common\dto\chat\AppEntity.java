package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 一键维保响应实体
 *
 * <AUTHOR>
 * @since 2025-06-25 11:22
 */
@Data
@Builder
@Schema(description = "一键维保响应实体")
public class AppEntity {

    /**
     * 经销商编码
     */
    @Schema(description = "经销商编码")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @Schema(description = "经销商名称")
    private String dealerName;

    /**
     * 最近进厂经销商代码
     */
    @Schema(description = "最近进厂经销商代码")
    private String lastEnterDealerCode;

    /**
     * 最近进厂经销商名称
     */
    @Schema(description = "最近进厂经销商名称")
    private String lastEnterDealerName;

    /**
     * 最近进厂时间
     */
    @Schema(description = "最近进厂时间")
    private String lastEnterTime2;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String vehicleNumber;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phoneNumber;

    /**
     * 剩余工位
     */
    @Schema(description = "剩余工位")
    private Integer number;

    /**
     * 预约日期
     */
    @Schema(description = "预约日期")
    private String appointmentDate;

    /**
     * 预约时间
     */
    @Schema(description = "预约时间")
    private String appointmentDateTime;

    /**
     * 车辆vin
     */
    @Schema(description = "车辆vin")
    private String vin;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String aid;

    /**
     * 车系
     */
    @Schema(description = "车系")
    private String carSeries;

    /**
     * 车辆里程
     */
    @Schema(description = "车辆里程")
    private String mileage;

    /**
     * 距上次保养里程
     */
    @Schema(description = "距上次保养里程")
    private String mileageEnd;

    /**
     * 上次进厂时间
     */
    @Schema(description = "上次进厂时间")
    private String lastEnterTime;

    /**
     * 省份编码
     */
    @Schema(description = "省份编码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @Schema(description = "省份名称")
    private String provinceName;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String cityCode;

    /**
     * 城市名称
     */
    @Schema(description = "城市名称")
    private String cityName;

    /**
     * 预约时间
     */
    @Schema(description = "预约时间")
    private String appointmentTime;

}