package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.request.AfterEmotionRequest;

/**
 * 售后接待情绪价值模型服务接口
 * <AUTHOR>
 */
public interface AfterEmotionService {

    /**
     * 处理售后接待情绪分析请求
     *
     * @param request 售后接待情绪分析请求
     */
    AiResult<String> processAfterEmotion(AfterEmotionRequest request);

    /**
     * 处理分片数据
     *
     * @param requestId 请求ID
     * @return 处理结果
     */
    boolean processAfterEmotionSlice(String requestId);

    /**
     * 更新状态（新事务）
     *
     * @param id     记录ID
     * @param status 状态
     * @param remark 备注
     */
    void updateStatusTran(Long id, String status, String remark);
}
