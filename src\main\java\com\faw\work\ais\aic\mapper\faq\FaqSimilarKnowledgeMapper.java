package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 相似问Mapper接口
 * 用于访问和操作FAQ相似问题数据表。
 */
@Mapper
public interface FaqSimilarKnowledgeMapper extends BaseMapper<FaqSimilarKnowledgePO> {

    /**
     * 根据原问题ID查询相似问列表。
     * 用于查询与指定原问题相关的相似问题。
     *
     * @param originalId 原问题ID。
     * @return 相似问列表，包含与指定原问题相关的相似问题信息。
     */
    List<FaqSimilarKnowledgePO> selectByOriginalId(@Param("originalId") String originalId);

    /**
     * 根据原问题ID删除相似问。
     * 用于删除与指定原问题相关的所有相似问题。
     *
     * @param originalId 原问题ID。
     * @return 删除数量，表示成功删除的记录数。
     */
    int deleteByOriginalId(@Param("originalId") String originalId);

    /**
     * 根据类目ID列表查询相似问列表。
     * 用于查询指定类目下的所有相似问题。
     *
     * @param categoryIds 类目ID列表。
     * @return 相似问列表，包含指定类目下的所有相似问题信息。
     */
    List<FaqSimilarKnowledgePO> selectByCategoryIds(@Param("categoryIds") List<String> categoryIds);

    /**
     * 根据知识类目ID删除相似问记录。
     * 用于删除与指定知识类目相关的所有相似问题。
     *
     * @param categoryId 知识类目ID。
     * @return 删除数量，表示成功删除的记录数。
     */
    int deleteByKnowledgeCategoryId(@Param("categoryId") String categoryId);

    /**
     * 根据原问题ID列表查询相似问列表。
     *
     * @param originalKnowledgeIds 原问题ID列表
     * @return 相似问列表
     */
    List<FaqSimilarKnowledgePO> selectByOriginalIds(List<String> originalKnowledgeIds);

    /**
     * 根据原问题ID查询生产环境相似问列表。
     *
     * @param originalId 原问题ID
     * @return 相似问列表
     */
    List<FaqSimilarKnowledgePO> selectProdByOriginalId(String originalId);

    /**
     * 根据原问题ID列表查询相似问的ID和问题内容。
     *
     * @param originalKnowledgeIds 原问题ID列表
     * @return 相似问列表，包含ID和问题内容
     */
    List<FaqSimilarKnowledgePO> selectIdAndQuestionByIds(List<String> originalKnowledgeIds);
}
