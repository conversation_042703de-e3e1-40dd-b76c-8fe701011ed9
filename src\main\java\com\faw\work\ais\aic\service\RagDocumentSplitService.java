package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import com.faw.work.ais.aic.model.domain.RagDocumentSplitPO;
import com.faw.work.ais.aic.model.request.RagDocumentSplitAddRequest;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 文档片段表 服务接口
 *
 * <AUTHOR> Assistant
 */
public interface RagDocumentSplitService extends IService<RagDocumentSplitPO> {

    /**
     * 根据文档ID查询片段
     *
     * @param documentId 文档ID
     * @return 片段列表
     */
    List<RagDocumentSplitPO> getByDocumentId(Long documentId);

    /**
     * 批量保存文档片段
     *
     * @param documentSplits 文档片段列表
     * @return 是否成功
     */
    boolean batchSave(List<RagDocumentSplitPO> documentSplits);

    /**
     * 根据文档ID删除片段
     *
     * @param documentId 文档ID
     */
    void deleteByDocumentId(Long documentId);
    
    /**
     * 获取文档片段列表
     * 
     * @param ragDocumentSplitPO 查询条件
     * @return 文档片段列表
     */
    List<RagDocumentSplitPO> getList(RagDocumentSplitPO ragDocumentSplitPO);

    /**
     * 按应用程序id获取
     *
     * @param appId   应用ID
     * @param bizInfo 我们信息
     * @return {@link List }<{@link RagDocumentPO }>
     */
    List<RagDocumentPO> getListByAppId(@NotNull(message = "应用ID不能为空") Long appId, String bizInfo);

    /**
     * 手动添加文档分片
     *
     * @param request  添加请求
     * @param document
     * @return 是否成功
     */
    boolean addManualSplit(RagDocumentSplitAddRequest request, RagDocumentPO document);

}