package com.faw.work.ais.feign;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.CallBackDTO;
import com.faw.work.ais.feign.interceptor.IworkOpenApiFeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.net.URI;

/**
 * iwork开放api  feign调用接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "callBackBnzx", url = "${feign.openApi.hostUrl}" , configuration = IworkOpenApiFeignInterceptor.class)
public interface BnzxOpenApiFeignClient {

    /**
     * 回调接口-固定url
     *
     * @param callBackDTO 入参
     * @return Object
     */
    @PostMapping(value = "${feign.openApi.BNZX}")
    Response<Object> callBack(@RequestBody CallBackDTO callBackDTO, @RequestHeader("exportServer") String exportServer, @RequestHeader("tenantid") String tenantid, @RequestHeader("useragent") String useragent);

}
