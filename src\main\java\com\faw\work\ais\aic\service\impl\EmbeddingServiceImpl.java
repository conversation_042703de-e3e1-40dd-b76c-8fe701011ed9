package com.faw.work.ais.aic.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.ai.model.RerankModel;
import com.alibaba.cloud.ai.model.RerankRequest;
import com.alibaba.cloud.ai.model.RerankResponse;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmbeddingServiceImpl implements EmbeddingService {

    @Autowired
    private RerankModel rerankModel;
    @Autowired
    private Executor embeddingThreadPool;

    @Autowired
    private EmbeddingModel embeddingModelV3;
    @Autowired
    private EmbeddingModel embeddingModelV1;
    @Autowired
    private EmbeddingModel embeddingModelV2;
    @Autowired
    private EmbeddingModel embeddingModelV4;


    @Override
    public float[] getEmbedding(String text) {
        return embeddingModelV3.embed(text);
    }


    @Override
    public float[] getEmbeddingByModel(EmbeddingModelTypeEnum modelType, String text) {
        if (modelType == null) {
            throw new BizException("模型类型不能为空");
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V1) {
            return embeddingModelV1.embed(text);
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V2) {
            return embeddingModelV2.embed(text);
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3) {
            return embeddingModelV3.embed(text);
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V4) {
            return embeddingModelV4.embed(text);
        }
        throw new BizException("不支持的模型类型");
    }


    @Override
    public List<EmbeddingPropertyDTO> getEmbeddingList(EmbeddingModelTypeEnum modelType, List<String> textList) {

        EmbeddingModel embeddingModel = null;
        int size = 10;
        if (modelType == null) {
            throw new BizException("模型类型不能为空");
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V1) {
            embeddingModel = embeddingModelV1;
            size = 25;
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V2) {
            embeddingModel = embeddingModelV2;
            size = 25;
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3) {
            embeddingModel = embeddingModelV3;
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V4) {
            embeddingModel = embeddingModelV4;
        }

        // 按照模型要求切分批次（每批不超过10条）

        List<List<String>> batches = CollUtil.split(textList, size);

        // 创建并行任务流（保持顺序的线程安全集合）
        EmbeddingModel finalEmbeddingModel = embeddingModel;
        List<CompletableFuture<List<EmbeddingPropertyDTO>>> futures = batches.stream()
                .map(batch -> CompletableFuture.supplyAsync(
                        () -> processBatch(batch, finalEmbeddingModel),
                        embeddingThreadPool
                )).toList();

        // 合并所有批次结果
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .flatMap(future -> {
                            try {
                                return future.get().stream();
                            } catch (Exception e) {
                                throw new CompletionException("Batch processing failed", e);
                            }
                        })
                        .collect(Collectors.toList()))
                .join();
    }

    @Override
    public RerankResponse getReRankResult(RerankRequest request) {
        return rerankModel.call(request);
    }

    private List<EmbeddingPropertyDTO> processBatch(List<String> batch, EmbeddingModel embeddingModel) {

        List<float[]> embeddings = embeddingModel.embed(batch);
        List<EmbeddingPropertyDTO> batchResult = new ArrayList<>(batch.size());

        for (int i = 0; i < batch.size(); i++) {
            batchResult.add(EmbeddingPropertyDTO.builder()
                    .text(batch.get(i))
                    .embedding(embeddings.get(i))
                    .build());
        }
        return batchResult;
    }
}