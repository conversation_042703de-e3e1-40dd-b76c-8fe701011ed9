package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dingtalk.api.request.OapiCalendarListRequest;
import com.faw.work.ais.entity.dto.ai.AiTaskDTO;
import com.faw.work.ais.entity.dto.ai.BeCheckFileDTO;
import com.faw.work.ais.entity.dto.ai.RePushAiDTO;
import com.faw.work.ais.entity.vo.ai.BizUnitAndTaskRuleInfoVO;
import com.faw.work.ais.entity.vo.ai.RePushAiVO;
import com.faw.work.ais.entity.vo.ai.TaskRuleVO;
import com.faw.work.ais.mapper.ais.LogInfoJavaMapper;
import com.faw.work.ais.mapper.ais.LogInfoPythonCallbackMapper;
import com.faw.work.ais.mapper.ais.NumberEmployeeMapper;
import com.faw.work.ais.model.LogInfoJava;
import com.faw.work.ais.service.AiTaskService;
import com.faw.work.ais.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    @Autowired
    private NumberEmployeeMapper numberEmployeeMapper;

    @Autowired
    private LogInfoPythonCallbackMapper logInfoPythonCallbackMapper;

    @Autowired
    private LogInfoJavaMapper logInfoJavaMapper;

    @Autowired
    private AiTaskService aiTaskService;

    public List<BizUnitAndTaskRuleInfoVO> getBizUnitInfos(List<String> unitCodes) {
        if(CollectionUtils.isEmpty(unitCodes)){
            throw new RuntimeException("业务单元不能为空!");
        }
        List<BizUnitAndTaskRuleInfoVO> result = new ArrayList<>();
        unitCodes.stream().forEach(x->{
            if(StringUtils.isEmpty(x)){
                throw new RuntimeException("业务单元不能为空！");
            }
            List<TaskRuleVO> taskRuleVOS = numberEmployeeMapper.getTaskRuleByBizUintCode(x);
            BizUnitAndTaskRuleInfoVO bizUnitAndTaskRuleInfoVO = new BizUnitAndTaskRuleInfoVO();
            if(CollectionUtils.isNotEmpty(taskRuleVOS)){
                bizUnitAndTaskRuleInfoVO.setTaskRuleList(taskRuleVOS);
                bizUnitAndTaskRuleInfoVO.setBusinessUnitCode(x);
                result.add(bizUnitAndTaskRuleInfoVO);
            }
        });
        return result;
    }

    @Override
    public void rePushAiTask(RePushAiDTO rePushAiDTO) {
        try {
            log.info("-----------------------------------本轮补推数据开始-------------------------------------------------" );
            // 计算开始时间和结束时间
            RePushAiDTO rePushAiDTOAfterCount = countBeginAndEndDate(rePushAiDTO);
            log.info("---补推数据时间段为---从" + rePushAiDTOAfterCount.getBeginDate() + "到" + rePushAiDTOAfterCount.getEndDate());

            // 查询过去N分钟内，没有回调的数据
            List<RePushAiVO> rePushAiVOS = logInfoPythonCallbackMapper.getUnAiResultBatchIds(rePushAiDTOAfterCount);

            if(CollectionUtils.isNotEmpty(rePushAiVOS)){
                // 组装没有AI结果的数据入参；
                List<AiTaskDTO> pushVos = assimbleAiTaskDTO(rePushAiVOS);
                if(CollectionUtils.isNotEmpty(pushVos)){
                    log.info("-------开始补推数据-------，共执行" + pushVos.size() + "条数据");
                    pushVos.stream().forEach(x->{
                        aiTaskService.asyncAiTask(x);
                    });
                    log.info("---------补推数据完成--------");
                }
            }else {
                log.info("------该时间段内没有数据补推------");
            }
            log.info("-------------------------------------------本轮补推数据完成-------------------------------------------------" );
        }catch (Exception e){
            log.error("---补推数据出错----" + e.getMessage() + e.getCause(), e);
            log.error("----------------------补推数据出错,本轮补推结束！----------------------------------------" + e.getMessage() + e.getCause(), e);
        }
    }

    /**
     * 拼装过滤补推的入参；如果补推入参是二手车，需要获取二手车的图片url
     * @param rePushAiVOS
     * @return
     */
    private List<AiTaskDTO> assimbleAiTaskDTO(List<RePushAiVO> rePushAiVOS) {
        List<AiTaskDTO> aiTaskDTOS = new ArrayList<>();
        // 获取去重后的batchId集合
        List<String> batchIds = rePushAiVOS.stream().map(RePushAiVO::getBatchId).distinct().collect(Collectors.toList());
        // 根据batchId获取入参
        List<LogInfoJava> logInfoJavas = logInfoJavaMapper.getInfoByBatchIds(batchIds);
        // 过滤出要补推数据的参数原始值
        if(CollectionUtils.isNotEmpty(logInfoJavas)){
            logInfoJavas.stream().forEach(x->{
                String param = x.getJavaParam();
                if(StringUtils.isNotEmpty(param)){
                    // param 反序列化为 List<AiTaskDTO>
                     List<AiTaskDTO> aiTaskDTOs = JSON.parseArray(param, AiTaskDTO.class);
                    // 如果aiTaskDTOs的traceId属性在rePushAiVOS集合中，则添加入aiTaskDTOS集合中
                    aiTaskDTOs.stream().forEach(z->{
                        if(rePushAiVOS.stream().anyMatch(y->y.getTraceId().equals(z.getTraceId()))){
                            aiTaskDTOS.add(z);
                        }
                    });
                }
            });
        }
        // 将原始值中二手车图片重新组装(因为会过期)
//        if(CollectionUtils.isNotEmpty(aiTaskDTOS)){
//            aiTaskDTOS.stream().forEach(x->{
//                if(x.getSystemId().equals("YSZC-ESC")){
//                    // 从rePushAiVOS中根据traceId和batchId获取coskey
//                    RePushAiVO rePushAiVO = rePushAiVOS.stream().filter(y->y.getTraceId().equals(x.getTraceId()) && y.getBatchId().equals(x.getBatchId())).findFirst().get();
//                    String coskey = rePushAiVO.getCoskey();
//                    if(StringUtils.isNotEmpty(coskey)){
//                        List<String> contents = Arrays.stream(coskey.split(",")).toList();
//                        List<String> urls = new ArrayList<>();
//                        if (CollectionUtils.isNotEmpty(contents)){
//                            contents.stream().forEach(y->{
//                                String allfilePath = filePath + y;
//                                String url = aiTaskService.getFileUrl(allfilePath);
//                                urls.add(url);
//                            });
//                        }
//                        List<BeCheckFileDTO> beCheckFileDTOS = x.getContents();
//                        if(CollectionUtils.isNotEmpty(beCheckFileDTOS)){
//                            beCheckFileDTOS.stream().forEach(y->{
//                                y.setFileUrl(urls.get(beCheckFileDTOS.indexOf(y)));
//                            });
//                        }
//                        x.setContents(beCheckFileDTOS);
//                    }
//                }
//            });
//        }
       return aiTaskDTOS;
    }

    /**
     * 计算补推的开始日期和结束日期
     * @param rePushAiDTO
     * @return
     */
    private RePushAiDTO countBeginAndEndDate(RePushAiDTO rePushAiDTO) {
        LocalDateTime  beginDate = LocalDateTime.now();
        LocalDateTime endDate = LocalDateTime.now();
        if(StringUtils.isEmpty(rePushAiDTO.getBeginDate())){
            rePushAiDTO.setBeginDate(beginDate.toString());
        }
        if(StringUtils.isEmpty(rePushAiDTO.getEndDate()) && StringUtils.isNotEmpty(rePushAiDTO.getDynamaicMinute())){
             endDate = beginDate.minusMinutes(Long.parseLong(rePushAiDTO.getDynamaicMinute()));
        }
        if(StringUtils.isEmpty(rePushAiDTO.getEndDate()) && StringUtils.isEmpty(rePushAiDTO.getDynamaicMinute())){
             endDate = beginDate.minusMinutes(90);
        }
        if(StringUtils.isNotEmpty(rePushAiDTO.getPushAllFlag()) && "1".equals(StringUtils.isNotEmpty(rePushAiDTO.getBeginDate()))){
            rePushAiDTO.setEndDate(null);
            rePushAiDTO.setBeginDate(null);
        }
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将LocalDateTime格式化为String
        String start = endDate.format(formatter);
        String endStr = beginDate.format(formatter);

        rePushAiDTO.setBeginDate(start);
        rePushAiDTO.setEndDate(endStr);
        return rePushAiDTO;
    }
}
