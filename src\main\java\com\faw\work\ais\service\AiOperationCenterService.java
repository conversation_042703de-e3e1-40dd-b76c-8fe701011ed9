package com.faw.work.ais.service;


import com.faw.work.ais.common.base.PageResult;
import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.dto.ai.QueryHumanResultDTO;
import com.faw.work.ais.entity.vo.ai.*;

import java.util.List;

public interface AiOperationCenterService {

    /**
     * 获取智能覆盖场景信息
     * @return AiCoveringScenesVO
     */
    AiCoveringScenesVO getAiCoveringScenesInfo();

    /**
     * 获取人工审核数据列表
     * @param dto 请求对象
     * @return AiCoveringScenesVO
     */
    PageResult<HumanResultVo> getHumanResultList(QueryHumanResultDTO dto);

    /**
     * 获取系统信息
     * @return List<SystemVO> 系统信息列表
     */
    List<SystemVO> getSystemInfo();

    /**
     * 获取任务规则
     * @param systemId 系统id
     * @return List<TaskRuleVO> 任务规则列表
     */
    List<TaskRuleVO> getTaskRuleBySystemId(String systemId);

    /**
     * 获取单据审核准确率
     * @param dto 请求参数
     * @return 返回对象
     */
    ApproveRightRateVo billApproveRightRate(QueryHumanResultDTO dto);

    /**
     * 获取规则审核准确率
     * @param dto 请求参数
     * @return 返回对象
     */
    ApproveRightRateVo ruleApproveRightRate(QueryHumanResultDTO dto);

    /**
     * 获取审核数量
     * @param dto   请求条件
     * @return  返回对象
     */
    AiOperationCenterCountVo getCount(QueryHumanResultDTO dto);

    /**
     * 获取文件详情
     * @param fileDTO   请求参数
     * @return List<BeCheckFileInfoVO> 文件列表
     */
    List<BeCheckFileInfoVO> getFileInfosByTraceId(FileDTO fileDTO);
}
