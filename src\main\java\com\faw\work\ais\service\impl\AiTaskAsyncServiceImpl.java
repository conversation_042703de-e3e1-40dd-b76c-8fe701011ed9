package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.enums.ResEnum;
import com.faw.work.ais.config.WhitelistConfig;
import com.faw.work.ais.entity.dto.ai.AiTaskDTO;
import com.faw.work.ais.service.AiTaskAsyncDealService;
import com.faw.work.ais.service.AiTaskAsyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AiTaskAsyncServiceImpl implements AiTaskAsyncService {

    @Autowired
    private AiTaskAsyncDealService asyncDealService;

    @Autowired
    private WhitelistConfig whitelistConfig;

    @Override
    public Response asyncAiTaskBatchAsync(List<AiTaskDTO> aiTaskDTOs) {
        log.info("-----asyncAiTaskBatch入参Json格式--------"  + JSON.toJSONString(aiTaskDTOs));
        if(CollectionUtils.isEmpty(aiTaskDTOs)){
            return Response.success(ResEnum.F_118);
        }
        if(!writeListInterceptor(aiTaskDTOs.get(0).getSystemId())){
            return Response.fail(ResEnum.F_117);
        }
        asyncDealService.asyncAiTaskBatch(aiTaskDTOs);
        return Response.success(ResEnum.SUCCESS_CODE);
    }


    private boolean writeListInterceptor(String systemId){
        List<String> systemIds = whitelistConfig.getSystemIds();
        // 域名不在白名单中，禁止访问
        // 域名在白名单中，继续处理请求
        return systemIds.contains(systemId);
    }
}
