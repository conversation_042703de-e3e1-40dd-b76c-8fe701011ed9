package com.faw.work.ais.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * COS上传文件配置
 * <AUTHOR>
 * @date 2023/10/13
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.upload")
@RefreshScope
public class UploadProperties {
    private String filePath;
    private String secretId;
    private String secretKey;
    private String bucketName;
    private String regionName;
    private List<String> allowPicTypes;
    private List<String> allowPPTTypes;
    private String contentType;
}