package com.faw.work.ais.aic.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 大模型评测输入Excel数据DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "大模型评测输入Excel数据DTO")
public class ModelEvaluationInputDTO {
    
    /**
     * 用户输入
     */
    @ExcelProperty("user_input")
    @Schema(description = "用户输入")
    private String userInput;
    
    /**
     * 人工标注结果
     */
    @ExcelProperty("ground_answer")
    @Schema(description = "人工标注结果")
    private String groundAnswer;
}
