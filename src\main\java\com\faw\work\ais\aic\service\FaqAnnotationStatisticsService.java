package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.FaqAnnotationStatisticsPO;
import com.faw.work.ais.aic.model.response.FaqAnnotationStatisticsResponse;

import java.util.List;

/**
 * FAQ标注统计Service接口
 *
 * <AUTHOR>
 */
public interface FaqAnnotationStatisticsService {

    /**
     * 创建统计信息
     *
     * @param statistics 统计信息
     * @return 创建的统计ID
     */
    String createStatistics(FaqAnnotationStatisticsPO statistics);

    /**
     * 批量创建统计信息
     *
     * @param statisticsList 统计信息列表
     * @return 创建的数量
     */
    int batchCreateStatistics(List<FaqAnnotationStatisticsPO> statisticsList);

    /**
     * 根据任务ID查询统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息列表
     */
    List<FaqAnnotationStatisticsPO> getStatisticsByTaskId(String taskId);

    /**
     * 查询任务的统计响应信息
     *
     * @param taskId 任务ID
     * @return 统计响应信息
     */
    FaqAnnotationStatisticsResponse getAnnotationStatistics(String taskId);

    /**
     * 更新或插入统计信息
     *
     * @param taskId 任务ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @param count 数量
     * @return 是否更新成功
     */
    Boolean upsertStatistics(String taskId, String annotationType, String annotationSubtype, Integer count);

    /**
     * 重新计算任务的统计信息
     *
     * @param taskId 任务ID
     * @return 是否计算成功
     */
    Boolean recalculateStatistics(String taskId);

    /**
     * 删除任务的所有统计信息
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    Boolean deleteByTaskId(String taskId);

    /**
     * 增加标注类型的统计数量
     *
     * @param taskId 任务ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @return 是否增加成功
     */
    Boolean incrementStatistics(String taskId, String annotationType, String annotationSubtype);

    /**
     * 减少标注类型的统计数量
     *
     * @param taskId 任务ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @return 是否减少成功
     */
    Boolean decrementStatistics(String taskId, String annotationType, String annotationSubtype);
}
