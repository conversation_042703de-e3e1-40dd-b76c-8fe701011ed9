package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.FaqSearchByRobotRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * 异步服务
 * <AUTHOR>
 */
public interface AsyncService  {
    /**
     * 日志命中异步
     *
     * @param request       要求
     * @param knowledgeList 知识列表
     */
    @Async("asyncExecutor")
    void logFaqHitAsync(FaqSearchByRobotRequest request, List<FaqKnowledgeResponse> knowledgeList);
} 