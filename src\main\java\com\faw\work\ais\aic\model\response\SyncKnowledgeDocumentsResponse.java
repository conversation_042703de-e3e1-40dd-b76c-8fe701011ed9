package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 同步知识中心文档响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "同步知识中心文档响应")
public class SyncKnowledgeDocumentsResponse {

    @Schema(description = "知识库ID")
    private Long ragKnowledgeId;

    @Schema(description = "知识库名称")
    private String knowledgeName;

    @Schema(description = "总文档数量")
    private Integer totalDocuments;

    @Schema(description = "新增文档数量")
    private Integer newDocuments;

    @Schema(description = "跳过文档数量（已存在）")
    private Integer skippedDocuments;

    @Schema(description = "成功处理文档数量")
    private Integer successfullyProcessed;

    @Schema(description = "处理失败文档数量")
    private Integer failedProcessed;

    @Schema(description = "处理状态描述")
    private String statusMessage;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "处理详情列表")
    private List<DocumentProcessDetail> processDetails;

    /**
     * 文档处理详情
     */
    @Data
    @Schema(description = "文档处理详情")
    public static class DocumentProcessDetail {

        @Schema(description = "文档ID")
        private Long documentId;

        @Schema(description = "文档名称")
        private String documentName;

        @Schema(description = "处理状态（success/failed/skipped）")
        private String status;

        @Schema(description = "错误信息")
        private String errorMessage;

        @Schema(description = "上传状态")
        private Boolean uploadSuccess;

        @Schema(description = "分片状态")
        private Boolean splitSuccess;

        @Schema(description = "绑定状态")
        private Boolean bindSuccess;

        @Schema(description = "向量化状态")
        private Boolean vectorSuccess;
    }
}
