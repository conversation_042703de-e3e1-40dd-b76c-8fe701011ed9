package com.faw.work.ais.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.vo.ai.AiCoverDicVO;
import com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleVO;
import com.faw.work.ais.model.RightTaskRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RightTaskRuleService extends IService<RightTaskRule> {
    /**
     * 获取抽检规则信息
     * @param numberEmployeeDTO
     * @return
     */
    HumanSampleRightRateRuleVO getSampleTaskRuleInfo(NumberEmployeeDTO numberEmployeeDTO);
    AiCoverDicVO getCover(NumberEmployeeDTO numberEmployeeDTO);

}
