package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.request.FaqKnowledgeConditionRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FAQ知识Mapper接口
 * 用于访问和操作FAQ知识库数据表。
 * <AUTHOR>
 */
@Mapper
public interface FaqKnowledgeMapper extends BaseMapper<FaqKnowledgePO> {

    /**
     * 分页查询知识列表，支持关键词、类型和类别筛选。
     *
     * @param page        分页对象，用于传递分页参数。
     * @param text        关键词，用于模糊匹配知识内容。
     * @param type        知识类型。
     * @param categoryIds 类别ID列表，用于筛选指定类别的知识。
     * @return 分页结果，包含符合条件的知识列表和总记录数。
     */
    IPage<FaqKnowledgePO> selectPageWithKeyword(Page<FaqKnowledgePO> page, @Param("text") String text, @Param("type") String type, @Param("categoryIds") List<String> categoryIds);

    /**
     * 使用 PageHelper 分页查询知识列表，支持关键词、类型和类别筛选。
     *
     * @param text        关键词，用于模糊匹配知识内容。
     * @param type        知识类型。
     * @param categoryIds 类别ID列表，用于筛选指定类别的知识。
     * @param robotId
     * @return 知识列表。
     */
    List<FaqKnowledgePO> selectPageWithKeywordByPageHelper(@Param("text") String text, @Param("type") String type, @Param("categoryIds") List<String> categoryIds, @Param("robotId") String robotId);

    /**
     * 增加知识的命中次数。
     * 当知识被用户访问或使用时，调用此方法增加其命中次数，用于统计知识的使用频率。
     *
     * @param id 知识ID。
     * @return 更新结果，表示受影响的记录数。
     */
    int incrementHitCount(@Param("id") String id);

    /**
     * 根据ID列表查询知识，同时查询相似问题。
     * 用于在搜索或推荐场景中，根据用户输入的关键词或问题，查询相关的知识和相似问题。
     *
     * @param ids ID列表。
     * @param score 相似度分数，用于过滤相似度低于指定阈值的相似问题。
     * @param robotId 机器人ID，用于指定查询的机器人。
     * @return 知识列表，包含符合条件的知识和相似问题。
     */
    List<FaqKnowledgePO> selectByIdsWithSimilar(@Param("ids") List<String> ids, @Param("score") Float score, @Param("robotId") String robotId);

    /**
     * 根据ID列表从相似问表查询知识。
     * 用于从FAQ相似问题表中查询与指定ID列表相关的知识。
     *
     * @param ids     ID列表。
     * @param robotId 机器人ID，用于指定查询的机器人。
     * @param env 环境标识
     * @return 知识响应列表，包含从相似问表中查询到的知识信息。
     */
    List<FaqKnowledgeResponse> selectFromFaqKnowledgeAndFaqSimilarQuestionByIds(@Param("ids") List<String> ids, @Param("robotId") String robotId, @Param("env") String env);

    /**
     * 根据ID列表从FAQ知识表查询知识。
     * 用于从FAQ知识表中查询与指定ID列表相关的知识。
     *
     * @param ids ID列表。
     * @param score 相似度分数，用于过滤相似度低于指定阈值的知识。
     * @param robotId 机器人ID，用于指定查询的机器人。
     * @return 知识响应列表，包含从FAQ知识表中查询到的知识信息。
     */
    List<FaqKnowledgeResponse> selectFromFaqKnowledgeByIds(@Param("ids") List<String> ids, @Param("score") Float score, @Param("robotId") String robotId);

    /**
     * 根据条件查询所有知识。
     * 用于根据指定的查询条件，查询所有符合条件的知识。
     *
     * @param request 查询条件，包含各种筛选条件。
     * @return 知识列表，包含符合条件的知识信息。
     */
    List<FaqKnowledgePO> selectAllByCondition(FaqKnowledgeConditionRequest request);

    /**
     * 根据知识ID查询绑定的机器人ID列表。
     * 用于查询指定知识与哪些机器人相关联。
     *
     * @param knowledgeId 知识ID。
     * @return 机器人ID列表，包含与指定知识相关联的机器人ID。
     */
    List<String> selectRobotIdsByKnowledgeId(@Param("knowledgeId") String knowledgeId);

    /**
     * 根据类别ID列表查询知识。
     *
     * @param categoryIds 类别ID列表
     * @return 知识列表
     */
    List<FaqKnowledgePO> selectByCategoryIds(@Param("categoryIds") List<String> categoryIds);

    /**
     * 根据ID查询生产环境知识。
     *
     * @param id 知识ID
     * @return 知识对象
     */
    FaqKnowledgePO selectProdKnowledgeById(@Param("id") String id);

    /**
     * 分页查询生产环境知识列表，支持关键词、类型和类别筛选。
     *
     * @param page        分页对象，用于传递分页参数。
     * @param text        关键词，用于模糊匹配知识内容。
     * @param type        知识类型。
     * @param categoryIds 类别ID列表，用于筛选指定类别的知识。
     * @return 分页结果，包含符合条件的知识列表和总记录数。
     */
    IPage<FaqKnowledgePO> selectProdPageWithKeyword(Page<FaqKnowledgePO> page, @Param("text") String text, @Param("type") String type, @Param("categoryIds") List<String> categoryIds);

    /**
     * 使用 PageHelper 分页查询生产环境知识列表，支持关键词、类型和类别筛选。
     *
     * @param text        关键词，用于模糊匹配知识内容。
     * @param type        知识类型。
     * @param categoryIds 类别ID列表，用于筛选指定类别的知识。
     * @param robotId
     * @return 知识列表。
     */
    List<FaqKnowledgePO> selectProdPageWithKeywordByPageHelper(@Param("text") String text, @Param("type") String type, @Param("categoryIds") List<String> categoryIds, @Param("robotId") String robotId);

    /**
     * 查询指定类别下的知识去重后的数量。
     *
     * @param categoryIds 类别ID列表
     * @return 去重后的知识数量
     */
    int selectDistinctCountByCategoryId(@Param("categoryIds") List<String> categoryIds);

    /**
     * 根据类别ID列表查询知识ID列表。
     *
     * @param categoryIdList 类别ID列表
     * @return 知识ID列表
     */
    List<String> selectKnowledgeIdsByCategoryIds(@Param("categoryIdList") List<String> categoryIdList);

    /**
     * 根据知识ID列表查询知识ID和问题。
     *
     * @param originalKnowledgeIds 知识ID列表
     * @return 知识列表
     */
    List<FaqKnowledgePO> selectIdAndQuestionByIds(@Param("originalKnowledgeIds") List<String> originalKnowledgeIds);

    /**
     * 统计机器人在指定日期内有效的知识数量。
     *
     * @param knowledgeIds 知识ID列表
     * @param date         日期
     * @param env          环境标识
     * @return 有效知识数量
     */
    long countEffectiveKnowledgeForRobot(@Param("knowledgeIds") List<String> knowledgeIds, @Param("date") String date, @Param("env") String env);

    /**
     * 根据知识ID列表和环境标识批量查询知识。
     *
     * @param coldKnowledgeIds 知识ID列表
     * @param env              环境标识
     * @return 知识列表
     */
    List<FaqKnowledgePO> selectBatchIdsByEnv(@Param("coldKnowledgeIds") List<String> coldKnowledgeIds, @Param("env") String env);

    /**
     * 根据版本号统计知识数
     *
     * @param robotId          机器人id
     * @param knowledgeVersion 知识版本
     * @return {@link Integer }
     */
    Integer dogKnowledgeCount(@Param("robotId") String robotId, @Param("knowledgeVersion") Integer knowledgeVersion);
}
