package com.faw.work.ais.service;

import com.faw.work.ais.common.dto.chat.AiAgentRequest;
import com.faw.work.ais.common.dto.chat.AiChatResponse;
import reactor.core.publisher.Flux;

/**
 * Ai智能体 服务接口
 *
 * <AUTHOR>
 * @since 2025-06-23 9:54
 */
public interface AiAgentService {

    /**
     * 获取智能体响应（字符串）
     *
     * @param request 请求参数
     * @return 回复内容
     */
    String getAgentResponse(AiAgentRequest request);

    /**
     * 获取智能体回复
     *
     * @param request 请求参数
     * @return 回复内容
     */
    Flux<AiChatResponse> getAgentFluxResponse(AiAgentRequest request);

}
