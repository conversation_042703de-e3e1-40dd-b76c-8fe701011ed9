package com.faw.work.ais.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * I/O 流工具类，提供流的转换和操作方法。
 */
public final class StreamUtils {

    /**
     * 私有构造函数，防止类被实例化。
     */
    private StreamUtils() {
        // 私有构造函数，阻止实例化
    }

    /**
     * 将输入流的内容转换为字符串。
     *
     * @param inputStream 输入流。
     * @return 转换后的字符串。
     * @throws IOException 如果读取流时发生 I/O 错误。
     */
    public static String convertToString(InputStream inputStream) throws IOException {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            result.write(buffer, 0, length);
        }
        return result.toString(StandardCharsets.UTF_8.name());
    }

    /**
     * 将输入流的内容转换为字节数组。
     *
     * @param inputStream 输入流。
     * @return 包含流内容的字节数组。
     * @throws IOException 如果读取流时发生 I/O 错误。
     */
    public static byte[] convertToByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            result.write(buffer, 0, length);
        }
        return result.toByteArray();
    }
}
