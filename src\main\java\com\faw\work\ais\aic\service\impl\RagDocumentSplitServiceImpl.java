package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.aic.mapper.rag.RagDocumentMapper;
import com.faw.work.ais.aic.mapper.rag.RagDocumentSplitMapper;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import com.faw.work.ais.aic.model.domain.RagDocumentSplitPO;
import com.faw.work.ais.aic.model.request.RagDocumentSplitAddRequest;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.aic.service.RagDocumentSplitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 文档片段表 服务实现类
 *
 * <AUTHOR> Assistant
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class RagDocumentSplitServiceImpl extends ServiceImpl<RagDocumentSplitMapper, RagDocumentSplitPO> implements RagDocumentSplitService {

    private final RagDocumentMapper ragDocumentMapper;
    private final MilvusService milvusService;
    private final EmbeddingService embeddingService;

    @Override
    public List<RagDocumentSplitPO> getByDocumentId(Long documentId) {
        LambdaQueryWrapper<RagDocumentSplitPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagDocumentSplitPO::getDocumentId, documentId);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<RagDocumentSplitPO> documentSplits) {
        if (documentSplits == null || documentSplits.isEmpty()) {
            return false;
        }
        
        // 先删除原有片段
        Long documentId = documentSplits.get(0).getDocumentId();
        this.deleteByDocumentId(documentId);
        
        // 批量保存新片段
        return this.saveBatch(documentSplits);
    }

    @Override
    public void deleteByDocumentId(Long documentId) {
        LambdaQueryWrapper<RagDocumentSplitPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagDocumentSplitPO::getDocumentId, documentId);
        this.remove(queryWrapper);
    }
    
    @Override
    public List<RagDocumentSplitPO> getList(RagDocumentSplitPO ragDocumentSplitPO) {
        LambdaQueryWrapper<RagDocumentSplitPO> queryWrapper = new LambdaQueryWrapper<>();
        
        if (ragDocumentSplitPO != null) {
            // 根据文档ID查询
            if (ragDocumentSplitPO.getDocumentId() != null) {
                queryWrapper.eq(RagDocumentSplitPO::getDocumentId, ragDocumentSplitPO.getDocumentId());
            }
            
            // 可根据需要添加其他查询条件
        }
        
        return this.list(queryWrapper);
    }

    @Override
    public List<RagDocumentPO> getListByAppId(Long appId, String bizInfo) {
        return ragDocumentMapper.getDocumentListByAppId(appId, bizInfo);
    }

    @Override
    public boolean addManualSplit(RagDocumentSplitAddRequest request, RagDocumentPO document) {


        
        try {
            // 3. 创建文档分片实体
            RagDocumentSplitPO split = new RagDocumentSplitPO();
            split.setDocumentId(document.getId());
            split.setTenantId(15L);
            split.setContent(request.getContent());
            split.setWordCount(request.getContent().length());
            split.setKeywords(StrUtil.emptyToDefault(request.getKeywords(), ""));
            split.setCreatedAt(LocalDateTime.now());
            split.setUpdatedAt(LocalDateTime.now());
            
            // 4. 保存文档分片
            boolean saveResult = this.save(split);
            if (!saveResult) {
                throw new BizException("保存文档分片失败");
            }
            
            // 5. 生成向量并存储到Milvus
            float[] embedding = embeddingService.getEmbedding(split.getContent());
            
            // 6. 存储向量到Milvus
            List<MilvusField> properties = new ArrayList<>();
            properties.add(new MilvusField("content", split.getContent()));
            String bizInfo = document.getBizInfo();
            if (StrUtil.isNotBlank(bizInfo)) {
                properties.add(new MilvusField(MilvusPoolConfig.BIZ_INFO_FIELD, bizInfo));
            }
            
            // 使用文档所属的集合名称
            String collectionName = document.getCollectionName();
            if (StrUtil.isBlank(collectionName)) {
                throw new BizException("文档所属的集合名称为空,请检查rag_document表collection_name字段是否正确");
            }
            
            milvusService.saveEmbedding(
                    collectionName,
                    split.getId().toString(),
                    properties,
                    embedding);
            
            return true;
            
        } catch (Exception e) {
            throw new BizException("添加文档分片失败: " + e.getMessage());
        }
    }
} 