package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 环境枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EnvEnum {
    /**
     * 测试
     */
    TEST("test", "测试环境"),
    /**
     * prod
     */
    PROD("prod", "生产环境");

    private final String code;
    private final String desc;

    public static EnvEnum getByCode(String code) {
        for (EnvEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return PROD;
    }
}