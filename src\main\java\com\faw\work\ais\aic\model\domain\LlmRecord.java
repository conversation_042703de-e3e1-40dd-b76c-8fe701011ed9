package com.faw.work.ais.aic.model.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 情绪分析表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("llm_record")
public class LlmRecord implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 请求id
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 用户输入
     */
    @TableField("user_input")
    private String userInput;

    /**
     * 大模型输出
     */
    @TableField("llm_output")
    private String llmOutput;


    /**
     * 完成状态（00-未处理 01-进行中 02-已完成，10-处理失败）
     */
    @TableField("status")
    private String status;

    /**
     * 业务类型
     */
    @TableField("biz_type")
    private String bizType;

    /**
     * 回调地址
     */
    @TableField("callback_url")
    private String callbackUrl;

    /**
     * 冗余字段1
     */
    @TableField("field1")
    private String field1;

    /**
     * 冗余字段2
     */
    @TableField("field2")
    private String field2;

    /**
     * 冗余字段3
     */
    @TableField("field3")
    private String field3;

    /**
     * 冗余字段4
     */
    @TableField("field4")
    private String field4;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * 创建时间
     */
    @TableField("create_at")
    private String createAt;
}
