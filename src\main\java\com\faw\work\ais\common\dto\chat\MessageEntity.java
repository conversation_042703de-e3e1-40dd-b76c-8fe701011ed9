package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 消息实体
 *
 * <AUTHOR>
 * @since 2025-04-22 10:45
 */
@Data
@Schema(description = "消息实体")
public class MessageEntity {

    @Schema(description = "角色")
    private String role;

    @Schema(description = "内容")
    private List<Map<String, Object>> content;

}
