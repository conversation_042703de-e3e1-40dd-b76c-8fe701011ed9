package com.faw.work.ais.feign.chat;

import com.faw.work.ais.common.dto.chat.PhotoAnalysisRequest;
import com.faw.work.ais.common.dto.chat.PhotoAnalysisResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 阿里云Feign调用接口
 *
 * <AUTHOR>
 * @since 2025-04-22 10:20
 */
@FeignClient(value = "AliYunFeignClient", url = "${spring.ai.dashscope.api-host:}", configuration = AliYunFeignInterceptor.class)
public interface AliYunFeignClient {

    /**
     * 图片解析
     *
     * @param photoAnalysisRequest 请求体
     * @return 解析结果
     */
    @PostMapping("/compatible-mode/v1/chat/completions")
    PhotoAnalysisResponse analyzePhoto(@RequestBody PhotoAnalysisRequest photoAnalysisRequest);

}
