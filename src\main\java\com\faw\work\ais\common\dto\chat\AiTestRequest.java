package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AI测试 请求参数
 *
 * <AUTHOR>
 * @since 2025-05-19 9:03
 */
@Data
@Schema(description = "AI测试 请求参数")
public class AiTestRequest {

    /**
     * 问题
     */
    @Schema(description = "问题")
    private String question;

    /**
     * 标准答案
     */
    @Schema(description = "标准答案")
    private String answer;

    /**
     * AI答案
     */
    @Schema(description = "AI答案")
    private String aiAnswer;

    /**
     * 网络地址
     */
    @Schema(description = "网络地址")
    private String networkUrl;

}
