package com.faw.work.ais.interceptor;


import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.faw.work.ais.common.dto.user.QueryUserByLoginAccount;
import com.faw.work.ais.common.vo.iworkUser.IworkUserInfo;
import com.faw.work.ais.config.UgcConfig;
import com.faw.work.ais.common.UserInfo;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.faw.work.ais.feign.IworkResponse;
import com.faw.work.ais.feign.OpenApiFeignClient;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;


/**
 * 工作台token拦截器
 * <AUTHOR>
 * @date 2024/01/03
 */
@Component
public class AuthorizationInterceptor implements HandlerInterceptor {

    Logger logger = LoggerFactory.getLogger(AuthorizationInterceptor.class);

    @Resource
    private UgcConfig ugcConfig;

    @Resource
    private OpenApiFeignClient openApiFeignClient;

    @Resource
    private RedissonClient redissonClient;

    private static final String USER_INFO = "IWORK_USER:";

    private static final String AUTHORIZATION_PREFIX = "Bearer";


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String authorization = request.getHeader("Authorization");
        if(StringUtils.isBlank(authorization)){
            throw new BizException("用户未登录");
        }
        if(!authorization.startsWith(AUTHORIZATION_PREFIX)){
            throw new BizException("Token格式错误，请传入Bearer Token");
        }
        // 获取完成token
        String token = authorization.replace(AUTHORIZATION_PREFIX, Strings.EMPTY).replace(" ",Strings.EMPTY);
        // 解析token信息  工作台token用的是hs512加密的(UAT无秘钥)
        DecodedJWT verify;
        try {
            if (StringUtils.isBlank(ugcConfig.getJwtSecretKey())){
                verify = JWT.decode(token);
            } else {
                JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC512(ugcConfig.getJwtSecretKey())).build();
                verify = jwtVerifier.verify(token);
            }
        } catch (Exception e) {
            logger.error("Token解析失败异常堆栈信息：{}",e.toString());
            throw new BizException("Token解析失败");
        }

        // token过期时间
        LocalDateTime expireTime = LocalDateTime.ofInstant(verify.getExpiresAt().toInstant(), ZoneId.systemDefault());
        if(expireTime.isBefore(LocalDateTime.now())){
            throw new BizException("Token已过期");
        }
        String userName = verify.getClaim("sub").toString().replaceAll("\"", "");
        String idmId = verify.getClaim("idmid").toString().replaceAll("\"", "");
        logger.debug("username {} ,idmId {}", userName, idmId);

        UserInfo userInfo = new UserInfo(userName, idmId);
        // 获取用户信息
        RBucket<IworkUserInfo> userBucket = redissonClient.getBucket(USER_INFO+userName);
        IworkUserInfo iworkUserInfo = null;
        if(userBucket.isExists()){
            iworkUserInfo = userBucket.get();
        } else {
            IworkResponse<IworkUserInfo> userResponse = openApiFeignClient.getCenterUserInfo(new QueryUserByLoginAccount().setLoginName(userName));
            if(userResponse.isSuc()) {
                iworkUserInfo = userResponse.getData();
                userBucket.set(iworkUserInfo);
                userBucket.expire(Duration.ofDays(7));
            }
        }
        if(iworkUserInfo != null){
            userInfo.setRealName(iworkUserInfo.getName());
        }
        // 当前登录人信息
        UserThreadLocalUtil.setUserInfo(userInfo);

        logger.info("获取到当前iwork登录人：{}",UserThreadLocalUtil.getCurrentName());
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 暂不需要
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserThreadLocalUtil.clear();
    }

}
