<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.ViewAiCoverNumMapper">

    <resultMap id="BaseResultMap" type="com.faw.work.ais.entity.vo.ai.AiCoveringScenesVO">
        <id column="id" property="id"/>
        <result column="ai_cover_role" property="aiCoverRoleNum"/>
        <result column="ai_cover_biz_unit" property="aiCoverBizNum"/>
        <result column="ai_cover_rule" property="aiCoverRuleNum"/>
        <result column="is_ready_for" property="onlineCount"/>
    </resultMap>

    <update id="updateAiCoverInfo" parameterType="com.faw.work.ais.entity.dto.ai.NumberEmployeeAiCoverInfoDTO">
        UPDATE view_ai_cover_num
        SET
        ai_cover_biz_unit = #{numberEmployeeAiCoverInfoDTO.aiCoverBizUnit},
        ai_cover_rule_unit = #{numberEmployeeAiCoverInfoDTO.aiCoverRuleUnit},
        ai_cover_role_unit = #{numberEmployeeAiCoverInfoDTO.aiCoverRoleUnit}
        where it = #{numberEmployeeAiCoverInfoDTO.id}
    </update>

    <select id="getViewAiCoverNum" resultMap="BaseResultMap">
        select *
        from view_ai_cover_num
    </select>
</mapper>
