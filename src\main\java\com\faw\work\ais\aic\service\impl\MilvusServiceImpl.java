package com.faw.work.ais.aic.service.impl;


import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.domain.MilvusRow;
import com.faw.work.ais.aic.model.dto.*;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.common.exception.BizException;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.ConsistencyLevel;
import io.milvus.v2.service.collection.request.LoadCollectionReq;
import io.milvus.v2.service.utility.request.FlushReq;
import io.milvus.v2.service.vector.request.DeleteReq;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.UpsertReq;
import io.milvus.v2.service.vector.request.data.BaseVector;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.DeleteResp;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import io.milvus.v2.service.vector.response.UpsertResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.faw.work.ais.aic.config.MilvusPoolConfig.DEFAULT_TENANT_CLIENT_KEY;
import static com.faw.work.ais.aic.config.MilvusPoolConfig.FAQ_TENANT_CLIENT_KEY;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MilvusServiceImpl implements MilvusService {

    private final MilvusPoolConfig milvusPoolConfig;
    private final EmbeddingService embeddingService;
    private final Gson gson = new Gson();




    @Override
    public InsertResp saveEmbedding(String collectionName, String vectorId, List<MilvusField> properties, float[] embedding) {

        if (StringUtils.isBlank(collectionName) || embedding.length == 0) {
            throw new BizException("集合名称或向量，不能为空");
        }

        MilvusClientV2 client = null;
        try {

            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 准备数据
            List<JsonObject> rows = new ArrayList<>();

            JsonObject row = new JsonObject();
            if (vectorId != null) {
                row.addProperty(MilvusPoolConfig.ID_FIELD, vectorId);
            }

            // 添加自定义属性
            this.addRowProperty(properties, row);

            // 添加向量字段
            row.add(MilvusPoolConfig.VECTOR_FIELD, gson.toJsonTree(embedding));


            rows.add(row);

            // 执行插入
            InsertResp res = client.insert(
                    InsertReq.builder()
                            .collectionName(collectionName)
                            .data(rows)
                            .build()
            );

            log.info("成功将文档ID {} 的向量插入到Milvus，向量维度: {}", vectorId, embedding.length);
            return res;
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }

    @Override
    public InsertResp saveEmbeddingBatch(String tenantKey,String collectionName, List<MilvusRow> rows) {
        if (StringUtils.isBlank(collectionName)) {
            throw new BizException("集合名称不能为空");
        }

        if (rows == null || rows.isEmpty()) {
            throw new BizException("待插入的向量数据不能为空");
        }

        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(tenantKey);

            // 准备批量数据
            List<JsonObject> jsonRows = new ArrayList<>(rows.size());

            for (MilvusRow milvusRow : rows) {
                JsonObject row = new JsonObject();

                // 添加ID字段
                if (milvusRow.getVectorId() != null) {
                    row.addProperty(MilvusPoolConfig.ID_FIELD, milvusRow.getVectorId());
                }

                // 添加自定义属性
                this.addRowProperty(milvusRow.getProperties(), row);

                // 添加向量字段
                row.add(MilvusPoolConfig.VECTOR_FIELD, gson.toJsonTree(milvusRow.getEmbedding()));


                jsonRows.add(row);
            }

            // 执行批量插入
            InsertResp res = client.insert(
                    InsertReq.builder()
                            .collectionName(collectionName)
                            .data(jsonRows)
                            .build()
            );

            log.info("成功批量插入 {} 条向量数据到Milvus集合 {}", rows.size(), collectionName);
            return res;
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(tenantKey, client);
            }
        }
    }

    private void addRowProperty(List<MilvusField> properties, JsonObject row) {
        if (properties == null || properties.isEmpty()) {
            return;
        }
        for (MilvusField property : properties) {
            Object value = property.getValue();
            if (value == null) {
                continue;
            }
            if (value instanceof String) {
                row.addProperty(property.getName(), (String) value);
            } else if (value instanceof Number) {
                row.addProperty(property.getName(), (Number) value);
            } else if (value instanceof Boolean) {
                row.addProperty(property.getName(), (Boolean) value);
            } else if (value instanceof Character) {
                row.addProperty(property.getName(), (Character) value);
            } else {
                throw new BizException("不支持的属性类型: " + value.getClass());
            }
        }
    }


    /**
     * 根据向量查询最相似的内容
     *
     * @param collectionName 集合名称
     * @param embedding      查询向量
     * @param topK           返回的结果数量
     * @return 包含ID和相似度分数的向量搜索结果列表
     */
    @Override
    public List<VectorSearchResult> searchByEmbedding(String collectionName, float[] embedding, int topK, float similarityThreshold, String filterString) {
        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 准备搜索向量
            FloatVec floatVec = new FloatVec(embedding);

            // 配置搜索参数
            Map<String, Object> searchParams = new HashMap<>(32);
            searchParams.put("metric_type", "COSINE");

            // 执行搜索
            SearchReq request = SearchReq.builder()
                    .collectionName(collectionName)
                    .consistencyLevel(ConsistencyLevel.EVENTUALLY)
                    .annsField(MilvusPoolConfig.VECTOR_FIELD)
                    .topK(topK)
                    .data(List.of(floatVec))
                    .outputFields(List.of(MilvusPoolConfig.ID_FIELD, MilvusPoolConfig.CONTENT))
                    .searchParams(searchParams)
                    .build();
            if (StringUtils.isNotBlank(filterString)) {
                request.setFilter(filterString);
            }
            SearchResp searchResp = client.search(request);

            if (searchResp == null || searchResp.getSearchResults() == null || searchResp.getSearchResults().isEmpty()) {
                log.info("没有找到相似的文档");
                return new ArrayList<>();
            }

            // 解析搜索结果
            List<VectorSearchResult> results = new ArrayList<>();

            // 获取第一个查询向量的所有匹配结果
            List<SearchResp.SearchResult> searchResults = searchResp.getSearchResults().get(0);
            for (SearchResp.SearchResult result : searchResults) {
                String documentId = (String) result.getId();
                float similarity = result.getScore();
                Object content = result.getEntity().get(MilvusPoolConfig.CONTENT);

                // 只添加相似度大于等于阈值的文档
                if (similarity >= similarityThreshold) {
                    log.debug("找到相似文档，ID: {}, 相似度: {},内容为: {}", documentId, similarity, content.toString());

                    VectorSearchResult searchResult = VectorSearchResult.builder()
                            .id(documentId)
                            .score(similarity)
                            .content(content.toString())
                            .build();
                    results.add(searchResult);
                } else {
                    log.debug("找到相似文档，ID: {}, 相似度: {}，但阈值 {} 未达到，忽略", documentId, similarity, similarityThreshold);
                }
            }

            return results;
        }  finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }

    @Override
    public List<MilvusSearchResult> searchByEmbeddingWithDetails(String tenantKey, String collectionName,
                                                                 float[] embedding, int topK,
                                                                 float similarityThreshold, String filterString) {
        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(tenantKey);

            // 配置搜索参数
            Map<String, Object> searchParams = new HashMap<>(2);
            searchParams.put("metric_type", "COSINE");

            // 构建搜索请求 - 只返回必要字段，不包含向量
            SearchReq.SearchReqBuilder<?, ?> builder = SearchReq.builder()
                    .collectionName(collectionName)
                    .consistencyLevel(ConsistencyLevel.EVENTUALLY)
                    .annsField(MilvusPoolConfig.VECTOR_FIELD)
                    .topK(topK)
                    .data(List.of(new FloatVec(embedding)))
                    .outputFields(List.of(MilvusPoolConfig.ID_FIELD, MilvusPoolConfig.CONTENT,
                            "document_id", "label"))
                    .searchParams(searchParams);

            if (StringUtils.isNotBlank(filterString)) {
                builder.filter(filterString);
            }

            SearchResp searchResp = client.search(builder.build());

            if (searchResp == null || searchResp.getSearchResults() == null ||
                    searchResp.getSearchResults().isEmpty()) {
                log.info("未找到匹配文档，collection: {}", collectionName);
                return new ArrayList<>();
            }

            // 解析并过滤搜索结果
            List<MilvusSearchResult> results = new ArrayList<>();
            List<SearchResp.SearchResult> searchResults = searchResp.getSearchResults().get(0);

            for (SearchResp.SearchResult result : searchResults) {
                float similarity = result.getScore();

                // 相似度阈值过滤
                if (similarity < similarityThreshold) {
                    log.debug("文档相似度 {} 低于阈值 {}，跳过", similarity, similarityThreshold);
                    continue;
                }

                Map<String, Object> entity = result.getEntity();

                // 修复：安全地获取ID，处理Long类型转换为String
                String id = getIdAsString(result.getId());
                String content = getStringValue(entity, MilvusPoolConfig.CONTENT);
                Long documentId = getLongValue(entity, "document_id");
                String label = getStringValue(entity, "label");

                log.info("找到匹配文档 - ID: {}, 相似度:{} , 文档ID: {}, 标签: {}",
                        id, similarity, documentId, label);

                results.add(MilvusSearchResult.builder()
                        .id(id)
                        .score(similarity)
                        .content(content)
                        .documentId(documentId)
                        .label(label)
                        .build());
            }

            log.info("搜索完成，返回 {} 个结果", results.size());
            return results;

        } catch (Exception e) {
            log.error("向量搜索失败 - collection: {}, tenantKey: {}", collectionName, tenantKey, e);
            throw new BizException("向量搜索失败: " + e.getMessage());
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(tenantKey, client);
            }
        }
    }

    /**
     * 安全地将ID转换为字符串
     * @param id ID对象，可能是Long或String类型
     * @return 字符串形式的ID
     */
    private String getIdAsString(Object id) {
        if (id == null) {
            return null;
        }
        if (id instanceof String) {
            return (String) id;
        }
        if (id instanceof Long) {
            return String.valueOf(id);
        }
        if (id instanceof Integer) {
            return String.valueOf(id);
        }
        // 处理其他数字类型
        return id.toString();
    }

    private String getStringValue(Map<String, Object> entity, String key) {
        Object value = entity.get(key);
        return value != null ? value.toString() : null;
    }

    private Long getLongValue(Map<String, Object> entity, String key) {
        Object value = entity.get(key);
        if (value == null) return null;

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法转换字段 {} 的值 {} 为Long类型", key, value);
            return null;
        }
    }

    @Override
    public long deleteByIds(String collectionName, List<String> ids, String documentId) {
        MilvusClientV2 client = null;
        try {
            // 参数校验（保持不变）
            if (collectionName == null || collectionName.trim().isEmpty()) {
                throw new BizException("集合名称不能为空");
            }
            if (ids == null || ids.isEmpty()) {
                return 0;
            }

            log.info("开始从Milvus集合 {} 中删除 {} 个向量", collectionName, ids.size());
            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // ==== 修复 1：使用单引号包裹每个 ID ====
            StringBuilder expr = new StringBuilder();
            expr.append(MilvusPoolConfig.ID_FIELD).append(" in [");

            for (int i = 0; i < ids.size(); i++) {
                // 每个 ID 用单引号包裹，并转义内部单引号
                String escapedId = ids.get(i).replace("'", "\\\\'");
                expr.append("'").append(escapedId).append("'");

                if (i < ids.size() - 1) {
                    expr.append(", ");
                }
            }
            expr.append("]");

            // ==== 修复 2：正确拼接 documentId 条件 ====
            if (documentId != null) {
                String escapedDocId = documentId.replace("'", "\\\\'");
                expr.insert(0, "(").append(") AND ");
                expr.append(MilvusPoolConfig.DOCUMENT_ID_FIELD)
                        .append(" == '").append(escapedDocId).append("'");
            }

            // 执行删除操作
            DeleteResp res = client.delete(
                    DeleteReq.builder()
                            .collectionName(collectionName)
                            .filter(expr.toString())
                            .build()
            );

            log.info("成功从Milvus集合 {} 中删除 {} 个向量", collectionName, res.getDeleteCnt());
            return res.getDeleteCnt();
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }

    @Override
    public long deleteByIdsNew(String collectionName, List<String> ids) {
        MilvusClientV2 client = null;
        try {
            // 参数校验（保持不变）
            if (collectionName == null || collectionName.trim().isEmpty()) {
                throw new BizException("集合名称不能为空");
            }
            if (ids == null || ids.isEmpty()) {
                return 0;
            }

            client = milvusPoolConfig.getClient(DEFAULT_TENANT_CLIENT_KEY);

            // ==== 修复 1：使用单引号包裹每个 ID ====
            StringBuilder expr = new StringBuilder();
            expr.append(MilvusPoolConfig.ID_FIELD).append(" in [");

            for (int i = 0; i < ids.size(); i++) {
                // 每个 ID 用单引号包裹，并转义内部单引号
                String escapedId = ids.get(i).replace("'", "\\\\'");
                expr.append("'").append(escapedId).append("'");

                if (i < ids.size() - 1) {
                    expr.append(", ");
                }
            }
            expr.append("]");


            // 执行删除操作
            DeleteResp res = client.delete(
                    DeleteReq.builder()
                            .collectionName(collectionName)
                            .filter(expr.toString())
                            .build()
            );

            log.info("成功从Milvus集合 {} 中删除 {} 个向量", collectionName, res.getDeleteCnt());
            return res.getDeleteCnt();
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(DEFAULT_TENANT_CLIENT_KEY, client);
            }
        }
    }

    @Override
    public List<VectorSearchResult> selectByIds(String collectionName, List<String> ids) {
        if (StringUtils.isBlank(collectionName) || CollectionUtils.isEmpty(ids)) {
            log.error("集合名称或ID列表为空");
            return new ArrayList<>();
        }

        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 构建ID过滤条件
            StringBuilder expr = new StringBuilder();
            expr.append(MilvusPoolConfig.ID_FIELD).append(" in [");
            for (int i = 0; i < ids.size(); i++) {
                expr.append("'").append(ids.get(i)).append("'");
                if (i < ids.size() - 1) {
                    expr.append(", ");
                }
            }
            expr.append("]");

            // 配置搜索参数
            Map<String, Object> searchParams = new HashMap<>(8);
            searchParams.put("metric_type", "COSINE");

            // 执行搜索
            SearchReq request = SearchReq.builder()
                    .collectionName(collectionName)
                    .consistencyLevel(ConsistencyLevel.EVENTUALLY)
                    .annsField(MilvusPoolConfig.VECTOR_FIELD)
                    .filter(expr.toString())
                    .topK(ids.size())
                    .data(List.of(new FloatVec(new float[1024])))
                    .outputFields(List.of(MilvusPoolConfig.ID_FIELD, MilvusPoolConfig.CONTENT))
                    .searchParams(searchParams)
                    .build();

            SearchResp searchResp = client.search(request);

            if (searchResp == null || searchResp.getSearchResults() == null || searchResp.getSearchResults().isEmpty()) {
                log.info("通过ID列表未找到匹配记录");
                return new ArrayList<>();
            }

            // 解析搜索结果
            List<VectorSearchResult> results = new ArrayList<>();
            List<SearchResp.SearchResult> searchResults = searchResp.getSearchResults().get(0);

            for (SearchResp.SearchResult result : searchResults) {
                String documentId = (String) result.getId();
                float similarity = result.getScore();
                Object content = result.getEntity().get(MilvusPoolConfig.CONTENT);

                VectorSearchResult searchResult = VectorSearchResult.builder()
                        .id(documentId)
                        .score(similarity)
                        .content(content != null ? content.toString() : null)
                        .build();
                results.add(searchResult);

                log.debug("通过ID查询到记录，ID: {}, 相似度: {}, 内容: {}", documentId, similarity,
                        content != null ? content.toString() : "null");
            }

            return results;
        }  finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }

    @Override
    public List<MilvusRow> selectRowsByIds(String collectionName, List<String> ids) {
        if (StringUtils.isBlank(collectionName) || CollectionUtils.isEmpty(ids)) {
            log.error("集合名称或ID列表为空");
            return new ArrayList<>();
        }

        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 构建ID过滤条件
            StringBuilder expr = new StringBuilder();
            expr.append(MilvusPoolConfig.ID_FIELD).append(" in [");
            for (int i = 0; i < ids.size(); i++) {
                expr.append("'").append(ids.get(i)).append("'"); // 添加单引号包裹ID
                if (i < ids.size() - 1) {
                    expr.append(", ");
                }
            }
            expr.append("]");

            // 配置搜索参数
            Map<String, Object> searchParams = new HashMap<>(8);
            searchParams.put("metric_type", "COSINE");

            // 执行搜索，返回所有字段
            SearchReq request = SearchReq.builder()
                    .collectionName(collectionName)
                    .consistencyLevel(ConsistencyLevel.EVENTUALLY)
                    .annsField(MilvusPoolConfig.VECTOR_FIELD)
                    .filter(expr.toString())
                    .topK(ids.size()) // 设置为ID列表的大小，确保返回所有匹配记录
                    .data(List.of(new FloatVec(new float[1024]))) // 使用1024维空向量
                    .outputFields(List.of(MilvusPoolConfig.ID_FIELD, MilvusPoolConfig.CONTENT, MilvusPoolConfig.VECTOR_FIELD, MilvusPoolConfig.BIZ_INFO_FIELD))
                    .searchParams(searchParams)
                    .build();

            SearchResp searchResp = client.search(request);

            if (searchResp == null || searchResp.getSearchResults() == null || searchResp.getSearchResults().isEmpty()) {
                log.info("通过ID列表未找到匹配记录");
                return new ArrayList<>();
            }

            // 解析搜索结果
            List<MilvusRow> results = new ArrayList<>();
            List<SearchResp.SearchResult> searchResults = searchResp.getSearchResults().get(0);

            for (SearchResp.SearchResult result : searchResults) {
                String documentId = (String) result.getId();
                Map<String, Object> entity = result.getEntity();
                
                // 获取向量数据
                float[] embedding = null;
                if (entity.get(MilvusPoolConfig.VECTOR_FIELD) instanceof List<?>) {
                    List<?> vectorList = (List<?>) entity.get(MilvusPoolConfig.VECTOR_FIELD);
                    embedding = new float[vectorList.size()];
                    for (int i = 0; i < vectorList.size(); i++) {
                        if (vectorList.get(i) instanceof Number) {
                            embedding[i] = ((Number) vectorList.get(i)).floatValue();
                        }
                    }
                }

                // 获取其他字段
                String content = entity.get(MilvusPoolConfig.CONTENT) != null ? entity.get(MilvusPoolConfig.CONTENT).toString() : null;
                String bizInfo = entity.get(MilvusPoolConfig.BIZ_INFO_FIELD) != null ? entity.get(MilvusPoolConfig.BIZ_INFO_FIELD).toString() : null;

                // 构建属性列表
                List<MilvusField> properties = new ArrayList<>();
                if (bizInfo != null) {
                    properties.add(new MilvusField(MilvusPoolConfig.BIZ_INFO_FIELD, bizInfo));
                }
                if (content != null) {
                    properties.add(new MilvusField(MilvusPoolConfig.CONTENT, content));
                }

                // 构建MilvusRow对象
                MilvusRow milvusRow = MilvusRow.builder()
                        .vectorId(documentId)
                        .embedding(embedding)
                        .properties(properties)
                        .build();

                results.add(milvusRow);

                log.debug("通过ID查询到完整记录，ID: {}, 内容长度: {}, 向量维度: {}", 
                    documentId, 
                    content != null ? content.length() : 0,
                    embedding != null ? embedding.length : 0);
            }

            return results;
        }finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }
    @Override
    public SearchResp batchSearch(String collectionName, List<List<Float>> embeddings, int topK, String filterString) {
        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 1. 将 List<List<Float>> 转换为 Milvus SDK 需要的 List<BaseVector>
            List<BaseVector> searchVectors = embeddings.stream()
                    .map(FloatVec::new)
                    .collect(Collectors.toList());

            // 2. 配置搜索参数
            Map<String, Object> searchParams = new HashMap<>(32);
            searchParams.put("metric_type", "COSINE");

            // 3. 构建批量搜索请求
            SearchReq request = SearchReq.builder()
                    .collectionName(collectionName)
                    .consistencyLevel(ConsistencyLevel.BOUNDED)
                    .annsField(MilvusPoolConfig.VECTOR_FIELD)
                    .topK(topK)
                    .data(searchVectors) // <--- 传入多个向量
                    .outputFields(List.of(MilvusPoolConfig.ID_FIELD, "content")) // 确保 "content" 是你存储问题文本的字段
                    .searchParams(searchParams)
                    .build();
            if (StringUtils.isNotBlank(filterString)){
                request.setFilter(filterString);
            }

            // 4. 执行搜索并返回结果
            return client.search(request);

        } catch (Exception e) {
            log.error("Milvus batch search failed", e);
            // 根据你的业务需求处理异常
            throw new RuntimeException("Milvus batch search failed", e);
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }


    @Override
    public boolean flushAndLoad(String tenantKey, String collectionName) {
        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(tenantKey);

            // 1. 构建并执行 Flush 请求
            FlushReq flushReq = FlushReq.builder()
                    .collectionNames(List.of(collectionName))
                    .build();
            client.flush(flushReq);
            log.info("Milvus 集合 {} 数据刷盘成功", collectionName);

            // 2. 构建并执行 Load 请求
            LoadCollectionReq loadReq = LoadCollectionReq.builder()
                    .collectionName(collectionName)
                    .sync(true)
                    .refresh(true)
                    .build();
            client.loadCollection(loadReq);
            log.info("Milvus 集合 {} 数据加载到内存成功", collectionName);

            return true;
        } catch (Exception e) {
            log.error("Milvus 刷盘或加载失败 - collection: {}, tenantKey: {}",
                    collectionName, tenantKey, e);
            return false;
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(tenantKey, client);
            }
        }
    }


    @Override
    public UpsertResp saveOrUpdateBatch(String tenantKey, String collectionName, List<MilvusRow> rows) {
        if (StringUtils.isBlank(collectionName) || rows == null || rows.isEmpty()) {
            throw new BizException("集合名称或向量，不能为空");
        }

        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(tenantKey);

            // 准备数据
            List<JsonObject> jsonRows = new ArrayList<>();

            for (MilvusRow milvusRow : rows) {
                JsonObject row = new JsonObject();

                // 添加ID字段 (主键，必须包含)
                if (milvusRow.getVectorId() != null) {
                    row.addProperty(MilvusPoolConfig.ID_FIELD, milvusRow.getVectorId());
                }

                // 添加向量字段
                row.add(MilvusPoolConfig.VECTOR_FIELD, gson.toJsonTree(milvusRow.getEmbedding()));

                // 添加自定义属性
                if (milvusRow.getProperties() != null) {
                    addRowProperty(milvusRow.getProperties(), row);
                }

                jsonRows.add(row);
            }

            // 执行upsert操作
            UpsertResp upsertResp = client.upsert(
                    UpsertReq.builder()
                            .collectionName(collectionName)
                            .data(jsonRows)
                            .build()
            );
            // 清理临时数据
            jsonRows.clear();

            log.info("成功对集合 {} 执行upsert操作，共处理 {} 条向量数据",
                    collectionName, rows.size());
            return upsertResp;

        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(tenantKey, client);
            }
        }
    }


    /**
     * 批量插入向量到Milvus
     *
     * @param collectionName 集合名称
     * @param ids            ID列表
     * @param embeddings     向量列表
     * @param idWithBizInfo  业务信息映射 (ID -> 业务信息)
     */
    public void insertVectors(String collectionName, List<String> ids, List<float[]> embeddings, Map<String, String> idWithBizInfo, Map<String, String> idWithContent) {
        if (StringUtils.isBlank(collectionName)) {
            throw new BizException("集合名称不能为空");
        }

        if (ids == null || ids.isEmpty() || embeddings == null || embeddings.isEmpty()) {
            throw new BizException("ID列表或向量列表不能为空");
        }

        if (ids.size() != embeddings.size()) {
            throw new BizException("ID列表和向量列表大小不一致");
        }

        MilvusClientV2 client = null;
        try {
            client = milvusPoolConfig.getClient(FAQ_TENANT_CLIENT_KEY);

            // 准备批量数据
            List<JsonObject> jsonRows = new ArrayList<>(ids.size());

            for (int i = 0; i < ids.size(); i++) {
                JsonObject row = new JsonObject();

                // 添加ID字段
                String id = ids.get(i);
                row.addProperty(MilvusPoolConfig.ID_FIELD, id);

                // 添加业务信息字段（如果有）
                if (idWithBizInfo != null && idWithBizInfo.containsKey(id)) {
                    row.addProperty(MilvusPoolConfig.BIZ_INFO_FIELD, idWithBizInfo.get(id));
                }

                // 添加向量字段
                row.add(MilvusPoolConfig.VECTOR_FIELD, gson.toJsonTree(embeddings.get(i)));

                // 添加content字段
                // 添加业务信息字段（如果有）
                if (idWithContent != null && idWithContent.containsKey(id)) {
                    row.addProperty(MilvusPoolConfig.CONTENT, idWithContent.get(id));
                }

                jsonRows.add(row);
            }

            // 执行批量插入
            InsertResp res = client.insert(
                    InsertReq.builder()
                            .collectionName(collectionName)
                            .data(jsonRows)
                            .build()
            );

            jsonRows.clear();

            log.info("成功批量插入 {} 条向量数据到Milvus集合 {}", ids.size(), collectionName);
        } finally {
            if (client != null) {
                milvusPoolConfig.releaseClient(FAQ_TENANT_CLIENT_KEY, client);
            }
        }
    }

    @Override
    public void autoEmbeddingAndInsertMilvus(String collectionName, List<AutoEmbeddingDTO> dataList) {
        if (StringUtils.isBlank(collectionName)) {
            throw new BizException("集合名称不能为空");
        }

        if (dataList == null || dataList.isEmpty()) {
            throw new BizException("待保存的向量数据不能为空");
        }

        // 提取全部content进行向量化（单个批次）
        List<String> contentList = dataList.stream()
                .map(AutoEmbeddingDTO::getContent)
                .toList();

        // 批量获取向量（建议限制单次请求向量维度）
        List<EmbeddingPropertyDTO> embeddingResults = embeddingService.getEmbeddingList(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3,contentList);

        // 准备插入数据
        List<String> ids = new ArrayList<>();
        List<float[]> embeddings = new ArrayList<>();
        Map<String, String> bizInfoMap = new HashMap<>(32);
        Map<String, String> contentMap = new HashMap<>(32);

        for (int i = 0; i < dataList.size(); i++) {
            AutoEmbeddingDTO data = dataList.get(i);
            if (data.getId() == null) {
                throw new BizException("数据ID为空");
            }

            ids.add(data.getId());
            embeddings.add(embeddingResults.get(i).getEmbedding());

            if (StringUtils.isNotBlank(data.getBizInfo())) {
                bizInfoMap.put(data.getId().toString(), data.getBizInfo());
            }

            if (StringUtils.isNotBlank(data.getContent())) {
                contentMap.put(data.getId().toString(), data.getContent());
            }
        }

        // 单次插入（需确保总数据量符合 Milvus 建议）
        if (!ids.isEmpty()) {
            insertVectors(collectionName, ids, embeddings, bizInfoMap, contentMap);
        }
    }


}