package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsProdPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * FAQ机器人知识关联Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface FaqRobotKnowledgeJoinsProdMapper extends BaseMapper<FaqRobotKnowledgeJoinsProdPO> {
    /**
     * 根据机器人ID获取所有中间表ID
     */
    @Select("SELECT id FROM faq_robot_knowledge_joins_prod WHERE robot_id = #{robotId}")
    List<String> getIdsByRobotId(@Param("robotId") String robotId);

    /**
     * 根据机器人ID删除所有记录
     */
    @Delete("DELETE FROM faq_robot_knowledge_joins_prod WHERE robot_id = #{robotId}")
    void deleteByRobotId(@Param("robotId") String robotId);
}