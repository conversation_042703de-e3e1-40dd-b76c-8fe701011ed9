package com.faw.work.ais.service.tool;

import com.faw.work.ais.common.dto.chat.AiChatResponse;
import com.faw.work.ais.common.dto.chat.ToolCacheEntity;

/**
 * 工具处理接口
 *
 * <AUTHOR>
 * @since 2025-07-01 14:17
 */
@FunctionalInterface
public interface ToolHandler {

    /**
     * 处理方法
     *
     * @param toolCache 工具缓存
     * @param response 响应
     */
    void handle(ToolCacheEntity toolCache, AiChatResponse response);

}