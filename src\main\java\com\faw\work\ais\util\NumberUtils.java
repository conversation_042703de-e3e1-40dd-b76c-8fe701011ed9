package com.faw.work.ais.util;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Optional;

/**
 * 数字工具类，提供常用的数字处理和格式化方法。
 */
public final class NumberUtils {

    /**
     * 私有构造函数，防止类被实例化。
     */
    private NumberUtils() {
        // 私有构造函数，阻止实例化
    }

    /**
     * 将字符串安全地转换为整数。如果字符串不是一个有效的数字，则返回一个空的 Optional。
     *
     * @param s 要转换的字符串。
     * @return 包含整数的 Optional，如果转换失败则为 Optional.empty()。
     */
    public static Optional<Integer> safeParseInt(String s) {
        try {
            return Optional.of(Integer.parseInt(s));
        } catch (NumberFormatException e) {
            return Optional.empty();
        }
    }

    /**
     * 将字符串安全地转换为双精度浮点数。如果字符串不是一个有效的数字，则返回一个空的 Optional。
     *
     * @param s 要转换的字符串。
     * @return 包含双精度浮点数的 Optional，如果转换失败则为 Optional.empty()。
     */
    public static Optional<Double> safeParseDouble(String s) {
        try {
            return Optional.of(Double.parseDouble(s));
        } catch (NumberFormatException e) {
            return Optional.empty();
        }
    }

    /**
     * 将数字格式化为货币字符串，例如 1234.56 -> "$1,234.56"。
     *
     * @param amount 要格式化的金额。
     * @return 格式化后的货币字符串。
     */
    public static String formatCurrency(double amount) {
        NumberFormat currencyFormatter = NumberFormat.getCurrencyInstance();
        return currencyFormatter.format(amount);
    }

    /**
     * 将数字格式化为带两位小数的字符串，例如 1234.5678 -> "1234.57"。
     *
     * @param number 要格式化的数字。
     * @return 格式化后的字符串。
     */
    public static String formatDecimal(double number) {
        DecimalFormat decimalFormat = new DecimalFormat("#.00");
        return decimalFormat.format(number);
    }
}
