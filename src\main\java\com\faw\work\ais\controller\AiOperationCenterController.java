package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.common.base.PageResult;
import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.dto.ai.QueryHumanResultDTO;
import com.faw.work.ais.entity.vo.ai.*;
import com.faw.work.ais.service.AiOperationCenterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 运营中心控制层
 * <AUTHOR>
 * @date 2025/01/13
 */
@Schema(description = "运营中心控制层")
@RestController
@Slf4j
@RequestMapping("/operationCenter")
public class AiOperationCenterController {

    @Autowired
    private AiOperationCenterService aiOperationCenterService;

    @Operation(summary = "智能覆盖场景概览", description = "[author:10236535]")
    @GetMapping(value = "/getAiCoveringScenesInfo")
    public Result<AiCoveringScenesVO> getAiCoveringScenesInfo() {
        AiCoveringScenesVO aiCoveringScenesVO = aiOperationCenterService.getAiCoveringScenesInfo();
        return Result.success(aiCoveringScenesVO);
    }

    @Operation(summary = "系统下拉列表接口", description = "[author:10236535]")
    @PostMapping(value = "/getSystemInfo")
    public Result<List<SystemVO>> getSystemInfo() {
        return Result.success(aiOperationCenterService.getSystemInfo());
    }

    @Operation(summary = "规则名称下拉列表接口", description = "[author:10236535]")
    @PostMapping(value = "/getTaskRules")
    public Result<List<TaskRuleVO>> getTaskRules(@RequestParam("systemId") @Valid String systemId) {
        return Result.success(aiOperationCenterService.getTaskRuleBySystemId(systemId));
    }

    @Operation(summary = "获取人工审核列表", description = "[author:10236535]")
    @PostMapping(value = "/getHumanResultList")
    public Result<PageResult<HumanResultVo>> getHumanResultList(@RequestBody QueryHumanResultDTO dto) {
        PageResult<HumanResultVo> result = aiOperationCenterService.getHumanResultList(dto);
        return Result.success(result);
    }

    @Operation(summary = "单据审核准确率", description = "[author:10236535]")
    @PostMapping(value = "/billApproveRightRate")
    public Result<ApproveRightRateVo> billApproveRightRate(@RequestBody QueryHumanResultDTO dto) {
        return Result.success(aiOperationCenterService.billApproveRightRate(dto));
    }

    @Operation(summary = "规则审核准确率", description = "[author:10236535]")
    @PostMapping(value = "/ruleApproveRightRate")
    public Result<ApproveRightRateVo> ruleApproveRightRate(@RequestBody QueryHumanResultDTO dto) {
        return Result.success(aiOperationCenterService.ruleApproveRightRate(dto));
    }

    @Operation(summary = "获取审核数量", description = "[author:10236535]")
    @PostMapping(value = "/getCount")
    public Result<AiOperationCenterCountVo> getCount(@RequestBody QueryHumanResultDTO dto) {
        return Result.success(aiOperationCenterService.getCount(dto));
    }

    @Operation(summary = "根据traceId获取文件列表信息", description = "[author:10236535]")
    @PostMapping(value = "/getFileInfosByTraceId")
    public Result<List<BeCheckFileInfoVO>> getFileInfosByTraceId(@RequestBody @Valid FileDTO fileDTO){
        List<BeCheckFileInfoVO> list = aiOperationCenterService.getFileInfosByTraceId(fileDTO);
        return Result.success(list);
    }

}

