package com.faw.work.ais.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 日期和时间工具类，使用 java.time API。
 * 该类不能被实例化。
 */
public final class DateUtils {

    /**
     * 私有构造函数，防止类被实例化。
     */
    private DateUtils() {
        // 私有构造函数，阻止实例化
    }

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 使用 "yyyy-MM-dd" 格式将 LocalDate 对象格式化为字符串。
     *
     * @param date LocalDate 对象。
     * @return 格式化后的日期字符串。
     */
    public static String formatDate(LocalDate date) {
        return date.format(DATE_FORMATTER);
    }

    /**
     * 使用 "yyyy-MM-dd HH:mm:ss" 格式将 LocalDateTime 对象格式化为字符串。
     *
     * @param dateTime LocalDateTime 对象。
     * @return 格式化后的日期时间字符串。
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime.format(DATETIME_FORMATTER);
    }

    /**
     * 计算两个日期之间的天数。
     *
     * @param startDate 开始日期。
     * @param endDate   结束日期。
     * @return 两个日期之间的天数。
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        return ChronoUnit.DAYS.between(startDate, endDate);
    }
}
