package com.faw.work.ais.util;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 通用API响应结果
 * @param <T> 数据类型
 */
public class ApiResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    // 响应码
    private int code;
    // 响应消息
    private String message;
    // 响应数据
    private T data;
    // 时间戳
    private long timestamp;

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return success(null);
    }

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), data);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> fail() {
        return fail(ResponseCode.FAIL.getCode(), ResponseCode.FAIL.getMessage());
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> fail(String message) {
        return fail(ResponseCode.FAIL.getCode(), message);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> fail(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    /**
     * 构造函数
     */
    public ApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }

    // Getter和Setter方法
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return code == ResponseCode.SUCCESS.getCode();
    }

    /**
     * 转换为Map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("message", message);
        map.put("data", data);
        map.put("timestamp", timestamp);
        return map;
    }

    /**
     * 重写toString方法
     */
    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                '}';
    }

    /**
     * 重写equals方法
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApiResponse<?> that = (ApiResponse<?>) o;
        return code == that.code &&
                timestamp == that.timestamp &&
                Objects.equals(message, that.message) &&
                Objects.equals(data, that.data);
    }

    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return Objects.hash(code, message, data, timestamp);
    }

    /**
     * 响应码枚举
     */
    public enum ResponseCode {
        SUCCESS(200, "成功"),
        FAIL(500, "失败"),
        UNAUTHORIZED(401, "未授权"),
        FORBIDDEN(403, "禁止访问"),
        NOT_FOUND(404, "资源不存在"),
        PARAM_ERROR(400, "参数错误"),
        SERVER_ERROR(503, "服务不可用");

        private final int code;
        private final String message;

        ResponseCode(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }
    }
}