package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 工具方法缓存 数据类
 *
 * <AUTHOR>
 * @since 2025-06-05 10:04
 */
@Data
@Builder
@Schema(description = "工具方法缓存 数据类")
public class ToolCacheEntity {

    /**
     * 工具名称
     */
    @Schema(description = "工具名称")
    private String toolName;

    /**
     * 工具执行状态
     */
    @Schema(description = "工具执行状态")
    private Boolean toolStatus;

    /**
     * 思维链
     */
    @Schema(description = "思维链")
    private String thoughtChain;

    /**
     * 工具响应值
     */
    @Schema(description = "工具响应值")
    private Object toolValue;

}
