package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 基于URL的文档一体化处理请求
 *
 * <AUTHOR>
 */
@Data
@Validated
@Schema(description = "基于URL的文档一体化处理请求")
public class RagDocumentProcessByUrlRequest {

    @NotNull(message = "类目ID不能为空")
    @Schema(description = "类目ID")
    private Long categoryId;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private Long ragKnowledgeId;

    @NotBlank(message = "文件URL不能为空")
    @Schema(description = "文件URL")
    private String fileUrl;

    @NotBlank(message = "文件名不能为空")
    @Schema(description = "文件名")
    private String fileName;

    @NotBlank(message = "文件类型不能为空")
    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件大小(字节)")
    private Long fileSize;

    @Schema(description = "对象存储Key")
    private String objectKey;

    @Schema(description = "分段策略(00-智能切分 01-自定义切分)")
    @NotNull(message = "分段策略不能为空")
    private String chunkStrategy;

    @Schema(description = "分段标识符(默认双换行符\\n ，。！等)")
    private String chunkSeparator;

    @Schema(description = "分段最大长度(tokens)")
    @NotNull(message = "分段最大长度不能为空")
    private Integer chunkLength;

    @Schema(description = "分段重叠长度(tokens)")
    @NotNull(message = "分段重叠长度不能为空")
    private Integer overlapLength;

    @Schema(description = "标签")
    @NotNull(message = "标签不能为空")
    private String label;
}
