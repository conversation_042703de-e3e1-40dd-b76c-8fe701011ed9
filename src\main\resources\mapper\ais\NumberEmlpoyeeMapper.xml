<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.NumberEmployeeMapper">

    <select id="getAiCoverRuleNumBybizUnitInfos" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM task_rule t
        LEFT JOIN biz_unit bu ON t.biz_type = bu.biz_type
        <where>
            <if test = "bizUnitInfos != null and bizUnitInfos.size > 0 ">
                AND bu.unit_code IN
                <foreach collection="bizUnitInfos" item="bizType" open="(" separator="," close=")">
                    #{bizType.bizUnitCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getAiCoverRuleNum" resultType="java.lang.Integer">
        SELECT
            IFNULL(t.ai_cover_rule,0)
        FROM
            view_ai_cover_num t
        WHERE
            t.deal_name = #{dealName}
    </select>
    <select id="getAiCoverRuleRate" resultType="com.faw.work.ais.entity.vo.ai.AiCoverDicVO">
        SELECT
            IFNULL(t.ai_cover_rule,0) as total ,IFNULL(t.is_ready_for,0) as isReadyFor
        FROM
            view_ai_cover_num t
        WHERE
            t.deal_name = #{dealName}
    </select>
    <select id="getAiCoverBizNum" resultType="java.lang.Integer">
        SELECT
            t.ai_cover_biz_unit
        FROM
            view_ai_cover_num t
        WHERE
            t.deal_name = #{dealName}
    </select>
    <select id="getAiCoverRoleNum" resultType="java.lang.Integer">
        SELECT
            t.ai_cover_role
        FROM
            view_ai_cover_num t
        WHERE
            t.deal_name = #{dealName}
    </select>

    <select id="getFlowInfos" resultType="com.faw.work.ais.entity.vo.ai.FlowInfoVO">
        SELECT
            pbu.l3_process_code l3flowCode,
            pbu.l3_process_name l3flowName
        FROM process_biz_unit pbu
        GROUP BY
            pbu.l3_process_code,
            pbu.l3_process_name
    </select>

    <select id="getl3BizUnitInfos" resultType="com.faw.work.ais.entity.vo.ai.BizUnitInfoVO">
        SELECT
            pbu.biz_unit_code bizUnitCode,
            pbu.biz_unit_name bizUnitName
        FROM
            process_biz_unit pbu
        <where>
            <if test = "l3FlowBusinessCode != null and l3FlowBusinessCode != ''">
                AND pbu.l3_process_code = #{l3FlowBusinessCode}
            </if>
        </where>
        GROUP BY
            pbu.biz_unit_name, pbu.biz_unit_code
    </select>

    <select id="getAiTouchTaskInfo" resultType="com.faw.work.ais.entity.vo.ai.NumberEmployeeVO">
        SELECT
            COUNT( 1 ) aiTriggerTaskNum,
            SUM(tt.aipassResult) aiTriggerTaskPassNum,
            IF (
                    COUNT( 1 ) = 0,
                    NULL,
                    ROUND( SUM(tt.aipassResult) / COUNT( 1 ) * 100, 2 )) aiTriggerTaskRate
        FROM
            (
                SELECT
                    CASE
                    WHEN (COUNT(atr.batch_id) = SUM(CASE WHEN atr.ai_result = 'true' THEN 1 ELSE 0 END)) THEN 1
                    ELSE 0
                    END AS aipassResult,
                    atr.batch_id
                FROM
                    ai_task_result atr
                LEFT JOIN biz_unit bu ON atr.biz_type = bu.biz_type
                <where>
                    <if test = " numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                        AND atr.create_time BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
                    </if>
                    <if test = "unitCodes != null and unitCodes.size > 0 ">
                        AND bu.unit_code IN
                        <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                              #{unitCode}
                        </foreach>
                    </if>
                    <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                        AND atr.system_id = #{numberEmployeeDTO.systemId}
                    </if>
                </where>
                GROUP BY
                atr.batch_id
            ) tt
    </select>

    <select id="getAiTouchRuleInfo" resultType="com.faw.work.ais.entity.vo.ai.NumberEmployeeVO">
        SELECT
            COUNT(atr.trace_id) aiTriggerRuleNum,
            SUM(CASE WHEN atr.ai_result = 'true' THEN 1 ELSE 0 END) AS aiTriggerRulePassNum,
            IF (
                COUNT( atr.trace_id ) = 0,
                NULL,
                ROUND( SUM(CASE WHEN atr.ai_result = 'true' THEN 1 ELSE 0 END) / COUNT( atr.trace_id ) * 100, 2 )) aiTriggerRulePassRate
        FROM
            ai_task_result atr
        LEFT JOIN task_rule tr ON atr.task_type = tr.task_type
        LEFT JOIN biz_unit bu ON atr.biz_type = bu.biz_type
        <where>
            <if test = " numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                AND atr.create_time BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
            </if>
            <if test = "unitCodes != null and unitCodes.size > 0 ">
                AND bu.unit_code IN
                <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                    #{unitCode}
                </foreach>
            </if>
            <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                AND tr.system_id = #{numberEmployeeDTO.systemId}
            </if>
        </where>
    </select>

    <select id="getSampleTaskInfo" resultType="com.faw.work.ais.entity.vo.ai.HumanSampleRightRateVO">
        SELECT
        COUNT( 1 ) sampleTotalNum,
        SUM(CASE WHEN hr.human_check_result_final = '0' and hr.ai_result_final = '0' THEN 1
        WHEN hr.human_check_result_final = '1' and hr.ai_result_final = '1' THEN 1 ELSE 0 END) aiTriggerTaskPassNum,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.human_check_result_final = '0' and hr.ai_result_final = '0' THEN 1
        WHEN hr.human_check_result_final = '1' and hr.ai_result_final = '1' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) sampleRightRate,
        SUM(CASE WHEN hr.human_check_result_final = '1' and hr.ai_result_final = '1' THEN 1 ELSE 0 END) aiPassHumanPassNum,
        SUM(CASE WHEN hr.human_check_result_final = '0' and hr.ai_result_final = '1' THEN 1 ELSE 0 END) aiPassHumanUnpassNum,
        SUM(CASE WHEN hr.human_check_result_final = '1' and hr.ai_result_final = '0' THEN 1 ELSE 0 END) aiUnpassHumanPassNum,
        SUM(CASE WHEN hr.human_check_result_final = '0' and hr.ai_result_final = '0' THEN 1 ELSE 0 END) aiUnpassHumanUnpassNum,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.ai_result_final = '1' and hr.human_check_result_final = '1' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiPassHumanPassRate,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM( CASE WHEN hr.ai_result_final = '1' and hr.human_check_result_final = '0' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiPassHumanUnpassRate,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.ai_result_final = '0' and hr.human_check_result_final = '1' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiUnpassHumanPassRate,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.ai_result_final = '0' and hr.human_check_result_final = '0' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiUnpassHumanUnpassRate
        FROM
            (
                SELECT
                    tt.batch_id,
                    tt.human_check_result_final,
                    tt.ai_result_final
                FROM
                    human_result_new tt
                LEFT JOIN biz_unit bu ON tt.biz_type = bu.biz_type
                <where>
                    tt.ai_result_single is not null
                    <if test = " numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                        AND tt.ai_result_time BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
                    </if>
                    <if test = "unitCodes != null and unitCodes.size > 0 ">
                        AND bu.unit_code IN
                        <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                            #{unitCode}
                        </foreach>
                    </if>
                    <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                        AND tt.system_id = #{numberEmployeeDTO.systemId}
                    </if>
                </where>
                GROUP BY
                    tt.human_check_result_final,
                    tt.ai_result_final,
                    tt.batch_id
            ) hr
    </select>

    <select id="getSampleRate" resultType="java.lang.String">
        SELECT
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.human_check_result_final = '0' and hr.ai_result_final = '0' THEN 1
        WHEN hr.human_check_result_final = '1' and hr.ai_result_final = '1' THEN 1 ELSE 0
        END)
        / COUNT( 1 ) * 100, 2 )) sampleRightRate
        FROM
            (
                SELECT
                    tt.batch_id,
                    tt.human_check_result_final,
                    tt.ai_result_final
                FROM
                    human_result_new tt
                LEFT JOIN biz_unit bu ON tt.biz_type = bu.biz_type
                <where>
                    tt.ai_result_single is not null
                    <if test = " beginDate != null and endDate != null and beginDate != '' and endDate != ''">
                        AND tt.ai_result_time BETWEEN #{beginDate} AND #{endDate}
                    </if>
                    <if test = "unitCodes != null and unitCodes.size > 0 ">
                        AND bu.unit_code IN
                        <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                            #{unitCode}
                        </foreach>
                    </if>
                    <if test = "systemId != null and systemId != ''">
                        AND tt.system_id = #{systemId}
                    </if>
                </where>
                GROUP BY
                    tt.human_check_result_final,
                    tt.ai_result_final,
                    tt.batch_id
            ) hr
    </select>

    <select id="getSampleRateMonth" resultType="com.faw.work.ais.entity.vo.ai.RateAndDateVO">
        SELECT
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.human_check_result_final = '0' and hr.ai_result_final = '0' THEN 1
        WHEN hr.human_check_result_final = '1' and hr.ai_result_final = '1' THEN 1 ELSE 0
        END)
        / COUNT( 1 ) * 100, 2 )) billRate,
        CONCAT( hr.aiTime, ' 00:00:00' ) billDateBegin,
        CONCAT( hr.aiTime, ' 23:59:59' ) billDateEnd
        FROM
        (
        SELECT
        tt.batch_id,
        tt.human_check_result_final,
        tt.ai_result_final,
        DATE(tt.ai_result_time) aiTime
        FROM
        human_result_new tt
        LEFT JOIN biz_unit bu ON tt.biz_type = bu.biz_type
        <where>
            tt.ai_result_single is not null
            <if test = " beginDate != null and endDate != null and beginDate != '' and endDate != ''">
                AND tt.ai_result_time BETWEEN #{beginDate} AND #{endDate}
            </if>
            <if test = "unitCodes != null and unitCodes.size > 0 ">
                AND bu.unit_code IN
                <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                    #{unitCode}
                </foreach>
            </if>
            <if test = "systemId != null and systemId != ''">
                AND tt.system_id = #{systemId}
            </if>
        </where>
        GROUP BY
        tt.human_check_result_final,
        tt.ai_result_final,
        tt.batch_id,
        DATE(tt.ai_result_time)
        ) hr
        GROUP BY hr.aiTime
    </select>


    <select id="getAiPassRate" resultType="java.lang.String">
        SELECT
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(tt.aipassResult) / COUNT( 1 ) * 100, 2 )) aiTriggerTaskRate
        FROM
        (
        SELECT
        CASE
        WHEN (COUNT(atr.batch_id) = SUM(CASE WHEN atr.ai_result = 'true' THEN 1 ELSE 0 END)) THEN 1
        ELSE 0
        END AS aipassResult,
        atr.batch_id
        FROM
        ai_task_result atr
        LEFT JOIN biz_unit bu ON atr.biz_type = bu.biz_type
        <where>
            <if test = " beginDate != null and endDate != null and beginDate != '' and endDate != ''">
                AND atr.create_time BETWEEN #{beginDate} AND #{endDate}
            </if>
            <if test = "unitCodes != null and unitCodes.size > 0 ">
                AND bu.unit_code IN
                <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                    #{unitCode}
                </foreach>
            </if>
            <if test = "systemId != null and systemId != ''">
                AND atr.system_id = #{systemId}
            </if>
        </where>
        GROUP BY
        atr.batch_id
        ) tt
    </select>

    <select id="getAiPassRateMonth" resultType="com.faw.work.ais.entity.vo.ai.RateAndDateVO">
        SELECT
            IF
            (
                COUNT( 1 ) = 0,
                NULL,
                ROUND( SUM( tt.aipassResult ) / COUNT( 1 ) * 100, 2 )) billRate ,
            CONCAT(tt.date_created,' 00:00:00') billDateBegin,
            CONCAT(tt.date_created,' 23:59:59') billDateEnd
            FROM
                (
                    SELECT
                        CASE
                            WHEN
                                (COUNT( atr.batch_id ) = SUM( CASE WHEN atr.ai_result = 'true' THEN 1 ELSE 0 END )) THEN
                                1 ELSE 0
                            END AS aipassResult,
                        atr.batch_id,
                        DATE(atr.create_time) AS date_created
                FROM
                ai_task_result atr
                LEFT JOIN biz_unit bu ON atr.biz_type = bu.biz_type
                <where>
                    <if test = " beginDate != null and endDate != null and beginDate != '' and endDate != ''">
                        AND atr.create_time <![CDATA[>=]]> #{beginDate}
                        AND atr.create_time <![CDATA[<=]]> #{endDate}
                    </if>
                    <if test = "unitCodes != null and unitCodes.size > 0 ">
                        AND bu.unit_code IN
                        <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                            #{unitCode}
                        </foreach>
                    </if>
                    <if test = "systemId != null and systemId != ''">
                        AND atr.system_id = #{systemId}
                    </if>
                </where>
                GROUP BY
                    atr.batch_id , DATE(atr.create_time)
                ) tt
        GROUP BY tt.date_created
    </select>

    <select id="getSampleRuleRate" resultType="java.lang.String">
        SELECT
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.ai_result_single = '0' and hr.human_check_result_single = '0' THEN 1
        WHEN hr.ai_result_single = '1' and hr.human_check_result_single = '1' THEN 1 ELSE 0
        END)
        / COUNT( 1 ) * 100, 2 )) sampleRightRate
        FROM human_result_new hr
        LEFT JOIN biz_unit bu ON hr.biz_type = bu.biz_type
        <where>
            hr.ai_result_single is not null
            <if test = " beginDate != null and endDate != null and beginDate != '' and endDate != ''">
                AND hr.ai_result_time BETWEEN #{beginDate} AND #{endDate}
            </if>
            <if test = "unitCodes != null and unitCodes.size > 0 ">
                AND bu.unit_code IN
                <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                    #{unitCode}
                </foreach>
            </if>
            <if test = "systemId != null and systemId != ''">
                AND hr.system_id = #{systemId}
            </if>
        </where>
    </select>

     <select id="getSampleRuleRateMonth" resultType="com.faw.work.ais.entity.vo.ai.RateAndDateVO">
        SELECT
            tt.billRate,
            CONCAT(tt.billDate,' 00:00:00') billDateBegin,
            CONCAT(tt.billDate,' 23:59:59') billDateEnd
         FROM
            (
                SELECT
                    IF(
                        COUNT(1) = 0,
                        NULL,
                        ROUND(
                            SUM(
                                 CASE
                                    WHEN hr.ai_result_single = '0' AND hr.human_check_result_single = '0' THEN 1
                                    WHEN hr.ai_result_single = '1' AND hr.human_check_result_single = '1' THEN 1
                                ELSE 0
                                END
                                ) / COUNT(1) * 100,
                            2
                        )
                    ) AS billRate,
                    DATE(hr.ai_result_time) AS billDate
                FROM human_result_new hr
                LEFT JOIN biz_unit bu ON hr.biz_type = bu.biz_type
                <where>
                    hr.ai_result_single is not null
                    <if test = " beginDate != null and endDate != null and beginDate != '' and endDate != ''">
                        AND hr.ai_result_time BETWEEN #{beginDate} AND #{endDate}
                    </if>
                    <if test = "unitCodes != null and unitCodes.size > 0 ">
                        AND bu.unit_code IN
                        <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                            #{unitCode}
                        </foreach>
                    </if>
                    <if test = "systemId != null and systemId != ''">
                        AND hr.system_id = #{systemId}
                    </if>
                </where>
                GROUP BY
                    DATE(hr.ai_result_time)
            ) tt
    </select>

    <select id="getSampleTaskRuleInfo" resultType="com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleVO">
        SELECT
        COUNT( 1 ) sampleTotalNum,
        SUM(CASE WHEN hr.human_check_result_single = '0' and hr.ai_result_single = '0' THEN 1
        WHEN hr.human_check_result_single = '1' and hr.ai_result_single = '1' THEN 1 ELSE 0 END) aiTriggerTaskPassNum,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.human_check_result_single = '0' and hr.ai_result_single = '0' THEN 1
        WHEN hr.human_check_result_single = '1' and hr.ai_result_single = '1' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) sampleRightRate,
        SUM(CASE WHEN hr.ai_result_single = '1' and hr.human_check_result_single = '1' THEN 1 ELSE 0 END) aiPassHumanPassNum,
        SUM(CASE WHEN hr.ai_result_single = '1' and hr.human_check_result_single = '0' THEN 1 ELSE 0 END) aiPassHumanUnpassNum,
        SUM(CASE WHEN hr.ai_result_single = '0' and hr.human_check_result_single = '1' THEN 1 ELSE 0 END) aiUnpassHumanPassNum,
        SUM(CASE WHEN hr.ai_result_single = '0' and hr.human_check_result_single = '0' THEN 1 ELSE 0 END) aiUnpassHumanUnpassNum,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.ai_result_single = '1' and hr.human_check_result_single = '1' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiPassHumanPassRate,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM( CASE WHEN hr.ai_result_single = '1' and hr.human_check_result_single = '0' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiPassHumanUnpassRate,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.ai_result_single = '0' and hr.human_check_result_single = '1' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiUnpassHumanPassRate,
        IF (
        COUNT( 1 ) = 0,
        NULL,
        ROUND( SUM(CASE WHEN hr.ai_result_single = '0' and hr.human_check_result_single = '0' THEN 1 ELSE 0 END)
        / COUNT( 1 ) * 100, 2 )) aiUnpassHumanUnpassRate
        FROM
        human_result_new hr
        LEFT JOIN biz_unit bu ON hr.biz_type = bu.biz_type
        <where>
            hr.ai_result_single is not null
            <if test = " numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                AND hr.ai_result_time BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
            </if>
            <if test = "unitCodes != null and unitCodes.size > 0 ">
                AND bu.unit_code IN
                <foreach collection="unitCodes" item="unitCode" open="(" separator="," close=")">
                    #{unitCode}
                </foreach>
            </if>
            <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                AND hr.system_id = #{numberEmployeeDTO.systemId}
            </if>
        </where>
    </select>

    <select id="getTaskRuleByBizUintCode" resultType="com.faw.work.ais.entity.vo.ai.TaskRuleVO">
        SELECT
            tr.task_type taskType,
            tr.task_name taskName,
            tr.biz_type bizType,
            tr.save_time saveTime,
            tr.create_time createTime,
            tr.system_id systemId,
            COUNT(1) countRule
        FROM ai_task_result atr
            LEFT JOIN task_rule tr ON atr.task_type = tr.task_type
            LEFT JOIN biz_unit bu ON tr.biz_type = bu.biz_type
        <where>
            <if test = "bizUnitCode != null and bizUnitCode != ''">
                AND bu.unit_code = #{bizUnitCode}
            </if>
        </where>
        GROUP BY tr.task_type,tr.task_name,tr.biz_type,tr.save_time,tr.create_time,tr.system_id
    </select>

    <select id="saveTimes" resultType="java.lang.Double">
        SELECT ROUND((COUNT(1) * #{unitTime} / 60),2)
        FROM ai_task_result atr
        <where>
            <if test = " numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                AND atr.create_time BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
            </if>
            <if test = "bizTypes != null and bizTypes.size > 0 ">
                AND atr.biz_type IN
                <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
                    #{bizType}
                </foreach>
            </if>
            <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                AND atr.system_id = #{numberEmployeeDTO.systemId}
            </if>
        </where>
    </select>

    <select id="saveMoney" resultType="java.lang.Double">
        SELECT
            (COUNT(1) * #{unitCost})
        FROM
        (
            SELECT atr.batch_id
                FROM ai_task_result atr
                <where>
                    <if test = "numberEmployeeDTO.aiCheckDateStart != null and numberEmployeeDTO.aiCheckDateEnd != null and numberEmployeeDTO.aiCheckDateStart != '' and numberEmployeeDTO.aiCheckDateEnd != ''">
                        AND atr.create_time BETWEEN #{numberEmployeeDTO.aiCheckDateStart} AND #{numberEmployeeDTO.aiCheckDateEnd}
                    </if>
                    <if test = "bizTypes != null and bizTypes.size > 0 ">
                        AND atr.biz_type IN
                        <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
                            #{bizType}
                        </foreach>
                    </if>
                    <if test = "numberEmployeeDTO.systemId != null and numberEmployeeDTO.systemId != ''">
                        AND atr.system_id = #{numberEmployeeDTO.systemId}
                    </if>
                </where>
                GROUP BY atr.batch_id
        ) tt
    </select>
    <select id="getFalseData" resultType="com.faw.work.ais.model.ViewAiCoverNum">

SELECT
    id,
    ai_cover_role,
    ai_cover_rule,
    ai_cover_biz_unit,
    deal_name,
    update_time,
    is_ready_for,
    is_true_cover from view_ai_cover_num
    </select>
</mapper>
