package com.faw.work.ais.aic.model.dto;

import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class OmniRequest {
    private String model;
    private List<OmniMessage> messages;
    private Boolean stream;
    private Map<String, Object> stream_options;
    private List<String> modalities;
    private Map<String, Object> audio;

    @Data
    @Builder
    public static class OmniMessage {
        private String role;
        private List<OmniContent> content;
    }

    @Data
    @Builder
    public static class OmniContent {
        private String type;
        private String text;
        private Map<String, Object> input_audio;
    }

}
