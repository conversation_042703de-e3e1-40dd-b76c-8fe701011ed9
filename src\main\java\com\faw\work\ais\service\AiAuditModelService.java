package com.faw.work.ais.service;

import java.util.Map;
import java.util.List;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.AiAuditModel;
import com.faw.work.ais.model.AiTechnologyModel;
import com.faw.work.ais.model.base.PageList;

/**
* ai审核模型配置表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 10:36:35
*/
public interface AiAuditModelService {

    /**
    * 新增或修改
    */
    public Result<Integer> insertOrUpdate(AiAuditModel aiAuditModel);

    /**
    * 新增
    */
    public Result<Integer> insert(AiAuditModel aiAuditModel);

    /**
    * 删除
    */
    public Result<Integer> delete(Long id);

    /**
    * 修改
    */
    public Result<Integer> update(AiAuditModel aiAuditModel);

    /**
    * 根据Id查询
    */
    public Result<AiAuditModel> getAiAuditModelById(Long id);

   /**
    * 分页全部查询
    */
    public Result<PageList<AiAuditModel>> getAiAuditModelList(AiAuditModel aiAuditModel);

    public Result< List<AiTechnologyModel>> getAiTechnologyModelList(AiTechnologyModel aiTechnologyModel);

}

