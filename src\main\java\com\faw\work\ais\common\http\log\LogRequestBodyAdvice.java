package com.faw.work.ais.common.http.log;


import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Type;

@ControllerAdvice()
public class LogRequestBodyAdvice extends RequestBodyAdviceAdapter {
    @Resource
    private HttpLogProperties httpLogProperties;
    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return httpLogProperties.getEnable();
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        HttpLogUtil.setRequestBody(JSONObject.toJSONString(body));
        return body;
    }


}
