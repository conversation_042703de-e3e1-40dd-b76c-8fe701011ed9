package com.faw.work.ais.aic.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 手动添加文档分片请求实体
 *
 * <AUTHOR> Assistant
 */
@Data
@Accessors(chain = true)
public class RagDocumentSplitAddRequest implements Serializable {
    

    /**
     * 文档ID
     */
    @NotNull(message = "文档ID不能为空")
    private Long documentId;
    
    /**
     * 分片内容
     */
    @NotBlank(message = "分片内容不能为空")
    private String content;
    
    /**
     * 关键词，可选
     */
    private String keywords;
    
    /**
     * 配置ID，可选，不传则使用文档默认配置
     */
    private Long configId;
} 