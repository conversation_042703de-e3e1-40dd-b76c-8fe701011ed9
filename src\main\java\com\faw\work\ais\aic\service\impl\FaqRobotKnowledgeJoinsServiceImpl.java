package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.mapper.faq.*;
import com.faw.work.ais.aic.model.domain.*;
import com.faw.work.ais.aic.service.*;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqRobotKnowledgeJoinsServiceImpl extends ServiceImpl<FaqRobotKnowledgeJoinsMapper, FaqRobotKnowledgeJoinsPO> implements FaqRobotKnowledgeJoinsService {
    @Autowired
    private MilvusService milvusService;

    @Autowired
    FaqCategoryProdService faqCategoryProdService;
    @Autowired
    FaqCategoryMapper faqCategoryMapper;
    @Autowired
    FaqKnowledgeService faqKnowledgeService;

    @Autowired
    FaqKnowledgeProdService faqKnowledgeProdService;
    @Autowired
    private FaqRobotKnowledgeJoinsProdService faqRobotKnowledgeJoinsProdService;
    @Autowired
    private FaqSimilarKnowledgeProdService faqSimilarKnowledgeProdService;
    @Autowired
    private FaqKnowledgeMapper faqKnowledgeMapper;
    @Autowired
    private FaqRobotMapper faqRobotMapper;

    @Autowired
    FaqRobotKnowledgeJoinsMapper faqRobotKnowledgeJoinsMapper;

    @Autowired
    private FaqSimilarKnowledgeMapper similarKnowledgeMapper;

    @Override
    public List<FaqRobotKnowledgeJoinsPO> selectAllByRobotId(String robotId) {
        return this.lambdaQuery()
                .eq(FaqRobotKnowledgeJoinsPO::getRobotId, robotId)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void migrateAllData(List<String> robotIds) throws InterruptedException {
        // 休眠1秒避免还没提交事务导致select不出来数据
        Thread.sleep(1000);
        if (CollUtil.isEmpty(robotIds)) {
            log.error("机器人ID列表不能为空");
            return;
        }
        for (String robotId : robotIds) {
            try {
                log.info("开始迁移机器人数据: robotId={}", robotId);

                // 获取当前机器人关联的类目ID
                List<String> categoryIds = faqRobotKnowledgeJoinsMapper.selectCategoryIdsByRobotId(robotId);
                if (CollUtil.isEmpty(categoryIds)) {
                    log.warn("机器人{}未关联任何类目，跳过迁移", robotId);
                    faqRobotMapper.updateRobotStatus(robotId, 1);
                    continue;
                }

                // === 1. 首先迁移完整的类目树结构 ===
                log.info("开始迁移机器人{}关联的类目数据", robotId);
                Set<String> migratedCategoryIds = migrateCategoryHierarchy(categoryIds, robotId);
                log.info("机器人{}的类目数据迁移完成: 共{}个", robotId, migratedCategoryIds.size());

                // === 2. 逐个类目进行知识数据迁移 ===
                Set<String> migratedKnowledgeIds = new HashSet<>();
                Set<String> migratedSimilarIds = new HashSet<>();

                for (String categoryId : categoryIds) {
                    log.info("开始迁移类目数据: robotId={}, categoryId={}", robotId, categoryId);

                    // 2.1 迁移当前类目的原始知识
                    List<FaqKnowledgePO> knowledgeList = faqKnowledgeMapper.selectByCategoryIds(List.of(categoryId));
                    if (CollUtil.isNotEmpty(knowledgeList)) {
                        List<String> knowledgeIds = knowledgeList.stream()
                                .map(FaqKnowledgePO::getId)
                                .collect(Collectors.toList());

                        // 避免重复迁移
                        knowledgeIds.removeIf(migratedKnowledgeIds::contains);

                        if (!knowledgeIds.isEmpty()) {
                            log.info("迁移类目{}的原始知识: 共{}条", categoryId, knowledgeIds.size());

                            // 设置发布状态
                            List<FaqKnowledgePO> toUpdateList = knowledgeList.stream()
                                    .filter(k -> !migratedKnowledgeIds.contains(k.getId()))
                                    .peek(k -> {
                                        k.setPublishStatus("01");
                                        k.setUpdatedAt(LocalDateTime.now());
                                    })
                                    .collect(Collectors.toList());

                            if (!toUpdateList.isEmpty()) {
                                faqKnowledgeService.updateBatchById(toUpdateList);

                                // 删除正式环境旧数据
                                faqKnowledgeProdService.removeBatchByIds(knowledgeIds);

                                // 迁移到正式环境
                                List<FaqKnowledgeProdPO> prodList = toUpdateList.stream()
                                        .map(k -> {
                                            FaqKnowledgeProdPO prod = new FaqKnowledgeProdPO();
                                            BeanUtils.copyProperties(k, prod);
                                            return prod;
                                        })
                                        .collect(Collectors.toList());

                                faqKnowledgeProdService.saveBatch(prodList);
                                migratedKnowledgeIds.addAll(knowledgeIds);
                                log.info("类目{}的原始知识迁移完成: 共{}条", categoryId, prodList.size());
                            }
                        }
                    }

                    // 2.2 迁移当前类目的相似知识
                    List<FaqSimilarKnowledgePO> similarList = similarKnowledgeMapper.selectByCategoryIds(List.of(categoryId));
                    if (CollUtil.isNotEmpty(similarList)) {
                        List<String> similarIds = similarList.stream()
                                .map(FaqSimilarKnowledgePO::getId)
                                .collect(Collectors.toList());

                        // 避免重复迁移
                        similarIds.removeIf(migratedSimilarIds::contains);

                        if (!similarIds.isEmpty()) {
                            log.info("迁移类目{}的相似知识: 共{}条", categoryId, similarIds.size());

                            // 删除正式环境旧数据
                            faqSimilarKnowledgeProdService.removeBatchByIds(similarIds);

                            List<FaqSimilarKnowledgePO> toMigrateList = similarList.stream()
                                    .filter(s -> !migratedSimilarIds.contains(s.getId())).toList();

                            if (!toMigrateList.isEmpty()) {
                                List<FaqSimilarKnowledgeProdPO> similarProdList = toMigrateList.stream()
                                        .map(k -> {
                                            FaqSimilarKnowledgeProdPO prod = new FaqSimilarKnowledgeProdPO();
                                            BeanUtils.copyProperties(k, prod);
                                            return prod;
                                        })
                                        .collect(Collectors.toList());

                                faqSimilarKnowledgeProdService.saveBatch(similarProdList);
                                migratedSimilarIds.addAll(similarIds);
                                log.info("类目{}的相似知识迁移完成: 共{}条", categoryId, similarIds.size());
                            }
                        }
                    }

                    log.info("类目{}数据迁移完成: robotId={}", categoryId, robotId);
                }

                // === 3. 迁移机器人中间表和向量数据 ===
                log.info("开始迁移机器人{}的中间表和向量数据", robotId);

                // 3.1 获取正式环境中的旧中间表ID
                List<String> oldProdJoinIds = faqRobotKnowledgeJoinsProdService.getIdsByRobotId(robotId);

                // 3.2 获取当前测试环境的中间表数据
                List<FaqRobotKnowledgeJoinsPO> testJoinList =
                        faqRobotKnowledgeJoinsMapper.selectByRobotId(robotId, null);
                List<String> testJoinIds = testJoinList.stream()
                        .map(FaqRobotKnowledgeJoinsPO::getId)
                        .collect(Collectors.toList());

                // 3.3 迁移中间表到正式环境
                if (!testJoinList.isEmpty()) {
                    // 删除正式环境所有旧数据
                    faqRobotKnowledgeJoinsProdService.removeByRobotId(robotId);

                    // 插入新数据
                    List<FaqRobotKnowledgeJoinsProdPO> prodJoinList = testJoinList.stream()
                            .map(j -> {
                                FaqRobotKnowledgeJoinsProdPO prod = new FaqRobotKnowledgeJoinsProdPO();
                                BeanUtils.copyProperties(j, prod);
                                return prod;
                            })
                            .collect(Collectors.toList());

                    faqRobotKnowledgeJoinsProdService.saveBatch(prodJoinList);
                    log.info("机器人{}的中间表数据迁移完成: 共{}条", robotId, testJoinList.size());
                }

                // 3.4 迁移向量数据
                // 先删除正式环境所有旧向量
                if (!oldProdJoinIds.isEmpty()) {
                    log.info("删除机器人{}的旧向量数据: 共{}条", robotId, oldProdJoinIds.size());
                    milvusService.deleteByIds(
                            MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD,
                            oldProdJoinIds,
                            null
                    );
                    log.info("机器人{}的旧向量数据删除完成", robotId);
                }

                // 1. 优化迁移逻辑 - 分批处理
                if (!testJoinIds.isEmpty()) {
                    log.info("开始迁移机器人{}的向量数据: 共{}条", robotId, testJoinIds.size());

                    // 分批处理，避免一次性加载过多数据
                    final int batchSize = 500;

                    for (int i = 0; i < testJoinIds.size(); i += batchSize) {
                        int endIndex = Math.min(i + batchSize, testJoinIds.size());
                        List<String> batchIds = testJoinIds.subList(i, endIndex);

                        try {
                            List<MilvusRow> vectorRows = milvusService.selectRowsByIds(
                                    MilvusPoolConfig.FAQ_COLLECTION_NAME,
                                    batchIds
                            );

                            if (!vectorRows.isEmpty()) {
                                milvusService.saveOrUpdateBatch(
                                        MilvusPoolConfig.FAQ_TENANT_CLIENT_KEY,
                                        MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD,
                                        vectorRows
                                );
                            }

                            // 主动清理引用，帮助GC
                            vectorRows.clear();

                        } catch (Exception e) {
                            log.error("批次处理失败，索引范围: {}-{}", i, endIndex, e);
                            // 继续处理下一批
                        }
                    }

                    log.info("机器人{}的向量数据迁移完成: 共{}条", robotId, testJoinIds.size());
                }

                log.info("机器人{}数据迁移完成: 迁移类目:{}个, 中间表:{}条, 向量:{}条",
                        robotId, migratedCategoryIds.size(), testJoinList.size(), testJoinIds.size());
                faqRobotMapper.updateRobotStatus(robotId, 1);
            } catch (Exception e) {
                log.error("机器人{}数据迁移失败: {}", robotId, e.getMessage(), e);
                faqRobotMapper.updateRobotStatus(robotId, 10);
                log.info("机器人{}状态已更新为发布失败(10)", robotId);
            }
        }

        log.info("所有机器人数据迁移处理完成");
    }

    /**
     * 迁移类目层级结构
     * 确保从根类目到当前类目的完整路径都被迁移
     */
    private Set<String> migrateCategoryHierarchy(List<String> categoryIds, String robotId) {
        Set<String> allCategoryIds = new HashSet<>();
        Set<String> migratedCategoryIds = new HashSet<>();

        // 1. 收集所有需要迁移的类目ID（包括父级类目）
        for (String categoryId : categoryIds) {
            collectAllParentCategoryIds(categoryId, allCategoryIds);
        }

        log.info("机器人{}需要迁移的类目总数: {}个（包含父级类目）", robotId, allCategoryIds.size());

        // 2. 按层级顺序迁移类目（先迁移父级，再迁移子级）
        List<FaqCategoryPO> allCategories = getAllCategoriesByIds(allCategoryIds);
        List<FaqCategoryPO> sortedCategories = sortCategoriesByLevel(allCategories);

        for (FaqCategoryPO category : sortedCategories) {
            if (!migratedCategoryIds.contains(category.getId())) {
                try {
                    // 删除正式环境中的旧类目数据（如果存在）
                    faqCategoryProdService.removeById(category.getId());

                    // 复制到正式环境
                    FaqCategoryProdPO categoryProd = new FaqCategoryProdPO();
                    BeanUtils.copyProperties(category, categoryProd);
                    // 确保parentId被正确设置
                    categoryProd.setParentId(category.getParentId());
                    categoryProd.setUpdatedAt(LocalDateTime.now());

                    faqCategoryProdService.save(categoryProd);
                    migratedCategoryIds.add(category.getId());

                    String level = category.getParentId() == null ? "一级" : "子级";
                    log.info("{}类目迁移完成: categoryId={}, name={}, robotId={}",
                            level, category.getId(), category.getName(), robotId);

                } catch (Exception e) {
                    log.error("类目迁移失败: categoryId={}, name={}, robotId={}, error={}",
                            category.getId(), category.getName(), robotId, e.getMessage(), e);
                    // 类目迁移失败不中断整个流程，继续后续迁移
                }
            }
        }

        return migratedCategoryIds;
    }

    /**
     * 递归收集当前类目及其所有父级类目ID
     */
    private void collectAllParentCategoryIds(String categoryId, Set<String> allCategoryIds) {
        if (StrUtil.isBlank(categoryId) || allCategoryIds.contains(categoryId)) {
            return;
        }

        allCategoryIds.add(categoryId);

        // 查询当前类目信息
        FaqCategoryPO category = faqCategoryMapper.selectById(categoryId);
        if (category != null && StrUtil.isNotBlank(category.getParentId())) {
            // 递归收集父类目
            collectAllParentCategoryIds(category.getParentId(), allCategoryIds);
        }
    }

    /**
     * 批量查询类目信息
     */
    private List<FaqCategoryPO> getAllCategoriesByIds(Set<String> categoryIds) {
        if (CollUtil.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        return faqCategoryMapper.selectBatchIds(categoryIds);
    }

    /**
     * 按层级排序类目，确保父级类目在子级类目之前
     * 一级类目（parentId为null）排在前面，然后按层级递归排序
     */
    private List<FaqCategoryPO> sortCategoriesByLevel(List<FaqCategoryPO> categories) {
        if (CollUtil.isEmpty(categories)) {
            return new ArrayList<>();
        }

        // 构建类目映射
        Map<String, FaqCategoryPO> categoryMap = categories.stream()
                .collect(Collectors.toMap(FaqCategoryPO::getId, Function.identity()));

        List<FaqCategoryPO> sortedList = new ArrayList<>();
        Set<String> processed = new HashSet<>();

        // 先处理一级类目（parentId为null）
        categories.stream()
                .filter(c -> c.getParentId() == null)
                .forEach(rootCategory -> addCategoryWithChildren(rootCategory, categoryMap, sortedList, processed));

        // 处理可能遗漏的类目（理论上不应该有，但为了保险起见）
        categories.stream()
                .filter(c -> !processed.contains(c.getId()))
                .forEach(category -> addCategoryWithChildren(category, categoryMap, sortedList, processed));

        return sortedList;
    }

    /**
     * 递归添加类目及其子类目
     */
    private void addCategoryWithChildren(FaqCategoryPO category,
                                         Map<String, FaqCategoryPO> categoryMap,
                                         List<FaqCategoryPO> sortedList,
                                         Set<String> processed) {
        if (processed.contains(category.getId())) {
            return;
        }

        // 添加当前类目
        sortedList.add(category);
        processed.add(category.getId());

        // 查找并添加子类目
        categoryMap.values().stream()
                .filter(c -> Objects.equals(c.getParentId(), category.getId()))
                .forEach(childCategory -> {
                    addCategoryWithChildren(childCategory, categoryMap, sortedList, processed);
                });
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void migrateKnowledge(String knowledgeId) throws InterruptedException {
        // === 1. 更新主知识状态 ===
        FaqKnowledgePO knowledgePo = faqKnowledgeMapper.selectById(knowledgeId);
        knowledgePo.setPublishStatus("01");
        knowledgePo.setUpdatedAt(LocalDateTime.now());
        faqKnowledgeService.updateById(knowledgePo);

        // === 2. 迁移原始知识到正式环境 ===
        faqKnowledgeProdService.removeById(knowledgeId);
        FaqKnowledgeProdPO original = new FaqKnowledgeProdPO();
        BeanUtils.copyProperties(knowledgePo, original);
        faqKnowledgeProdService.saveOrUpdate(original);

        // === 3. 迁移相似知识到正式环境 ===
        List<FaqSimilarKnowledgePO> similarKnowledgeList = similarKnowledgeMapper.selectByOriginalId(knowledgeId);
        List<String> similarIds = similarKnowledgeList.stream()
                .map(FaqSimilarKnowledgePO::getId)
                .collect(Collectors.toList());

        if (!similarIds.isEmpty()) {
            faqSimilarKnowledgeProdService.removeBatchByIds(similarIds);
            List<FaqSimilarKnowledgeProdPO> similarProdList = similarKnowledgeList.stream()
                    .map(k -> {
                        FaqSimilarKnowledgeProdPO prod = new FaqSimilarKnowledgeProdPO();
                        BeanUtils.copyProperties(k, prod);
                        return prod;
                    })
                    .collect(Collectors.toList());
            faqSimilarKnowledgeProdService.saveBatch(similarProdList);
        }

        // === 4. 迁移中间表 ===
        // 获取所有需要迁移的知识ID（原始知识+所有相似问）
        List<String> allKnowledgeIds = new ArrayList<>();
        allKnowledgeIds.add(knowledgeId);
        allKnowledgeIds.addAll(similarIds);

        // 获取所有关联的join记录（包含原始知识和相似问）
        List<FaqRobotKnowledgeJoinsPO> allJoinRecords = new ArrayList<>();
        for (String kid : allKnowledgeIds) {
            allJoinRecords.addAll(faqRobotKnowledgeJoinsMapper.selectByKnowledgeId(kid));
        }

        if (!allJoinRecords.isEmpty()) {
            // 删除正式环境旧数据
            List<String> joinIds = allJoinRecords.stream()
                    .map(FaqRobotKnowledgeJoinsPO::getId)
                    .collect(Collectors.toList());
            faqRobotKnowledgeJoinsProdService.removeBatchByIds(joinIds);

            // 迁移到正式环境
            List<FaqRobotKnowledgeJoinsProdPO> joinProdList = allJoinRecords.stream()
                    .map(j -> {
                        FaqRobotKnowledgeJoinsProdPO prod = new FaqRobotKnowledgeJoinsProdPO();
                        BeanUtils.copyProperties(j, prod);
                        return prod;
                    })
                    .collect(Collectors.toList());
            faqRobotKnowledgeJoinsProdService.saveBatch(joinProdList);

            // === 5. 迁移向量数据 === (修复关键问题)
            // 从测试环境获取所有向量记录
            Thread.sleep(2000);
            List<MilvusRow> vectorSearchResults = milvusService.selectRowsByIds(
                    MilvusPoolConfig.FAQ_COLLECTION_NAME,
                    joinIds
            );
            log.info("获取向量数据成功: 共{}条", vectorSearchResults.size());

            if (!vectorSearchResults.isEmpty()) {
                log.info("开始迁移向量数据: 共{}条", vectorSearchResults.size());
                // 删除正式环境旧向量
                milvusService.deleteByIds(
                        MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD,
                        joinIds,
                        null
                );

                // 插入新向量
                milvusService.saveOrUpdateBatch(
                        MilvusPoolConfig.FAQ_TENANT_CLIENT_KEY,
                        MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD,
                        vectorSearchResults
                );
                log.info("向量数据迁移完成: 共{}条", vectorSearchResults.size());
            }
        }

        log.info("知识迁移完成: knowledgeId={}, 迁移知识数量={}", knowledgeId, allKnowledgeIds.size());
    }
}
