package com.faw.work.ais.aic.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.dto.ModelEvaluationInputDTO;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.AiResponse;
import com.faw.work.ais.aic.model.response.MessageQueueCleanResponse;
import com.faw.work.ais.aic.model.response.ModelEvaluationResponse;
import com.faw.work.ais.aic.model.response.RetryFailedMessagesResponse;
import com.faw.work.ais.aic.service.LlmRecordService;
import com.faw.work.ais.aic.service.LlmRecordServiceV2;
import com.faw.work.ais.aic.service.ModelEvaluationService;
import com.faw.work.ais.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/llm-record")
@Slf4j
@Tag(name = "LlmRecordController", description = "处理外部调用大模型请求")
public class LlmRecordController {
    @Autowired
    private LlmRecordService llmRecordService;

    @Autowired
    private LlmRecordServiceV2 llmRecordServiceV2;
    @Autowired
    private ModelEvaluationService modelEvaluationService;

    /**
     * 处理对话，包括情绪分析和标签提取
     *
     * @param request 包含文本和语音COS URL的请求
     * @return 处理结果
     */
    @Operation(summary = "处理对话", description = "[author:10200571]")
    @PostMapping("/process-conversation")
    public AiResult<String> processConversation(@Valid @RequestBody ProcessRequest request) {
        log.info("开始处理对话，请求ID: {}, 用户输入: {},  语音URL: {}", request.getRequestId(), request.getUserInput(), request.getAudioUrl());
        llmRecordService.processConversation(request);
        return AiResult.successMsg("成功接收消息,请关注回调消息");
    }

    /**
     * 重试失败的记录
     *
     * @param request 包含请求ID的请求
     * @return 处理结果
     */
    @Operation(summary = "重试失败记录", description = "[author:10200571]")
    @PostMapping("/retry-failed")
    public AiResult<String> retryFailedRecords(@Valid @RequestBody RetryRequest request) {
        log.info("开始重试失败记录，请求ID: {}", request.getRequestId());
        for (String requestId : request.getRequestId()) {
            llmRecordService.retryFailedRecords(requestId);
        }
        return AiResult.successMsg("重试成功");
    }

    /**
     * 清理已完成的消息队列数据
     *
     * @return 清理结果
     */
    @Operation(summary = "清理已完成的消息队列", description = "[author:10200571]")
    @PostMapping("/clean-completed-messages")
    public AiResult<MessageQueueCleanResponse> cleanCompletedMessages() {
        log.info("开始清理已完成的消息队列数据");
        MessageQueueCleanResponse response = llmRecordService.cleanCompletedMessages();
        return AiResult.success(response);
    }

    @Operation(summary = "处理对话", description = "[author:10200571]")
    @PostMapping("/process-conversation-demo")
    public AiResult<String> processConversationDemo(@Valid @RequestBody ProcessRequest request) {
        log.info("开始处理对话，请求ID: {}, 用户输入: {},  语音URL: {}", request.getRequestId(), request.getUserInput(), request.getAudioUrl());
        llmRecordServiceV2.processConversationAbTest(request);
        return AiResult.successMsg("成功接收消息,请关注回调消息");
    }

    /**
     * 测试窗口接口 - 用于测试AI模型对话功能
     *
     * @param request 包含模型配置和用户输入的请求
     * @return AI模型响应结果
     */
    @Operation(summary = "测试窗口AI", description = "[author:10200571]")
    @PostMapping("/test-chat")
    public AiResult<AiResponse> testChat(@Valid @RequestBody AiRequest request) {
        log.info("开始测试AI对话，模型: {}, 用户输入: {}", request.getModelName(), request.getUserInput());
        return AiResult.success(llmRecordServiceV2.testChat(request));
    }

    /**
     * 重试所有失败的消息队列
     *
     * @param request 重试请求参数
     * @return 重试结果
     */
    @Operation(summary = "重试失败的消息队列", description = "[author:10200571]")
    @PostMapping("/retry-failed-messages")
    public AiResult<RetryFailedMessagesResponse> retryFailedMessages(@Valid @RequestBody RetryFailedMessagesRequest request) {
        log.info("开始重试所有失败的消息队列，清理成功数据: {}", request.getCleanSuccessData());
        RetryFailedMessagesResponse response = llmRecordService.retryAllFailedMessages(request);
        return AiResult.success(response);
    }

    /**
     * 大模型评测接口
     *
     * 入参的参数都要放在body中的form-data中，包括：file、request
     *
     * @param file Excel文件，包含user_input和model_result两列
     * @return 评测结果
     */
    @Operation(summary = "大模型评测", description = "[author:10200571]")
    @PostMapping("/model-evaluation")
    public AiResult<ModelEvaluationResponse> modelEvaluation(
            @RequestParam("file") MultipartFile file,
            @RequestParam("request") String requestJson) {

        log.info("开始大模型评测，文件名: {}, 参数: {}", file.getOriginalFilename(), requestJson);

        try {
            // 解析JSON参数
            ModelEvaluationRequest request = JSONUtil.toBean(requestJson, ModelEvaluationRequest.class);

            // 执行评测
            ModelEvaluationResponse response = modelEvaluationService.evaluateModel(file, request);

            return AiResult.success(response);
        } catch (Exception e) {
            log.error("大模型评测失败", e);
            throw new BizException("大模型评测失败: " + e.getMessage());
        }
    }


    @Operation(summary = "下载大模型评测导入模板", description = "[author:10200571]")
    @GetMapping("/model-evaluation-download-template")
    public void downloadEvaluationTemplate(HttpServletResponse response) {

        log.info("开始下载大模型评测导入模板");

        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 设置文件名
            String fileName = URLEncoder.encode("大模型评测导入模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");



            // 使用EasyExcel写入数据
            EasyExcel.write(response.getOutputStream(), ModelEvaluationInputDTO.class)
                    .sheet("评测数据")
                    .doWrite(List.of());

            log.info("大模型评测导入模板下载成功");

        } catch (IOException e) {
            log.error("下载大模型评测导入模板失败", e);
            throw new BizException("模板下载失败: " + e.getMessage());
        }
    }





}
