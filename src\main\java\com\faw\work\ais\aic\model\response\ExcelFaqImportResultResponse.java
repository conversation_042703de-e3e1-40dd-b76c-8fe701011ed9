package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 导入结果响应对象
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "导入结果响应对象")
public class ExcelFaqImportResultResponse {
    
    @Schema(description = "总数量")
    private int total;
    
    @Schema(description = "成功数量")
    private int successCount;
    
    @Schema(description = "失败数量")
    private int failCount;
    
    @Schema(description = "错误信息列表")
    private List<String> errorMessages;
}
