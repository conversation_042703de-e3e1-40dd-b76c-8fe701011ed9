package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 人工客服跳转 实体类
 *
 * <AUTHOR>
 * @since 2025-06-30 14:15
 */
@Data
@Builder
@Schema(description = "人工客服跳转 实体类")
public class StaffServiceEntity {

    /**
     * 跳转来源
     */
    @Schema(description = "跳转来源")
    private String jumpFrom;

    /**
     * 技能组编码
     */
    @Schema(description = "技能组编码")
    private String skillGroup;

    /**
     * 总结的话
     */
    @Schema(description = "总结的话")
    private String sentence;

}
