package com.faw.work.ais.controller;

import com.faw.work.ais.common.dto.chat.AiTestRequest;
import com.faw.work.ais.service.AiTestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AI测试 控制类
 *
 * <AUTHOR>
 * @since 2025-05-19 8:44
 */
@Schema(description = "AI测试 控制类")
@RestController
@RequestMapping("/aiTest")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiTestController {

    private final AiTestService aiTestService;


    @Operation(summary = "验证问题答案", description = "[author:10236535]")
    @PostMapping(value = "/verifyQuestionAnswer")
    public String verifyQuestionAnswer(@RequestBody AiTestRequest request) {
        return aiTestService.verifyQuestionAnswer(request);
    }

    @Operation(summary = "文字转语音", description = "[author:10236535]")
    @PostMapping(value = "/textToVoice")
    public String textToVoice(@RequestBody AiTestRequest request) {
        return aiTestService.textToVoice(request);
    }

    @Operation(summary = "测试语音识别", description = "[author:10236535]")
    @PostMapping(value = "/testAsr")
    public String testAsr(@RequestBody AiTestRequest request) {
        return aiTestService.testAsr(request);
    }

}
