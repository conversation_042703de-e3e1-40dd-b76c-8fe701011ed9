package com.faw.work.ais.controller;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.OcrDTO;
import com.faw.work.ais.service.OcrService;
import com.tencentcloudapi.ocr.v20181119.models.MixedInvoiceItem;
import com.tencentcloudapi.ocr.v20181119.models.TextVehicleFront;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Schema(description = "OCR控制器")
@RestController
@RequestMapping("/recognize")
public class OcrController {

    @Autowired
    private OcrService ocrService;

    @Operation(summary = "车牌识别", description = "[author:10236535]")
    @PostMapping(value = "/plateLicense")
    public Response<String> plateLicense(@RequestBody OcrDTO ocrDTO) {
        return Response.success(ocrService.plateLicense(ocrDTO));
    }


    @Operation(summary = "车辆vin识别", description = "[author:10236535]")
    @PostMapping(value = "/vinLicense")
    public Response<String> vinLicense(@RequestBody OcrDTO ocrDTO) {
        return Response.success(ocrService.vinLicense(ocrDTO));
    }

    @Operation(summary = "行驶证识别（正本）", description = "[author:10236535]")
    @PostMapping(value = "/driverLicenseFront")
    public Response<TextVehicleFront> driverLicenseFront(@RequestBody OcrDTO ocrDTO) {
//        return ocrService.driverLicenseFront(ocrDTO);
        return ocrService.driverLicenseFrontForCut(ocrDTO,null,false);
    }


    @Operation(summary = "购车发票识别", description = "[author:10236535]")
    @PostMapping(value = "/motorVehicleSaleInvoice")
    public Response<MixedInvoiceItem[]> motorVehicleSaleInvoice(@RequestBody OcrDTO ocrDTO) {
        return ocrService.motorVehicleSaleInvoice(ocrDTO);
    }

}
