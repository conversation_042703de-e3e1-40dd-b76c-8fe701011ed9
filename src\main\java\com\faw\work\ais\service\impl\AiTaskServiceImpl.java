package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dcp.common.files.vo.FileInfoVO;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.enums.ResEnum;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.FileUtil;
import com.faw.work.ais.common.util.HttpUtil;
import com.faw.work.ais.config.WhitelistConfig;
import com.faw.work.ais.entity.dto.BaseOutDTO;
import com.faw.work.ais.entity.dto.MessageDTO;
import com.faw.work.ais.entity.dto.ai.*;
import com.faw.work.ais.entity.dto.python.AiTaskFormPythonDTO;
import com.faw.work.ais.entity.vo.ai.AiPythonTaskVO;
import com.faw.work.ais.entity.vo.ai.AiResultVO;
import com.faw.work.ais.entity.vo.ai.AiTaskVO;
import com.faw.work.ais.feign.ApiTaskFeignClientInvokes;
import com.faw.work.ais.feign.BnzxOpenApiFeignClient;
import com.faw.work.ais.feign.IworkOpenApiFeignClient;
import com.faw.work.ais.mapper.ais.*;
import com.faw.work.ais.message.RabbitService;
import com.faw.work.ais.model.*;
import com.faw.work.ais.service.AiTaskResultService;
import com.faw.work.ais.service.AiTaskService;
import com.faw.work.ais.service.DingRobotService;
import com.faw.work.ais.service.OcrService;
import com.tencentcloudapi.ocr.v20181119.models.MixedInvoiceItem;
import com.tencentcloudapi.ocr.v20181119.models.SingleInvoiceInfo;
import com.tencentcloudapi.ocr.v20181119.models.TextVehicleBack;
import com.tencentcloudapi.ocr.v20181119.models.TextVehicleFront;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;

import java.net.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiTaskServiceImpl implements AiTaskService {

    @Value("${ding.robot.access_token}")
    private String robotToken;

    @Value("${ding.robot.robotSecret}")
    private String robotSecret;

    @Value("${ding.robot.keyWordOne}")
    private String robotKey;

    @Value("ding.userId.ruleError")
    private String ruleError_dingdingUserId;

    private final FileMapper fileMapper;

    private final TaskRuleMapper taskRuleMapper;

    private final AiTaskResultMapper aiTaskResultMapper;

    private final LogInfoJavaMapper logInfoJavaMapper;

    private final LogInfoPythonCallbackMapper logInfoPythonCallbackMapper;

    private final ApiTaskFeignClientInvokes taskFeignClientInvokes;

    private final AiTaskResultService taskResultService;

    private final IworkOpenApiFeignClient iworkOpenApiFeignClient;

    private final BnzxOpenApiFeignClient bnzxOpenApiFeignClient;

    private final RabbitService rabbitService;

    private final DingRobotService dingRobotService;

    private final WhitelistConfig whitelistConfig;

    private final OcrService ocrService;

    private final FileInfoNewDao fileInfoNewDao;

    private final AiTaskResultNewDao aiTaskResultNewDao;

    private final FileUtil fileUtil;

    // 请求标识；2-批量请求
     final String BATCHFLAG_MORE = "2";

    // 请求标识；1-单次请求
    final String BATCHFLAG_SINGLE = "1";

    /**
     * 日志表更新-更新python请求的入参
     * @param aiTaskDTO
     * @param cosQueueDTO
     * @param pythonInParam
     * @param flag 1-同步请求；2-异步请求；
     * @param updateColumnFlag in-更新python请求入参；result-更新python请求结果
     * @return
     */
    private int assimblePythonInParamAndUpdate(AiTaskDTO aiTaskDTO, CosQueueDTO cosQueueDTO, String pythonInParam, String flag, String updateColumnFlag){
        LogInfoPythonCallbackWithBLOBs logInfoPythonCallback = new LogInfoPythonCallbackWithBLOBs();
        Date now = new Date();
        if("1".equals(flag)){
            logInfoPythonCallback.setBatchId(aiTaskDTO.getBatchId());
            logInfoPythonCallback.setTraceId(aiTaskDTO.getTraceId());
        }
        if("2".equals(flag)){
            logInfoPythonCallback.setBatchId(cosQueueDTO.getBatchId());
            logInfoPythonCallback.setTraceId(cosQueueDTO.getTraceId());
        }

        LogInfoPythonCallbackWithBLOBs updateInfo = logInfoPythonCallbackMapper.selectByObj(logInfoPythonCallback);
        if("in".equals(updateColumnFlag)){
            updateInfo.setPythonCallTime(now);
            updateInfo.setPythonParam(pythonInParam);
        }
        if("result".equals(updateColumnFlag)){
            updateInfo.setPythonResultTime(now);
            updateInfo.setPythonResultJson(pythonInParam);
        }
        int result = logInfoPythonCallbackMapper.updateByPrimaryKey(updateInfo);
        return result;
    }

    private LogInfoPythonCallbackWithBLOBs assimbleCallBackParam(LogInfoPythonCallbackWithBLOBs inParam, String callBackParam){
        Date now = new Date();
        inParam.setCallbackParam(callBackParam);
        inParam.setCallbackTime(now);
        inParam.setUpdateTime(now);
        return inParam;
    }

    private LogInfoPythonCallbackWithBLOBs assimbleCallBackResultParam(LogInfoPythonCallbackWithBLOBs inParam, String callBackResultParam){
        Date now = new Date();
        inParam.setCallbackResult(callBackResultParam);
        inParam.setCallbackResultTime(now);
        inParam.setUpdateTime(now);
        return inParam;
    }

    private boolean writeListInterceptor(String systemId){
        List<String> systemIds = whitelistConfig.getSystemIds();
        if (systemIds.contains(systemId)) {
            return true; // 域名在白名单中，继续处理请求
        } else {
            return false; // 域名不在白名单中，禁止访问
        }
    }

    /**
     * 判断字符串是否为json格式
     * @param str
     * @return true-是；false-否
     */
    public boolean isJsonStr(String str) {
        log.info("--------isJsonStr入参------" + str);
        boolean result = false;
        try {
            Object obj=JSON.parse(str);
            result = true;
        } catch (Exception e) {
            result=false;
        }
        return result;
    }

    @Override
    public Response asyncAiTask(AiTaskDTO aiTaskDTO) {
        log.info(aiTaskDTO.getTraceId() + "--AiTaskServiceImpl.asyncAiTask--接口入参："  + JSON.toJSONString(aiTaskDTO));
        if(!writeListInterceptor(aiTaskDTO.getSystemId())){
            return Response.fail(ResEnum.F_117);
        }
        try {
            // 保存入参内容到java日志表
            savaJavaLogWhenGetRequest(aiTaskDTO,null, BATCHFLAG_SINGLE);
            // 保存入参内容到python日志表
            savaPythonLogWhenGetRequest(aiTaskDTO);

            return Response.success(asyncDeal(aiTaskDTO));
        }catch (Exception ex){
            log.error(aiTaskDTO.getTraceId() + "--asyncAiTask接口处理失败--" + ex.getMessage() + ex.getCause());
            return Response.fail(ex.getMessage() + ex.getCause());
        }
    }

    /**
     * 接到请求时报错java入参日志
     * @Param aiTaskDTO
     * @Param batchFlag 1-单次请求(从/asyncAiTask接口过来的请求)；2-批量请求（从/asyncAiTaskBatch或者/asyncPushToMq过来的请求）
     */
    private void savaJavaLogWhenGetRequest(AiTaskDTO aiTaskDTO,List<AiTaskDTO> aiTaskDTOS, String batchFlag){
        // 根据batch_id查询日志是否存在；存在则不保存
        if(BATCHFLAG_MORE.equals(batchFlag)){
            aiTaskDTO = aiTaskDTOS.get(0);
        }
        LogInfoJava logInfoJava = logInfoJavaMapper.getInfoByBatchId(aiTaskDTO.getBatchId());
        if(logInfoJava == null){
            // 保存入参内容到java日志表
            LogInfoJava logInfoJavaNew = assimbleLogInfoJava(aiTaskDTO, aiTaskDTOS, batchFlag);
            logInfoJavaMapper.insert(logInfoJavaNew);
        }else {
            logInfoJava.setUpdateTime(new Date());
            logInfoJavaMapper.updateByPrimaryKeySelective(logInfoJava);
        }
    }

    /**
     * 保存batch_id,trace_id到python日志表
     * @param aiTaskDTO
     */
    private void savaPythonLogWhenGetRequest(AiTaskDTO aiTaskDTO){
        // 根据batch_id,traceId查询日志是否存在；存在则不保存
        LogInfoPythonCallbackWithBLOBs selectObj = new LogInfoPythonCallbackWithBLOBs();
        selectObj.setTraceId(aiTaskDTO.getTraceId());
        selectObj.setBatchId(aiTaskDTO.getBatchId());
        LogInfoPythonCallbackWithBLOBs logInfoPythonCallback = logInfoPythonCallbackMapper.selectByObj(selectObj);
        if(logInfoPythonCallback == null){
            // 保存入参内容到python日志表
            assimbleLogInfoPythonAndSave(aiTaskDTO);
        }else {
            logInfoPythonCallback.setUpdateTime(new Date());
            logInfoPythonCallback.setPushCount(logInfoPythonCallback.getPushCount() + 1);
            logInfoPythonCallbackMapper.updateByPrimaryKeyWithBLOBs(logInfoPythonCallback);
        }
    }

    /**
     * 保存batch_id,trace_id到python日志表
     * @Param aiTaskDTO
     */

    /**
     * 拼装java请求日志的被调用入参
     * @param aiTaskDTO 单个参数
     * @param aiTaskDTOs 多个参数
     * @param flag 1-单次请求；2-批量请求
     * @return
     */
    private LogInfoJava assimbleLogInfoJava(AiTaskDTO aiTaskDTO, List<AiTaskDTO> aiTaskDTOs, String flag){
        LogInfoJava logInfoJava = new LogInfoJava();
        Date now = new Date();
        logInfoJava.setJavaCallTime(now);
        logInfoJava.setCreateTime(now);
        // 单次请求参数记录
        if("1".equals(flag)){
            String param = JSON.toJSONString(aiTaskDTO);
            logInfoJava.setBatchId(aiTaskDTO.getBatchId());
            logInfoJava.setJavaParam(param);
            logInfoJava.setSystemId(aiTaskDTO.getSystemId());
        }
        //  批量请求参数记录
        if("2".equals(flag)){
            AiTaskDTO aiTaskDTOBatch = aiTaskDTOs.get(0);
            String param = JSON.toJSONString(aiTaskDTOs);
            logInfoJava.setBatchId(aiTaskDTOBatch.getBatchId());
            logInfoJava.setJavaParam(param);
            logInfoJava.setSystemId(aiTaskDTOBatch.getSystemId());
        }
        return logInfoJava;
    }

    /**
     * 新增loginfo-python表数据
     * @param aiTaskDTO
     */
    private void assimbleLogInfoPythonAndSave(AiTaskDTO aiTaskDTO){
        Date now = new Date();
        LogInfoPythonCallbackWithBLOBs infoPython = new LogInfoPythonCallbackWithBLOBs();
        infoPython.setBatchId(aiTaskDTO.getBatchId());
        infoPython.setTraceId(aiTaskDTO.getTraceId());
        infoPython.setAuditModelSceneId(aiTaskDTO.getAuditModelSceneId());
        infoPython.setCreateTime(now);
        infoPython.setPushCount(0);
        logInfoPythonCallbackMapper.insert(infoPython);
    }

    @Override
    public Response asyncAiTaskBatch(List<AiTaskDTO> aiTaskDTOs) {
        log.info("-----asyncAiTaskBatch入参Json格式--------"  + JSON.toJSONString(aiTaskDTOs));

        if(CollectionUtils.isEmpty(aiTaskDTOs)){
            return Response.success(ResEnum.F_118);
        }
        if(!writeListInterceptor(aiTaskDTOs.get(0).getSystemId())){
            return Response.fail(ResEnum.F_117);
        }
        // 保存入参内容到java日志表
        savaJavaLogWhenGetRequest(null,aiTaskDTOs, BATCHFLAG_MORE);

        AtomicReference<String> message = new AtomicReference<>("");
        try {
            aiTaskDTOs.stream().forEach(x -> {
                try {
                    // 保存入参内容到python日志表
                    savaPythonLogWhenGetRequest(x);
                    String result = asyncDeal(x);
                } catch (Exception e) {
                    log.warn("错误信息：", e);
                    message.set(message + e.getMessage());
                }
            });

        }catch (Exception ex){
            log.error("--asyncAiTaskBatch接口处理失败--", ex);
            return Response.fail(ex.getMessage()+message);
        }
        return Response.success(ResEnum.SUCCESS_CODE);
    }

    private String asyncDeal(AiTaskDTO aiTaskDTO){
        log.info("[AiTaskService][asyncDeal][entrance] aiTaskDTO: {}", JSON.toJSONString(aiTaskDTO));

        log.info("[AiTaskService][asyncDeal] -------------开始插入消息队列前置处理-----------------");
        AiTaskVO aiTaskVO = new AiTaskVO();
        BeanUtils.copyProperties(aiTaskDTO,aiTaskVO);

        List<BeCheckFileDTO> contents = aiTaskDTO.getContents();
        // 给Python服务的文件入参
        List<BeCheckFileDTO> toPythonFiles = new ArrayList<>();

        if(whitelistConfig.getFileUndoChecks().contains(aiTaskDTO.getSystemId())){
            // 推送消息队列
            sendAiTaskMessage(aiTaskDTO, toPythonFiles, contents);

        }else {
            if(contents != null && !contents.isEmpty()) {
                // 补能的单独处理(不能中心测试用的代码，测试时候输入test字段为true，直接返回，正式使用test字段没有值)
                if("BNZX".equals(aiTaskDTO.getSystemId())){
                    if("true".equals(aiTaskDTO.getTest())){
                        return getString(aiTaskDTO, null);
                    }
                }
                contents.forEach(x -> {
                    try {
                        // 计算文件的MD5值并上传文件到cos
                        CosInfoDTO cosDto = pushToCos(x, aiTaskDTO.getBizId());
                        // 构造给python服务的fileUrl为本次COS桶中的Url
                        BeCheckFileDTO toPythonFileInfos = BeCheckFileDTO.builder().fileUrl(cosDto.getFilePath())
                                .fileContentType(x.getFileContentType()).version(x.getVersion()).fileIndex(x.getFileIndex())
                                .fileDesc(x.getFileDesc()).cosKey(cosDto.getKey()).build();
                        toPythonFiles.add(toPythonFileInfos);

                    }catch (Exception ex){
                        log.warn("--asyncAiTask上传cos文件服务器错误-- message: ", ex);
                        Response.fail(ResEnum.F_114);
                    }
                });
                // 推送消息队列
                sendAiTaskMessage(aiTaskDTO, toPythonFiles, contents);
            }else{
                // 目前任务运营中心不需要使用推送消息队列
                sendAiTaskMessageNoFileInfo(aiTaskDTO);
            }
        }
        log.info("[AiTaskService][asyncDeal] -------------完成插入消息队列前置处理-----------------");
        return "";
    }

    private @NotNull String getString(AiTaskDTO aiTaskDTO, CosQueueDTO cosQueueDTO) {
        JSONObject jsonObject = JSON.parseObject(aiTaskDTO.getGivenInfoJson());
        // 使用OCR
        OcrDTO ocrDTO = new OcrDTO();
        ocrDTO.setUrl(aiTaskDTO.getContents().get(0).getFileUrl());
        AiPythonTaskVO aiPythonTaskVO = new AiPythonTaskVO();
        aiPythonTaskVO.setT(false);
        if("GB_05".equals(aiTaskDTO.getTaskType())){
            Response<MixedInvoiceItem[]> response = ocrService.motorVehicleSaleInvoice(ocrDTO);
            if("-111".equals(response.getCode())){
                aiPythonTaskVO.setExplanation(response.getMessage());
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException(response.getMessage());
            }
            MixedInvoiceItem[] data = response.getData();
            if(data.length<1){
                aiPythonTaskVO.setExplanation("识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("识别失败");
            }
            MixedInvoiceItem mixedInvoiceItem = data[0];
            if(!"OK".equals(mixedInvoiceItem.getCode())){
                aiPythonTaskVO.setExplanation("识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("识别失败");
            }
            List<SingleInvoiceInfo> singleInvoiceInfoList = Arrays.asList(mixedInvoiceItem.getSingleInvoiceInfos());
            if(CollectionUtils.isEmpty(singleInvoiceInfoList)){
                aiPythonTaskVO.setExplanation("图片不是购车发票或图片模糊");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("图片不是购车发票或图片模糊");
            }
            SingleInvoiceInfo singleInvoiceInfo = singleInvoiceInfoList.stream().filter(item -> item.getName().contains("车辆识别代号")).findFirst().get();
            if(StringUtils.isBlank(singleInvoiceInfo.getValue())){
                aiPythonTaskVO.setExplanation("识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("识别失败");
            }
            if(!singleInvoiceInfo.getValue().equalsIgnoreCase((String) jsonObject.get("vvin"))){
                aiPythonTaskVO.setExplanation("vin码不一致");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("vin码不一致");
            }

            singleInvoiceInfo = singleInvoiceInfoList.stream().filter(item -> item.getName().contains("身份证号码/组织机构代码")).findFirst().get();
            if(StringUtils.isBlank(singleInvoiceInfo.getValue())){
                aiPythonTaskVO.setExplanation("识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("识别失败");
            }
            if(!singleInvoiceInfo.getValue().equals(jsonObject.get("custermCode"))){
                aiPythonTaskVO.setExplanation("用户编码不一致");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("用户编码不一致");
            }
            aiPythonTaskVO.setT(true);
            aiPythonTaskVO.setExplanation("满足所有规则");
            successCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
            return "满足所有规则";
        }else if("GB_06".equals(aiTaskDTO.getTaskType())){
            // 行驶证主页
//            Response<TextVehicleFront> response = ocrService.driverLicenseFront(ocrDTO);
            Response<TextVehicleFront> response = ocrService.driverLicenseFrontForCut(ocrDTO,null,false);
            if("-111".equals(response.getCode())){
                aiPythonTaskVO.setExplanation(response.getMessage());
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new RuntimeException(response.getMessage());
            }
            TextVehicleFront textVehicleFront = response.getData();
            if(StringUtils.isBlank(textVehicleFront.getPlateNo())){
                aiPythonTaskVO.setExplanation("号牌号码识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("号牌号码识别失败");
            }
            if(StringUtils.isBlank(textVehicleFront.getVehicleType())){
                aiPythonTaskVO.setExplanation("车辆类型识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("车辆类型识别失败");
            }
            if(StringUtils.isBlank(textVehicleFront.getUseCharacter())){
                aiPythonTaskVO.setExplanation("使用性质识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("使用性质识别失败");
            }
            if(StringUtils.isBlank(textVehicleFront.getVin())){
                aiPythonTaskVO.setExplanation("Vin识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("Vin识别失败");
            }
            // 处理业务判断
            String GB_06_DESC = "vvin是车架号，vlicense是车牌号，dlicense是注册日期，owner是客户名称，custermCode是客户代码(只传前两位)，carUser是车辆用途(传汉字)， custermType是客户类型(传汉字)";
            if(!textVehicleFront.getVin().equals(jsonObject.get("vvin"))){
                aiPythonTaskVO.setExplanation("vin码不一致");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("vin码不一致");
            }
            if(!textVehicleFront.getPlateNo().equals(jsonObject.get("vlicense"))){
                aiPythonTaskVO.setExplanation("车牌号不一致");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("车牌号不一致");
            }
            if(!textVehicleFront.getRegisterDate().equals(jsonObject.get("dlicense"))){
                aiPythonTaskVO.setExplanation("注册日期不一致");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("注册日期不一致");
            }
            if(!ignoreSymbolEquals(textVehicleFront.getOwner(),(String)jsonObject.get("owner"))){
                aiPythonTaskVO.setExplanation("客户名称不一致");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("客户名称不一致");
            }
            if("非营运".equals(textVehicleFront.getUseCharacter())){
                if("个人".equals(jsonObject.get("custermType")) && !"私人乘用车".equals(jsonObject.get("carUser")) ){
                    aiPythonTaskVO.setExplanation("车辆用途不对");
                    failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                    throw new BizException("车辆用途不对");
                }
                //S22产品修改规则，prd：https://iwork.faw.cn/development/docs?businessId=4655969749&nodeKey=de8a1e51cc52443382c2fb38669e5e86
                // 2.1 #故事#新能源溯源及监控管理流程国补信息运营-溯源信息分析-AI审核规则改造
                if("企业".equals(jsonObject.get("custermType")) &&
                        !"11".equals(jsonObject.get("custermCode")) &&
                        !"公务乘用车".equals(jsonObject.get("carUser")) ){
                    aiPythonTaskVO.setExplanation("车辆用途不对");
                    failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                    throw new BizException("车辆用途不对");
                }
                if("企业".equals(jsonObject.get("custermType")) &&
                        "11".equals(jsonObject.get("custermCode")) &&
                        !"公务乘用车".equals(jsonObject.get("carUser")) ){
                    aiPythonTaskVO.setExplanation("车辆用途不对");
                    failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                    throw new BizException("车辆用途不对");
                }
            }else if("出租客车".equals(textVehicleFront.getUseCharacter()) || "预约出租客运".equals(textVehicleFront.getUseCharacter())){
                if(!"出租乘用车".equals(jsonObject.get("carUser"))){
                    aiPythonTaskVO.setExplanation("车辆类型不对");
                    failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                    throw new BizException("车辆类型不对");
                }
            }else if("租赁".equals(textVehicleFront.getUseCharacter())){
                if(!"租赁乘用车".equals(jsonObject.get("carUser"))){
                    aiPythonTaskVO.setExplanation("车辆类型不对");
                    failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                    throw new BizException("车辆类型不对");
                }
            }else if("警用".equals(textVehicleFront.getUseCharacter())){
                if(!"公务乘用车".equals(jsonObject.get("carUser"))){
                    aiPythonTaskVO.setExplanation("车辆类型不对");
                    failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                    throw new BizException("车辆类型不对");
                }
            }
            /**
             * 判断销售领域
             * "vvin是车架号，vlicense是车牌号，dlicense是注册日期，owner是客户名称，custermCode是客户代码(只传前两位)，
             * carUser是车辆用途(传汉字)， custermType是客户类型(传汉字)";
             */
            // 2.1 #故事#新能源溯源及监控管理流程国补信息运营-溯源信息分析-AI审核规则改造
            if("租赁".equals(textVehicleFront.getUseCharacter())
                    && !"私人领域".equals(jsonObject.get("vsalefield"))){
                aiPythonTaskVO.setExplanation("应该是私人领域");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("应该是私人领域");
            }
            if("企业".equals(jsonObject.get("custermType"))){
                if(("出租客车".equals(textVehicleFront.getUseCharacter())
                        || "警用".equals(textVehicleFront.getUseCharacter())
                        || "预约出租客运".equals(textVehicleFront.getUseCharacter()))
                        && !"公共领域".equals(jsonObject.get("vsalefield"))){
                    aiPythonTaskVO.setExplanation("应该是公共领域");
                    failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                    throw new BizException("应该是公共领域");
                }
                if("非营运".equals(textVehicleFront.getUseCharacter())){
                    if( "11".equals(jsonObject.get("custermCode")) && !"公共领域".equals(jsonObject.get("vsalefield"))){
                        aiPythonTaskVO.setExplanation("应该是公共领域");
                        failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                        throw new BizException("应该是公共领域");
                    }
                    if( !"11".equals(jsonObject.get("custermCode"))
                            && !"12".equals(jsonObject.get("custermCode"))
                            && !"私人领域".equals(jsonObject.get("vsalefield"))){
                        aiPythonTaskVO.setExplanation("应该是私人领域");
                        failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                        throw new BizException("应该是私人领域");
                    }
                    if("12".equals(jsonObject.get("custermCode"))
                            && (!"私人领域".equals(jsonObject.get("vsalefield")) || !"公共领域".equals(jsonObject.get("vsalefield")) )){
                        aiPythonTaskVO.setExplanation("应该是私人领域或公共领域");
                        failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                        throw new BizException("应该是私人领域或公共领域");
                    }
                }


            }
            aiPythonTaskVO.setT(true);
            aiPythonTaskVO.setExplanation("满足所有规则");
            successCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
            return "满足所有规则";
        }else if("GB_04".equals(aiTaskDTO.getTaskType())){
            //总计1项,行驶证副页图片
            ocrDTO.setCardSide("DOUBLE");
            Response<TextVehicleBack> response = ocrService.driverLicenseBack(ocrDTO);
            if("-111".equals(response.getCode())){
                aiPythonTaskVO.setExplanation(response.getMessage());
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException(response.getMessage());
            }
            TextVehicleBack textVehicleFront = response.getData();
            if(StringUtils.isBlank(textVehicleFront.getPlateNo())){
                aiPythonTaskVO.setExplanation("号牌号码识别失败");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("号牌号码识别失败");
            }
            if(!textVehicleFront.getPlateNo().equals(jsonObject.get("driveno"))){
                aiPythonTaskVO.setExplanation("车牌号不一致");
                failCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
                throw new BizException("车牌号不一致");
            }
            aiPythonTaskVO.setT(true);
            aiPythonTaskVO.setExplanation("满足所有规则");
            successCallBack(aiTaskDTO, aiPythonTaskVO, cosQueueDTO);
            return "满足所有规则";

        }else{
            throw new BizException("未知taskType");
        }
    }

    public boolean ignoreSymbolEquals(String owner, String knownOwner) {
        if(owner.equals(knownOwner)){
            return true;
        }
        // 移除中文括号
        owner = owner.replaceAll("【|（|）| |】", "");
        knownOwner = knownOwner.replaceAll("【|（|）| |】", "");

        // 移除英文括号
        owner = owner.replaceAll("[|(|)| |]", "");
        knownOwner = knownOwner.replaceAll("[|(|)| |]", "");
        return owner.equals(knownOwner);
    }

    private void successCallBack(AiTaskDTO aiTaskDTO, AiPythonTaskVO aiPythonTaskVO, CosQueueDTO cosQueueDTO) {
        // 保存AI结果
        savaBnzxOcrResult(aiTaskDTO, aiPythonTaskVO, cosQueueDTO, "success");

        // 拼装回调入参
        CallBackDTO callBackDTO = new CallBackDTO();
        // copy函数赋值失败，所以主动赋值试试
        callBackDTO.setCallbackCustomParam(aiTaskDTO.getCallBackCustomParam());
        callBackDTO.setTaskResult(JSON.toJSONString(aiPythonTaskVO));
        callBackDTO.setTaskType(aiTaskDTO.getTaskType());
        callBackDTO.setSuccess("true");
        callBackDTO.setMessage(aiPythonTaskVO.getExplanation());
        callBackDTO.setSystemId(aiTaskDTO.getSystemId());
        callBackDTO.setBizId(aiTaskDTO.getBizId());
        callBackDTO.setBizType(aiTaskDTO.getBizType());
        if(!"true".equals(aiTaskDTO.getTest())){
            callBack(callBackDTO, cosQueueDTO);
        }
    }
    private void failCallBack(AiTaskDTO aiTaskDTO, AiPythonTaskVO aiPythonTaskVO, CosQueueDTO cosQueueDTO) {
        // 保存AI结果
        savaBnzxOcrResult(aiTaskDTO, aiPythonTaskVO, cosQueueDTO, "false");

        // 拼装回调入参
        CallBackDTO callBackDTO = new CallBackDTO();
        // copy函数赋值失败，所以主动赋值试试
        callBackDTO.setCallbackCustomParam(aiTaskDTO.getCallBackCustomParam());
        callBackDTO.setTaskResult(JSON.toJSONString(aiPythonTaskVO));
        callBackDTO.setTaskType(aiTaskDTO.getTaskType());
        callBackDTO.setSuccess("false");
        callBackDTO.setMessage(aiPythonTaskVO.getExplanation());
        callBackDTO.setSystemId(aiTaskDTO.getSystemId());
        callBackDTO.setBizId(aiTaskDTO.getBizId());
        callBackDTO.setBizType(aiTaskDTO.getBizType());
        if(!"true".equals(aiTaskDTO.getTest())){
            callBack(callBackDTO, cosQueueDTO);
        }
    }

    /**
     * 保存国补资源ORC识别结果
     * @param aiTaskDTO
     * @param aiPythonTaskVO
     * @param cosQueueDTO
     * @param successFlag OCR校验成功标识；success-成功；false-失败；
     */
    private void savaBnzxOcrResult(AiTaskDTO aiTaskDTO, AiPythonTaskVO aiPythonTaskVO, CosQueueDTO cosQueueDTO, String successFlag){
        //构造ai处理结果记录
        AiTaskResultNew aiTaskResult = new AiTaskResultNew();
        aiTaskResult.setBatchId(cosQueueDTO.getBatchId());
        aiTaskResult.setTraceId(cosQueueDTO.getTraceId());
        BeanUtils.copyProperties(aiTaskDTO, aiTaskResult);

        // 拼装处理结果
        aiTaskResult.setTaskStatus(2);
        // 解析AI审核结果
        if(aiPythonTaskVO != null){
            aiTaskResult.setAiResult(aiPythonTaskVO.getIsT() ? 1 : 0);
            aiTaskResult.setAiExplain(aiPythonTaskVO.getExplanation());
        }else {
            aiTaskResult.setAiResult(0);
            aiTaskResult.setAiExplain("AI审核结果为空！");
        }
        aiTaskResult.setTaskType(aiTaskDTO.getTaskType());
        // 处理替换后的规则 国补的pormpt没走数据库，分开的，所以这个字段可以不记录
        //  aiTaskResult.setPrompt(finalPrompt);
        aiTaskResult.setBizId(aiTaskDTO.getBizId());
        aiTaskResult.setBizType(aiTaskResult.getBizType());
        aiTaskResult.setSystemId(cosQueueDTO.getSystemId());
        savaOrUpdateAiResult(aiTaskResult);
    }

    private void callBack(CallBackDTO callBackDTO, CosQueueDTO cosQueueDTO) {
        log.info("-----------dynamicCallBack回调入参实体打印---------" + callBackDTO);
        log.info("-----dynamicCallBack回调json格式入参----" + JSON.toJSONString(callBackDTO));

        LogInfoPythonCallbackWithBLOBs logInfoPythonCallback = logInfoPythonCallbackMapper.selectByTraceId(cosQueueDTO.getTraceId());
        logInfoPythonCallback = assimbleCallBackParam(logInfoPythonCallback, JSON.toJSONString(callBackDTO));
        logInfoPythonCallbackMapper.updateByTranceId(logInfoPythonCallback);
        log.info(cosQueueDTO.getTraceId() + "-------插入回调能力中心入参日志成功----------");

        Response result = null;
        try {
            result = bnzxOpenApiFeignClient.callBack(callBackDTO,"true", "15", "pc");
            log.info("回调DMS BNZX返回值{}", result);

            //更新ai处理结果状态
            if(result != null){
                int updateFlag = aiTaskResultMapper.updateResultStatusByTraceId(cosQueueDTO.getTraceId());
                log.info("-----完成taskResult状态更新----");
            }
        } catch (Exception e) {
            throw new BizException("回调异常",e);
        }

        logInfoPythonCallback = assimbleCallBackResultParam(logInfoPythonCallback, JSON.toJSONString(result));
        logInfoPythonCallbackMapper.updateByPrimaryKey(logInfoPythonCallback);
        log.info(cosQueueDTO.getTraceId() + "-----插入回调能力中心的回调结果日志成功-----" + JSON.toJSON(result));

    }

    /**
     * 调用大模型处理结果并记录
     * @param cosQueueDTO
     * @return
     */
    @Override
    public Response getAiResult(CosQueueDTO cosQueueDTO) {
        log.info(cosQueueDTO.getTraceId() + "---------------------------开始处理消息-----------------------------" + cosQueueDTO);
        if("BNZX".equals(cosQueueDTO.getSystemId())){
            AiTaskDTO aiTaskDTO = new AiTaskDTO();
            BeanUtils.copyProperties(cosQueueDTO,aiTaskDTO);
            try {
                String string = getString(aiTaskDTO, cosQueueDTO);
                return Response.success(string);
            } catch (BizException bizException){
                log.warn("----消费消息业务异常----",bizException);
                return Response.fail("消费消息业务异常");
            } catch (Exception e) {
                log.error("----消费消息异常----",e);
                return Response.fail(e.getMessage());
            }
        }
        AiTaskVO aiTaskVO = new AiTaskVO();
        BeanUtils.copyProperties(cosQueueDTO,aiTaskVO);
        // 从数据库查询prompt拼接，查询处理记录取时间最新的一条记录
        log.info("----查询taskRule入参-----systemtId={}, taskType = {} ", cosQueueDTO.getSystemId(), cosQueueDTO.getTaskType());
        TaskRule taskRule = taskRuleMapper.getLastPromptBySystemId(cosQueueDTO.getSystemId(), cosQueueDTO.getTaskType());
        log.info("----查询taskRule结果-----taskRule  = " + taskRule);
        // 获取各系统回调地址
        String callBackUrl = taskRuleMapper.getCallBackUrl(cosQueueDTO.getSystemId());
        // 任务运营中心不需要设置规则；
        // TODO 这段代码逻辑要优化，现在只是针对问题解决问题
        if(taskRule != null){
            try {
                // 构造ai处理请求入参
                String promptTemplate = taskRule.getPrompt();
                String finalPrompt = "";
                if(StringUtils.isBlank(promptTemplate)){
                    setRuleRobotMsg(cosQueueDTO);
                    String taskResult = errorAiTaskResult("请设置业务规则！","AI-113");
                    dynamicCallBack(callBackUrl, cosQueueDTO,taskResult,null,false, "false", "请设置业务规则；");
                    return Response.fail(ResEnum.F_113);
                }
                List<String> systemIdList = new ArrayList<>(Arrays.asList("RWYYZX", "HIS"));
                if(systemIdList.contains(cosQueueDTO.getSystemId())){
                    finalPrompt = promptTemplate;
                } else {
                    finalPrompt = replatePrompt(cosQueueDTO.getGivenInfoJson(), promptTemplate);
                }
                AiTaskResultDTO param = new AiTaskResultDTO();
                param.setVersion(taskRule.getVersion().toString());
                param.setBizId(cosQueueDTO.getBizId());
                param.setTaskType(cosQueueDTO.getTaskType());
                param.setSystemId(cosQueueDTO.getSystemId());
                param.setPrompt(finalPrompt);
                log.info("---从表中获取回调URL为-- " +  callBackUrl);

                PythonAiTaskDTO pythonAiInParam = new PythonAiTaskDTO();
                log.info("---finalPrompt---" + finalPrompt);
                BeanUtils.copyProperties(cosQueueDTO, pythonAiInParam);
                pythonAiInParam.setPrompt(finalPrompt);
                cosQueueDTO.setVersion(taskRule.getVersion().toString());

                //构造ai处理结果记录
                AiTaskResultNew aiTaskResult = new AiTaskResultNew();
                BeanUtils.copyProperties(param, aiTaskResult);
                aiTaskResult.setBatchId(cosQueueDTO.getBatchId());
                aiTaskResult.setTraceId(cosQueueDTO.getTraceId());

                //  给python服务发送任务，获取ai处理结果
                BaseOutDTO dto = new BaseOutDTO();
                dto.setBizId(cosQueueDTO.getBizId());
                if(StringUtils.isBlank(cosQueueDTO.getCallBackCustomParam()) || cosQueueDTO.getCallBackCustomParam() == null){
                    pythonAiInParam.setCallBackCustomParam("");
                }
                pythonAiInParam.setContents(cosQueueDTO.getToPythonFileInfos());
                log.info(cosQueueDTO.getTraceId() + "---调用python服务请求入参为---" + JSON.toJSONString(pythonAiInParam));
                //  保存python的入参到日志表
                assimblePythonInParamAndUpdate(null, cosQueueDTO, JSONObject.toJSONString(pythonAiInParam), "2", "in");
                dto = taskFeignClientInvokes.aiTask(pythonAiInParam);
                // 如果大模型返回结果为空，直接回调
                if(dto == null){
                    // 回调返回结果
                    dynamicCallBack(callBackUrl, cosQueueDTO, null, aiTaskResult, true, null, null);
                    throw new BizException("大模型返回数据为空，请确认");
                }

                log.info(cosQueueDTO.getTraceId() + "---调用python服务请求返回结果为---" + JSON.toJSONString(dto));
                //  保存python服务的返回结果信息到日志表
                assimblePythonInParamAndUpdate(null, cosQueueDTO, JSONObject.toJSONString(dto), "2", "result");

                // 校验大模型接口返回数据格式
                boolean isJsonStr = isJsonStr(dto.getData());
                // 如果data不是json格式,给机器人推送消息,并把数据放到错误的消息队列中，同时AI审核结果直接记录为false，解释为不是json格式
                if(!isJsonStr){
                    dingRobotService.pushRobotMsg(robotToken, "", robotSecret,
                            "【"+ robotKey + "】" + "data数据不是json格式，请处理！data原始值为：[" + dto.getData() + "],"
                                    + "[BizId = " + pythonAiInParam.getBizId() + "]" + "[systemId = " + pythonAiInParam.getSystemId() + "], [tranceId =" + pythonAiInParam.getTraceId() + "]",
                            true);
//                        rabbitService.convertAndSendError(JSON.toJSONString(cosQueueDTO));
                    aiTaskResult.setAiResult(CommonConstants.ZERO);
                    aiTaskResult.setAiExplain("AI返回结果不是json格式，不能解析！");
                }
                if (CommonConstants.TRUE.equals(dto.getSuccess()) || CommonConstants.FALSE.equals(dto.getSuccess())) {

                    // 处理文件信息
                    if (CollectionUtils.isNotEmpty(cosQueueDTO.getToPythonFileInfos())) {
                        dealerFileInfo(cosQueueDTO.getToPythonFileInfos(), cosQueueDTO.getTraceId());
                    }
                    dealAiResult(dto, pythonAiInParam, cosQueueDTO, finalPrompt, aiTaskResult, aiTaskVO);
                }
                // 将ai处理结果返回给各个系统
                dynamicCallBack(callBackUrl, cosQueueDTO, dto.getData(), aiTaskResult,true, dto.getSuccess(), dto.getMessage());

            }catch (Exception ex){
                log.error(cosQueueDTO.getTraceId() + "ai处理结果异常：" + ex.getMessage(),  ex);
                String taskResult = errorAiTaskResult(cosQueueDTO.getTraceId() + "AI公共服务接口异常","AI-116");
                setRuleRobotAiError(cosQueueDTO, ex.getMessage());
                dynamicCallBack(callBackUrl, cosQueueDTO, taskResult, null,false, "false", "AI公共服务接口异常！");
                return Response.fail(ResEnum.F_116);
            }
        }else {
            setRuleRobotMsg(cosQueueDTO);
            String taskResult = errorAiTaskResult("请设置业务规则！","AI-113");
            dynamicCallBack(callBackUrl, cosQueueDTO, taskResult, null,false, "false", "请设置业务规则！");
            return Response.fail(ResEnum.F_113);
        }
        log.info(cosQueueDTO.getTraceId() + "-------------------------结束处理消息----------------------------------------");
        return Response.success();
    }

    public void dealerFileInfo(List<BeCheckFileDTO> contents, String traceId) {
        List<Long> ids = fileInfoNewDao.getFileIdByTraceId(traceId);
        if (CollectionUtils.isNotEmpty(ids)) {
            fileInfoNewDao.deleteByIds(ids);
        }
        for (int i = 0; i < contents.size(); i++) {
            FileInfoNew fileInfoNew = new FileInfoNew();
            fileInfoNew.setFileId(contents.get(i).getCosKey());
            fileInfoNew.setFileType(Integer.valueOf(contents.get(i).getFileContentType()));
            fileInfoNew.setFileUrl(contents.get(i).getFileUrl());
            fileInfoNew.setFileIndex(i);
            fileInfoNew.setTraceId(traceId);
            fileInfoNewDao.insert(fileInfoNew);
        }
    }

    /**
     * 调用AIS服务后，保存AI处理结果
     * @param dto
     * @param pythonAiInParam
     * @param cosQueueDTO
     * @param finalPrompt
     * @param aiTaskResult
     * @param aiTaskVO
     */
    private void dealAiResult(BaseOutDTO dto,PythonAiTaskDTO pythonAiInParam, CosQueueDTO cosQueueDTO, String finalPrompt, AiTaskResultNew aiTaskResult, AiTaskVO aiTaskVO){
        // 拼装处理结果
        aiTaskResult.setTaskStatus(2);
        // 解析AI审核结果
        if(StringUtils.isNotEmpty(dto.getData())){
            String dataStr = dto.getData();
            AiResultVO obj = JSON.parseObject(dataStr, AiResultVO.class);
            aiTaskResult.setAiResult(CommonConstants.TRUE.equals(obj.getIsT().toString()) ? 1 : 0);
            aiTaskResult.setAiExplain(obj.getExplanation());
        }else {
            aiTaskResult.setAiResult(0);
            aiTaskResult.setAiExplain("AI审核结果为空！");
        }
        aiTaskResult.setTaskType(pythonAiInParam.getTaskType());
        aiTaskResult.setBizId(pythonAiInParam.getBizId());
        aiTaskResult.setBizType(pythonAiInParam.getBizType());
        aiTaskResult.setSystemId(cosQueueDTO.getSystemId());
        aiTaskVO.setTaskResult(dto.getData() == null? "": dto.getData().toString());

        // 保存AI结果
        savaOrUpdateAiResult(aiTaskResult);
        log.info(cosQueueDTO.getTraceId() + "------保存taskResult完成----aiTaskResult = " + aiTaskResult);
    }

    private void savaOrUpdateAiResult(AiTaskResultNew aiTaskResultNew){
        // 根据batchId traceId查询是否已经有AI结果；
        AiTaskResultDTO searchParam = new AiTaskResultDTO();
        searchParam.setBatchId(aiTaskResultNew.getBatchId());
        searchParam.setTraceId(aiTaskResultNew.getTraceId());
        AiTaskResultNew aiTaskResultNew1 = aiTaskResultNewDao.getAiTaskResultNewOne(searchParam);
        if(aiTaskResultNew1 == null){
            aiTaskResultNewDao.insert(aiTaskResultNew);
        }else {
            aiTaskResultNew.setId(aiTaskResultNew1.getId());
            aiTaskResultNewDao.update(aiTaskResultNew);
        }
    }

    /**
     * 给机器人发消息-设置规则
     */
    private void setRuleRobotMsg(CosQueueDTO cosQueueDTO){
        dingRobotService.pushRobotMsg(robotToken, "", robotSecret,
                "【"+ robotKey + "】" + "请设置业务规则！taskType：[" + cosQueueDTO.getTaskType() + "],"
                        + "[bizId = " + cosQueueDTO.getBizId() + "]"
                        + "[systemId = " + cosQueueDTO.getSystemId() + "]",
                true);
    }

    /**
     * 给机器人发消息-AI调用python报错时
     */
    private void setRuleRobotAiError(CosQueueDTO cosQueueDTO, String errorMsg){
        dingRobotService.pushRobotMsg(robotToken, "", robotSecret,
                "【"+ robotKey + "】" + "AI公共服务报错！"
                + "错误信息为:" + errorMsg
                + "json入参为：" + JSON.toJSONString(cosQueueDTO),
                false);
    }

    /**
     * AI公共服务的异常taskResult
     * @param content
     * @param code
     * @return
     */
    private String errorAiTaskResult(String content, String code){
        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setExplanation(content);
        taskResultDTO.setCode(code);
        taskResultDTO.setIsT("false");
        return JSON.toJSONString(taskResultDTO);
    }

    /**
     * 替换占位符
     * @param givenInfoJson
     * @param promptTemplate
     * @return
     */
    private String replatePrompt(String givenInfoJson, String promptTemplate){
        Map<String, String> placeholders = JSON.parseObject(givenInfoJson, new TypeReference<Map<String, String>>() {});
        String finalPrompt = replacePlaceholders(promptTemplate, placeholders);
        return finalPrompt;
    }

    public static String replacePlaceholders(String template, Map<String, String> placeholders) {
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            template = template.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return template;
    }

    private void dynamicCallBack(String callBackUrl, CosQueueDTO cosQueueDTO, String taskResult, AiTaskResultNew aiTaskResult, boolean updateFlag, String success, String message) {
        log.info(cosQueueDTO.getTraceId() + "-----------------------------开始执行回调方法-------------------------");
        log.info("-----callBackUrl = " + callBackUrl + ", taskResult =" + taskResult);
        log.info("-----cosQueueDTO = " + JSON.toJSONString(cosQueueDTO));
        if(StringUtils.isEmpty(callBackUrl)){
            dingRobotService.pushRobotMsg(robotToken, "", robotSecret,
                    "【"+ robotKey + "】" + "回调地址不能为空！taskType：[" + cosQueueDTO.getTaskType() + "],"
                            + "[bizId = " + cosQueueDTO.getBizId() + "]"
                            + "[systemId = " + cosQueueDTO.getSystemId() + "]",
                    true);
        }
        try {
            // 拼装回调入参
            CallBackDTO callBackDTO = new CallBackDTO();
            BeanUtils.copyProperties(cosQueueDTO, callBackDTO);
            // copy函数赋值失败，所以主动赋值试试
            callBackDTO.setCallbackCustomParam(cosQueueDTO.getCallBackCustomParam());
            callBackDTO.setTaskResult(taskResult);
            callBackDTO.setTaskType(cosQueueDTO.getTaskType());
            callBackDTO.setSuccess(success);
            callBackDTO.setMessage(message);
            log.info(cosQueueDTO.getTraceId() + "-----dynamicCallBack回调json格式入参----" + JSON.toJSONString(callBackDTO));
            LogInfoPythonCallbackWithBLOBs logInfoPythonCallback = logInfoPythonCallbackMapper.selectByTraceId(cosQueueDTO.getTraceId());
            logInfoPythonCallback = assimbleCallBackParam(logInfoPythonCallback, JSON.toJSONString(callBackDTO));
            logInfoPythonCallbackMapper.updateByTranceId(logInfoPythonCallback);
            log.info(cosQueueDTO.getTraceId() + "-------插入回调能力中心入参日志成功----------");
            Response result = null;
            if("BNZX".equals(cosQueueDTO.getSystemId())){
                 result = bnzxOpenApiFeignClient.callBack(callBackDTO,"true", "15", "pc");
            }else {
                URI uri = new URI(callBackUrl);
                result = iworkOpenApiFeignClient.dynamicCallBack(uri, callBackDTO);
            }
            log.info(cosQueueDTO.getTraceId() + "-----dynamicCallBack回调返回值为-----" + JSON.toJSON(result));
            logInfoPythonCallback = assimbleCallBackResultParam(logInfoPythonCallback, JSON.toJSONString(result));
            logInfoPythonCallbackMapper.updateByPrimaryKey(logInfoPythonCallback);
            log.info(cosQueueDTO.getTraceId() + "-----插入回调能力中心的回调结果日志成功-----" + JSON.toJSON(result));
            //更新ai处理结果状态
            if(updateFlag && aiTaskResult != null){
                aiTaskResult.setTaskStatus(3);
                aiTaskResultNewDao.update(aiTaskResult);
                log.info("-----完成taskResult状态更新----");
            }
        }catch (Exception exception){
            log.warn(cosQueueDTO.getTraceId() + "--执行回调函数出错--" + exception.getCause()+"--异常信息={},异常={}", exception.getMessage(),exception);
        }
        log.info(cosQueueDTO.getTraceId() + "--------------------------------执行回调方法结束---------------------------------"  + cosQueueDTO.getTraceId());
    }

    /**
     * 推送消息到队列
     * @param aiTaskDTO 接口入参
     * @param toPythonFileInfos 给python的文件入参，url为AI对外公共服务COS桶中的数据
     */
    private void sendAiTaskMessage(AiTaskDTO aiTaskDTO, List<BeCheckFileDTO> toPythonFileInfos, List<BeCheckFileDTO> contents){
        log.info(aiTaskDTO.getTraceId() + "---------------开始推送消息队列-----------" + aiTaskDTO.getTraceId());
        // 构造消息业务数据
        CosQueueDTO cosQueueDTO = new CosQueueDTO();
        BeanUtils.copyProperties(aiTaskDTO, cosQueueDTO);
        cosQueueDTO.setToPythonFileInfos(toPythonFileInfos);
        cosQueueDTO.setContents(contents);
        cosQueueDTO.setBatchId(aiTaskDTO.getBatchId());
        String jsonString = JSON.toJSONString(cosQueueDTO);
        // 封装消息结构体
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setJsonMapString(jsonString);
        messageDTO.setBusinessType("1");
        // 推送消息
        String messageJson = JSON.toJSONString(messageDTO);
        if ("BNZX".equals(aiTaskDTO.getSystemId())){
            rabbitService.convertAndSendBnzx(messageJson);
        }else {
            rabbitService.convertAndSend(messageJson);
        }
        log.info(aiTaskDTO.getTraceId() + "---------------推送消息队列结束-----------" + aiTaskDTO.getTraceId());
    }

    /**
     * 没有文件信息-旗效看板使用
     * @param aiTaskDTO
     */
    private void sendAiTaskMessageNoFileInfo(AiTaskDTO aiTaskDTO){
        log.info(aiTaskDTO.getTraceId() + "---------------开始推送消息队列无文件信息-----------" + aiTaskDTO.getTraceId());
        // 构造消息业务数据
        CosQueueDTO cosQueueDTO = new CosQueueDTO();
        BeanUtils.copyProperties(aiTaskDTO, cosQueueDTO);
        String jsonString = JSON.toJSONString(cosQueueDTO);
        // 封装消息结构体
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setJsonMapString(jsonString);
        messageDTO.setBusinessType("1");
        // 推送消息
        String messageJson = JSON.toJSONString(messageDTO);
        if("BNZX".equals(aiTaskDTO.getSystemId())){
            rabbitService.convertAndSendBnzx(messageJson);
        }else{
            rabbitService.convertAndSend(messageJson);
        }
        log.info(aiTaskDTO.getTraceId() + "---------------推送消息队列结束无文件信息-----------" + aiTaskDTO.getTraceId());
    }

    /**
     * 文件上传到cos桶，并返回文件的fileId
     * @param beCheckFile
     * @param bizId
     * @return
     */
    private CosInfoDTO pushToCos(BeCheckFileDTO beCheckFile, String bizId){
        log.info("[AiTaskService][pushToCos][entrance] bizId: {}, beCheckFile: {}", bizId, JSON.toJSONString(beCheckFile));
        log.info("[AiTaskService][pushToCos] ----------------开始处理文件信息--------------");
        String cosKey = getCosKey();
            String fileName = getUrlFileName(beCheckFile.getFileUrl());
            String fileUrl = beCheckFile.getFileUrl();
            // 计算文件的md5值
            com.faw.work.ais.model.File fileInfo = getFileByUrl(fileUrl == null ? cosKey : fileUrl);
            String fileMd5 = fileInfo.getMd5();
            fileInfo.setRawName(fileName);
            fileInfo.setMd5(fileMd5);
            fileInfo.setFileDesc(beCheckFile.getFileDesc());
            fileInfo.setFileIndex(Integer.valueOf(beCheckFile.getFileIndex()));
            fileInfo.setVersion(beCheckFile.getVersion());

            CosInfoDTO cosInfo = new CosInfoDTO();
            cosInfo.setKey(cosKey);
            cosInfo.setLocalFile(fileUrl);

        try {
            fileInfo.setId(cosKey);
            //保存到cos
            FileInfoVO fileResult = fileUtil.uploadToCos(cosInfo);
            cosInfo.setFilePath(fileResult.getUrl());
            fileMapper.insert(fileInfo);

        } catch (Exception e) {
            log.error("[AiTaskService][pushToCos] 推送文件md5值到cos服务器出错 message: ", e);
            throw new BizException("------推送文件md5值到cos服务器出错----");
        }
        log.info("[AiTaskService][pushToCos] ----------------处理文件信息结束--------------");
        return cosInfo;
    }

    @Override
    public void dynamicUrlTest(String urlType){
        log.info("--------test dynamicUrlTest ----------");
        try {
            String urlPath = "";
            if("1".equals(urlType)){
                urlPath = "http://localhost:8080/test/urlA";
            }else {
                urlPath = "http://localhost:8080/test/urlB";
            }
            URI uri = new URI(urlPath);
            iworkOpenApiFeignClient.dynamicCallBackTest(uri, "a", "true");
            log.info("request url = " + uri);
        }catch (Exception ex){
            throw new RuntimeException(ex.getMessage());
        }
        log.info("--------test dynamicUrlTest End----------");
    }

    @Override
    public void dealErrorQueueMsg(CosQueueDTO cosQueueDTO) {
        String msg =
                "【"+ robotKey + "】" + "json格式错误！[taskType：" + cosQueueDTO.getTaskType() + "],"
                        + "[bizId = " + cosQueueDTO.getBizId() + "],"
                        + "[systemId = " + cosQueueDTO.getSystemId() + "],"
                        + "[traceId = "+ cosQueueDTO.getTraceId() +"]";
        dingRobotService.pushRobotMsg(robotToken, ruleError_dingdingUserId, robotSecret, msg, true);
    }

    /**
     * 保存AIS服务返回的AI结果
     * @param aiTaskFormPythonDTO
     * @return
     */
    @Override
    public Response saveAiTaskResultFromPython(AiTaskFormPythonDTO aiTaskFormPythonDTO) {
        AiTaskResult aiTaskResult = new AiTaskResult();
        aiTaskResult.setTaskType("PRD_01");
        aiTaskResult.setVersion("1");
        aiTaskResult.setSystemId("AITASK");
        aiTaskResult.setTaskStatus(1);

        aiTaskResult.setFileId(aiTaskFormPythonDTO.getFileId());
        aiTaskResult.setTaskResult(aiTaskFormPythonDTO.getTaskResult());
        try {
            taskResultService.save(aiTaskResult);
        }catch (Exception exception){
            log.error("------保存ai处理结果失败---------" + exception.getMessage());
            return Response.fail(ResEnum.F_220);
        }
        return Response.success();
    }

    /**
     * 根据外系统的url读取文件的md5以及其他文件信息
     * @param urlPath
     * @return
     */
    public com.faw.work.ais.model.File getFileByUrl(String urlPath) {
        com.faw.work.ais.model.File file = new com.faw.work.ais.model.File();
        InputStream inputStream = null;
        try {

            HttpEntity result = HttpUtil.sendGet(urlPath,null ,null );
            inputStream =  result.getContent();
            long fileSize = result.getContentLength();
            log.info("File size: " + fileSize + " bytes");
            // TODO 本应该判断文件是否真的有内容，但是因为国补的contentEncode取不到值，所以没法判断
//            if(fileSize < 0 && !isCompressed(result)){
//                log.error("--请求图片地址异常-" + urlPath);
//            }
            if(fileSize < 0){
                log.warn("--请求图片地址可能异常，因为result.getEncoding为空，所以不能判断是否为真异常，请自行根据地址具体查看！---" + urlPath);
            }
            // 创建一个临时文件来存储下载的内容
            Path tempFile = Files.createTempFile("downloaded-file", null);
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);

            // 计算MD5哈希值
            MessageDigest md = MessageDigest.getInstance(CommonConstants.ENCRYPTION_ALGORITHM);
            byte[] fileContent = Files.readAllBytes(tempFile);
            md.update(fileContent);
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            String md5 = sb.toString();
            System.out.println("File MD5: " + md5);
            file.setMd5(sb.toString());
            file.setLength(String.valueOf(fileSize));
            // 清理临时文件
            Files.delete(tempFile);
        } catch (Exception e) {
            log.error("----读取文件异常----错误信息 = " + e.getMessage(), e);
            throw new RuntimeException(e);
        }finally {
            try {
                // 关闭文件流
                if(inputStream != null){
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("----读取文件异常----错误信息 = " + e.getMessage(), e);
            }
        }
        return file;
    }

    private String getUrlFileName(String url){
        String fileName = "";
        String regex = "([^/]+)$"; // 匹配最后一个/后面的所有字符，直到字符串结束

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            fileName = matcher.group(1); // 获取匹配到的部分（即最后一个/后面的字符串）
        }
        return fileName;
    }

    /**
     * 生成18位随机数
     *
     * @return
     */
    private String getCosKey(){
        String timestampStr = String.format("%015d", System.currentTimeMillis()); // 15位时间戳
        Random random = new Random();
        int hundred = random.nextInt(9) + 1; // 1-9之间的百位
        int ten = random.nextInt(10); // 0-9之间的十位
        int one = random.nextInt(10); // 0-9之间的个位
        int randomNumber = hundred * 100 + ten * 10 + one; // 组合成三位数
        String  result = String.valueOf(randomNumber) + timestampStr;
        return result;
    }
}
