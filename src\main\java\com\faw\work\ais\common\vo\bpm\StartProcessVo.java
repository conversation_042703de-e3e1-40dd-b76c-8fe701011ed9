package com.faw.work.ais.common.vo.bpm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "启动bpm流程请求返回数据实体")
public class StartProcessVo {

    @Schema(description = "流程id")
    private String processInstanceId;
}
