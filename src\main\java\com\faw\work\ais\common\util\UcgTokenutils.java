package com.faw.work.ais.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 获取Ucg网关access_token相关方法
 * 
 */
@Slf4j
@Service
public class UcgTokenutils {
	// 四套环境地址
	private static final String DEV_UCG_GET_TOKEN_DOMAIN = "http://dev.api.qm.cn";
	private static final String SIT_UCG_GET_TOKEN_DOMAIN = "https://test-api.faw.cn:30443";
	private static final String UAT_UCG_GET_TOKEN_DOMAIN = "https://uat-api.faw.cn:30443";
	private static final String PROD_UCG_GET_TOKEN_DOMAIN = "https://prod-api.faw.cn";

	private static final String UCG_GET_TOKEN_URL = "/ucg/oauth/getToken";
	private static final String TIMESTAMP = "&timestamp=";
	private static final String SIGNATURE = "&signature=";
	private static final String APPKEY = "?appKey=";


	/**
	 * 
	 * 直接获取token
	 * 
	 * dev,sit,uat,prod 失败会返回null 或者抛出异常
	 * 
	 * @param appKey
	 * @param appSecret
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws InvalidKeyException
	 */
	public static JSONObject getUcgAccessToken(String profile, String appKey, String appSecret) throws Exception {
		Map<String, String> params = new HashMap<>();
		params.put("appKey", appKey);
		String timeStamp = String.valueOf(System.currentTimeMillis());
		params.put("timestamp", timeStamp);
		String signature = sign(params, appSecret);
		return doHttpGetToken(profile, appKey, timeStamp, signature);
	}

	/**
	 * 按参数名排序后依次拼接参数名称与数值，之后对该字符串使用 HmacSHA256 加签，加签结果进行 base 64 返回
	 * 
	 * @param params      请求参数 map
	 * @param suiteSecret 套件密钥，用作 mac key
	 * @return 签名
	 * @throws NoSuchAlgorithmException
	 * @throws UnsupportedEncodingException
	 * @throws InvalidKeyException
	 */
	public static String sign(Map<String, String> params, String suiteSecret)
			throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
		// use tree map to sort params by name
		Map<String, String> treeMap;
		if (params instanceof TreeMap) {
			treeMap = params;
		} else {
			treeMap = new TreeMap<>(params);
		}

		StringBuilder stringBuilder = new StringBuilder();
		for (Map.Entry<String, String> entry : treeMap.entrySet()) {
			stringBuilder.append(entry.getKey()).append(entry.getValue());
		}

		Mac mac = Mac.getInstance("HmacSHA256");
		mac.init(new SecretKeySpec(suiteSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
		byte[] signData = mac.doFinal(stringBuilder.toString().getBytes(StandardCharsets.UTF_8));
		String base64String = Base64.getEncoder().encodeToString(signData);
		return URLEncoder.encode(base64String, "UTF-8");
	}

	private static JSONObject doHttpGetToken(String profile, String appKey, String timestamp, String signature)
			{
				String url = "";
		JSONObject tokenResult = null;
		CloseableHttpClient httpclient = HttpClients.createDefault();
		try {
			switch (profile) {
				case "dev":
					url = DEV_UCG_GET_TOKEN_DOMAIN + UCG_GET_TOKEN_URL + APPKEY + appKey
							+ TIMESTAMP + timestamp + SIGNATURE + signature;
					break;
				case "sit":
					url = SIT_UCG_GET_TOKEN_DOMAIN + UCG_GET_TOKEN_URL + APPKEY + appKey
							+ TIMESTAMP + timestamp + SIGNATURE + signature;
					break;
				case "uat":
					url = UAT_UCG_GET_TOKEN_DOMAIN + UCG_GET_TOKEN_URL + APPKEY + appKey
							+ TIMESTAMP + timestamp + SIGNATURE + signature;
					break;
				case "prod":
					url = PROD_UCG_GET_TOKEN_DOMAIN + UCG_GET_TOKEN_URL + APPKEY + appKey
							+ TIMESTAMP + timestamp + SIGNATURE + signature;
					break;
				default:
					url = DEV_UCG_GET_TOKEN_DOMAIN + UCG_GET_TOKEN_URL + APPKEY + appKey
							+ TIMESTAMP + timestamp + SIGNATURE + signature;
					break;
			}
			HttpGet httpGet = new HttpGet(url);
			CloseableHttpResponse response = httpclient.execute(httpGet);
			String resData = EntityUtils.toString(response.getEntity(), "UTF-8");
			final JSONObject resJson = JSON.parseObject(resData);
			httpclient.close();
			tokenResult = resJson;
		}catch (Exception e){
			log.error(e.getMessage());
		}
		if (tokenResult != null && "200".equals(tokenResult.getString("code"))) {
			return tokenResult;
		}
		return null;
	}



	public static enum ENV_ENUM {
		DEV("dev", "开发环境"), SIT("sit", "测试环境"), UAT("uat", "联调环境"), PROD("prod", "生产环境");

		private String code;
		private String name;

		private ENV_ENUM(String code, String name) {
			this.code = code;
			this.name = name;
		}

		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}
	}
}
