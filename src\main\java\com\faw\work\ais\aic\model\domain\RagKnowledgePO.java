package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * RAG知识库配置表
 *
 * <AUTHOR>
 */
@Data
@TableName("rag_knowledge")
@Schema(description = "RAG知识库配置实体")
public class RagKnowledgePO {

    @TableId(value = "id")
    @Schema(description = "知识库ID")
    private Long id;

    @TableField("name")
    @Schema(description = "知识库名称")
    private String name;

    @TableField("description")
    @Schema(description = "知识库描述")
    private String description;

    @TableField("data_type")
    @Schema(description = "数据类型（00-非结构文档pdf doc ，01-结构化文档 excel）")
    private String dataType;

    @TableField("requery_open")
    @Schema(description = "是否改写上下文（0-不使用 1-使用）")
    private String requeryOpen;

    @TableField("embedding_model")
    @Schema(description = "Embedding模型名称")
    private String embeddingModel;

    @TableField("rerank_open")
    @Schema(description = "是否使用rerank（0-不使用 1-使用）")
    private String rerankOpen;

    @TableField("rerank_model")
    @Schema(description = "重排序模型")
    private String rerankModel;

    @TableField("similarity_threshold")
    @Schema(description = "相似度阈值(0-1)，用于召回时过滤")
    private Float similarityThreshold;

    @TableField("topK")
    @Schema(description = "召回片段数量")
    private Integer topK;


    @TableField("collection_name")
    @Schema(description = "集合名称（按照场景分类，相当于分表）")
    private String collectionName;

    @TableField("created_by")
    @Schema(description = "创建人")
    private String createdBy;

    @TableField("created_at")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @TableField("updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @TableField("updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 