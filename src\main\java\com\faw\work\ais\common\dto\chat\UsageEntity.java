package com.faw.work.ais.common.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 使用情况 实体
 *
 * <AUTHOR>
 * @since 2025-04-22 16:38
 */
@Data
@Schema(description = "使用情况 实体")
public class UsageEntity {

    @Schema(description = "提示词token")
    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    @Schema(description = "完成token")
    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    @Schema(description = "总token")
    @JsonProperty("total_tokens")
    private Integer totalTokens;

}
