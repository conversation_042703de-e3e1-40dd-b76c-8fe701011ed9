package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "任务规则表")
public class ViewAiCoverNum {
    /**
     * id
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * AI覆盖角色数
     */
    @Schema(description = "覆盖角色数")
    private Integer aiCoverRole;

    /**
     * AI覆盖规则数
     */
    @Schema(description = "覆盖规则数")
    private Integer aiCoverRule;

    /**
     * AI覆盖业务单元数
     */
    @Schema(description = "覆盖业务单元数")
    private Integer aiCoverBizUnit;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private String dealName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 上线满足
     */
    @Schema(description = "上线满足")
    private Integer isReadyFor;

    /**
     * 是否显示真实准确率
     */
    @Schema(description = "是否显示真实准确率")
    private Integer isTrueCover;
}