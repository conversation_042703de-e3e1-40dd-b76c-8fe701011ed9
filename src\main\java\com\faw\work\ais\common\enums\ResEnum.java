package com.faw.work.ais.common.enums;

public enum ResEnum implements IEnum {

    SUCCESS_CODE("200", "请求成功"),
    FAIL_CODE("-111", "请求失败"),
    E_400("400", "请求处理异常，请稍后再试"),
    FP_112("-112", "调用大模型接口异常！"),
    F_113("-113", "请设置业务规则！"),
    F_114("-114", "文件上传失败！"),
    F_115("-115", "您未上传任务文件，请先上传文件！"),
    F_116("-116", "AI公共服务接口异常！"),
    F_117("-117", "您还没有权限调用这个接口，请联系管理员！"),
    F_118("-118", "不能传空数据！"),
    F_119("-119", "AI返回结果不是json格式，请联系管理员处理！"),
    F_220("-200", "保存AI处理结果失败！"),
    F_221("-221", "不能推送空的人工审核结果数据！"),
    F_222("-222", "单次推送数据不能大于1000条！"),
    F_223("-223", "日期间隔不能大于30天！");



    ResEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    @Override
    public String code() {
        return this.errorCode;
    }

    @Override
    public String msg() {
        return this.errorMsg;
    }

    private final String errorCode;

    private final String errorMsg;

}
