<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.chat.AppChatHistoryDetailMapper">

    <resultMap id="AppChatHistoryDetail" type="com.faw.work.ais.model.chat.AppChatHistoryDetail">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="chat_id" property="chatId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="chat_text" property="chatText" jdbcType="VARCHAR"/>
        <result column="like_flag" property="likeFlag" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`, `chat_id`, `type`, `chat_text`, `like_flag`, `create_time`, `update_time`
    </sql>

    <insert id="insertList" >
        INSERT INTO app_chat_history_detail (`chat_id`, `type`, `chat_text`, `like_flag`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.chatId,jdbcType=BIGINT}, #{item.type,jdbcType=INTEGER}, #{item.chatText,jdbcType=VARCHAR},
             #{item.likeFlag,jdbcType=INTEGER})
        </foreach>
    </insert>

    <delete id="deleteByChatId" parameterType="java.lang.Long">
        DELETE FROM app_chat_history_detail
        WHERE chat_id = #{chatId,jdbcType=BIGINT}
    </delete>

    <select id="selectList" parameterType="com.faw.work.ais.model.chat.AppChatHistoryDetail" resultMap="AppChatHistoryDetail">
        SELECT <include refid="Base_Column_List"/>
        FROM app_chat_history_detail
        <where>
            <if test="chatId != null ">
                AND chat_id = #{chatId,jdbcType=BIGINT}
            </if>
            <if test="type != null ">
                AND `type` = #{type,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY create_time ASC
    </select>

</mapper>

