package com.faw.work.ais.aic.model.request;

import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Milvus默认集合初始化请求参数
 * <AUTHOR>
 */
@Data
@Schema(description = "Milvus默认集合初始化请求参数")
public class MilvusDefaultCollectionRequest {

    @Schema(description = "集合名称", required = true)
    private String collectionName;

    @Schema(description = "集合描述")
    private String description = "默认知识库向量集合";

    @Schema(description = "ID字段配置")
    private IdFieldConfig idField = new IdFieldConfig();

    @Schema(description = "向量字段配置")
    private VectorFieldConfig vectorField = new VectorFieldConfig();

    @Schema(description = "普通字段配置列表")
    private List<FieldConfig> fields = new ArrayList<>();

    /**
     * ID字段配置
     */
    @Data
    @Schema(description = "ID字段配置")
    public static class IdFieldConfig {
        @Schema(description = "ID字段名称")
        private String name = "id";

        @Schema(description = "是否自动生成ID")
        private Boolean autoId = true;
    }

    /**
     * 向量字段配置
     */
    @Data
    @Schema(description = "向量字段配置")
    public static class VectorFieldConfig {
        @Schema(description = "向量字段名称")
        private String name = "embedding";

        @Schema(description = "向量维度")
        private Integer dimension = 1024;

        @Schema(description = "度量类型", allowableValues = "COSINE, L2, IP")
        private String metricType = "COSINE";

        @Schema(description = "是否创建索引")
        private Boolean createIndex = true;

        @Schema(description = "索引类型", allowableValues = "FLAT, IVF_FLAT, IVF_SQ8, IVF_PQ, HNSW, AUTOINDEX")
        private String indexType = "AUTOINDEX";

        @Schema(description = "索引参数 - HNSW的efConstruction参数，建议值：200-500")
        private Integer efConstruction = 200;

        @Schema(description = "索引参数 - HNSW的M参数，建议值：4-64")
        private Integer m = 16;

        @Schema(description = "索引参数 - IVF系列的nlist参数，建议值：1-65536")
        private Integer nlist = 128;
    }

    /**
     * 普通字段配置
     */
    @Data
    @Schema(description = "普通字段配置")
    public static class FieldConfig {
        @Schema(description = "字段名称", required = true)
        private String name;

        @Schema(description = "字段类型", allowableValues = "Bool, Int8, Int16, Int32, Int64, Float, Double, VarChar")
        private String dataType = "VarChar";

        @Schema(description = "字段最大长度（仅VarChar类型有效）")
        private Integer maxLength = 1024;

        @Schema(description = "是否创建索引")
        private Boolean createIndex = false;

        @Schema(description = "索引类型", allowableValues = "FLAT, IVF_FLAT, IVF_SQ8, IVF_PQ, HNSW, AUTOINDEX")
        private String indexType = "AUTOINDEX";
    }

    /**
     * 转换为MilvusCollectionRequest
     * @return MilvusCollectionRequest对象
     */
    public MilvusCollectionRequest toMilvusCollectionRequest() {
        MilvusCollectionRequest request = new MilvusCollectionRequest();
        request.setCollectionName(this.collectionName);
        request.setDescription(this.description);

        // 设置ID字段配置
        MilvusCollectionRequest.IdFieldConfig idFieldConfig = new MilvusCollectionRequest.IdFieldConfig();
        idFieldConfig.setName(this.idField.getName());
        idFieldConfig.setDataType(DataType.Int64); // ID字段类型固定为Int64
        idFieldConfig.setAutoId(this.idField.getAutoId());
        request.setIdField(idFieldConfig);

        // 设置向量字段配置
        MilvusCollectionRequest.VectorFieldConfig vectorFieldConfig = new MilvusCollectionRequest.VectorFieldConfig();
        vectorFieldConfig.setName(this.vectorField.getName());
        vectorFieldConfig.setDimension(this.vectorField.getDimension());

        // 设置索引类型
        if (this.vectorField.getCreateIndex()) {
            IndexParam.IndexType indexType = convertToIndexType(this.vectorField.getIndexType());
            vectorFieldConfig.setIndexType(indexType);
        } else {
            vectorFieldConfig.setIndexType(null); // 不创建索引
        }

        // 设置度量类型
        if ("L2".equalsIgnoreCase(this.vectorField.getMetricType())) {
            vectorFieldConfig.setMetricType(IndexParam.MetricType.L2);
        } else if ("IP".equalsIgnoreCase(this.vectorField.getMetricType())) {
            vectorFieldConfig.setMetricType(IndexParam.MetricType.IP);
        } else {
            vectorFieldConfig.setMetricType(IndexParam.MetricType.COSINE);
        }

        request.setVectorField(vectorFieldConfig);

        // 添加普通字段
        if (this.fields != null && !this.fields.isEmpty()) {
            for (FieldConfig fieldConfig : this.fields) {
                MilvusCollectionRequest.ScalarFieldConfig scalarFieldConfig = new MilvusCollectionRequest.ScalarFieldConfig();
                scalarFieldConfig.setName(fieldConfig.getName());
                scalarFieldConfig.setDataType(convertToDataType(fieldConfig.getDataType()));

                // 设置最大长度（仅VarChar类型有效）
                if ("VarChar".equalsIgnoreCase(fieldConfig.getDataType())) {
                    scalarFieldConfig.setMaxLength(fieldConfig.getMaxLength());
                }

                // 设置索引
                scalarFieldConfig.setCreateIndex(fieldConfig.getCreateIndex());

                request.getScalarFields().add(scalarFieldConfig);
            }
        }

        // 添加默认字段（如果用户没有提供）
        if (this.fields == null || this.fields.isEmpty()) {
            // 添加默认的业务信息字段
            MilvusCollectionRequest.ScalarFieldConfig bizInfoFieldConfig = new MilvusCollectionRequest.ScalarFieldConfig();
            bizInfoFieldConfig.setName("biz_info");
            bizInfoFieldConfig.setDataType(DataType.VarChar);
            bizInfoFieldConfig.setMaxLength(1024);
            bizInfoFieldConfig.setCreateIndex(true);
            request.getScalarFields().add(bizInfoFieldConfig);

            // 添加默认的内容字段
            MilvusCollectionRequest.ScalarFieldConfig contentFieldConfig = new MilvusCollectionRequest.ScalarFieldConfig();
            contentFieldConfig.setName("content");
            contentFieldConfig.setDataType(DataType.VarChar);
            contentFieldConfig.setMaxLength(4096);
            contentFieldConfig.setCreateIndex(false);
            request.getScalarFields().add(contentFieldConfig);
        }

        return request;
    }

    /**
     * 将字符串类型转换为Milvus的DataType
     * @param dataTypeStr 字符串类型
     * @return Milvus的DataType
     */
    private DataType convertToDataType(String dataTypeStr) {
        if (dataTypeStr == null) {
            return DataType.VarChar;
        }

        switch (dataTypeStr.toUpperCase()) {
            case "BOOL":
                return DataType.Bool;
            case "INT8":
                return DataType.Int8;
            case "INT16":
                return DataType.Int16;
            case "INT32":
                return DataType.Int32;
            case "INT64":
                return DataType.Int64;
            case "FLOAT":
                return DataType.Float;
            case "DOUBLE":
                return DataType.Double;
            case "VARCHAR":
            default:
                return DataType.VarChar;
        }
    }

    /**
     * 将字符串索引类型转换为Milvus的IndexType
     * @param indexTypeStr 字符串索引类型
     * @return Milvus的IndexType
     */
    private IndexParam.IndexType convertToIndexType(String indexTypeStr) {
        if (indexTypeStr == null) {
            return IndexParam.IndexType.AUTOINDEX;
        }

        switch (indexTypeStr.toUpperCase()) {
            case "FLAT":
                return IndexParam.IndexType.FLAT;
            case "IVF_FLAT":
                return IndexParam.IndexType.IVF_FLAT;
            case "IVF_SQ8":
                return IndexParam.IndexType.IVF_SQ8;
            case "IVF_PQ":
                return IndexParam.IndexType.IVF_PQ;
            case "HNSW":
                return IndexParam.IndexType.HNSW;
            case "AUTOINDEX":
            default:
                return IndexParam.IndexType.AUTOINDEX;
        }
    }
}
