package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * FAQ机器人知识关联请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ机器人知识关联请求对象")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FaqRobotKnowledgeRequest {
    
    @NotNull(message = "机器人ID不能为空")
    @Schema(description = "机器人ID")
    private String robotId;


    @Schema(description = "类目id")
    private List<String> categoryId;
}