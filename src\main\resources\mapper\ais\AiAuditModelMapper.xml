<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.AiAuditModelDao">

    <resultMap id="AiAuditModel" type="com.faw.work.ais.model.AiAuditModel" >
        <result column="id" property="id" />
        <result column="audit_model_name" property="auditModelName" />
        <result column="create_time" property="createTime" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_code" property="updateUserCode" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `audit_model_name`,
        `create_time`,
        `create_user_code`,
        `create_user_name`,
        `update_time`,
        `update_user_code`,
        `update_user_name`
    </sql>

    <insert id="insert"  keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.Map" >
        INSERT INTO ai_audit_model (
            `audit_model_name`,
            `create_user_code`,
            `create_user_name`,
            `update_user_code`,
            `update_user_name`
        )
        VALUES(
            #{aiAuditModel.auditModelName},
            #{aiAuditModel.createUserCode},
            #{aiAuditModel.createUserName},
            #{aiAuditModel.updateUserCode},
            #{aiAuditModel.updateUserName}
        )
    </insert>

    <delete id="delete" parameterType="java.util.Map" >
        DELETE FROM ai_audit_model
        where id = #{id}

    </delete>

    <update id="update" parameterType="java.util.Map" >
        UPDATE ai_audit_model
         <set>
            <if test="aiAuditModel.auditModelName != null and aiAuditModel.auditModelName != '' " >
                audit_model_name = #{aiAuditModel.auditModelName},
            </if>

            <if test="aiAuditModel.createUserCode != null and aiAuditModel.createUserCode != '' " >
                create_user_code = #{aiAuditModel.createUserCode},
            </if>
            <if test="aiAuditModel.createUserName != null and aiAuditModel.createUserName != '' " >
                create_user_name = #{aiAuditModel.createUserName},
            </if>

            <if test="aiAuditModel.updateUserCode != null and aiAuditModel.updateUserCode != '' " >
                update_user_code = #{aiAuditModel.updateUserCode},
            </if>
            <if test="aiAuditModel.updateUserName != null and aiAuditModel.updateUserName != '' " >
                update_user_name = #{aiAuditModel.updateUserName},
            </if>
         </set>
       where  id = #{aiAuditModel.id}

    </update>


    <select id="getAiAuditModelList" resultMap="AiAuditModel">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_audit_model
        <where>
          <if test="aiAuditModel.id != null and aiAuditModel.id != '' " >
              AND  id = #{aiAuditModel.id}
          </if>
          <if test="aiAuditModel.auditModelName != null and aiAuditModel.auditModelName != '' " >
              AND  audit_model_name like concat('%',#{aiAuditModel.auditModelName},'%')
          </if>
          <if test="aiAuditModel.createTime != null and aiAuditModel.createTime != '' " >
              AND  create_time = #{aiAuditModel.createTime}
          </if>
          <if test="aiAuditModel.createUserCode != null and aiAuditModel.createUserCode != '' " >
              AND  create_user_code = #{aiAuditModel.createUserCode}
          </if>
          <if test="aiAuditModel.createUserName != null and aiAuditModel.createUserName != '' " >
              AND  create_user_name = #{aiAuditModel.createUserName}
          </if>
          <if test="aiAuditModel.updateTime != null and aiAuditModel.updateTime != '' " >
              AND  update_time = #{aiAuditModel.updateTime}
          </if>
          <if test="aiAuditModel.updateUserCode != null and aiAuditModel.updateUserCode != '' " >
              AND  update_user_code = #{aiAuditModel.updateUserCode}
          </if>
          <if test="aiAuditModel.updateUserName != null and aiAuditModel.updateUserName != '' " >
              AND  update_user_name = #{aiAuditModel.updateUserName}
          </if>

        </where>
        order by id desc
     </select>



    <select id="getExitNameAiAuditModelList" resultMap="AiAuditModel">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_audit_model
        where audit_model_name = #{aiAuditModel.auditModelName}
        <if test="aiAuditModel.id != null  " >
            AND  id != #{aiAuditModel.id}
        </if>


    </select>

    <select id="getAiAuditModelById" parameterType="java.util.Map" resultMap="AiAuditModel">
        SELECT <include refid="Base_Column_List" />
        FROM ai_audit_model
       where  id = #{id}

    </select>



</mapper>

