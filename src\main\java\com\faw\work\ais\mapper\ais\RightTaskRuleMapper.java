package com.faw.work.ais.mapper.ais;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.vo.ai.AiCoverDicVO;
import com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleChartVO;
import com.faw.work.ais.entity.vo.ai.HumanSampleRightRateRuleVO;
import com.faw.work.ais.model.RightTaskRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface RightTaskRuleMapper extends BaseMapper<RightTaskRule> {
    HumanSampleRightRateRuleVO getSampleTaskRuleInfo(@Param("numberEmployeeDTO") NumberEmployeeDTO numberEmployeeDTO, @Param("bizTypes") List<String> bizTypes);
    List<HumanSampleRightRateRuleChartVO> getRateTrendChartVO(@Param("numberEmployeeDTO") NumberEmployeeDTO numberEmployeeDTO, @Param("bizTypes") List<String> bizTypes);

}
