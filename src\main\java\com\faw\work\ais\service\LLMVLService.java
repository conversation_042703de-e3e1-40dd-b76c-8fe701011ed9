package com.faw.work.ais.service;

import com.faw.work.ais.entity.request.CommentSummaryRequest;
import com.faw.work.ais.entity.request.PostSummaryRequest;

import java.util.List;

public interface LLMVLService {

    /**
     * 分析图片内容
     *
     * @param photoUrls 图片URL列表
     * @param query 查询文本
     * @return 分析结果
     */
    String analyzePhoto(List<String> photoUrls, String query);

    /**
     * 分析单条视频内容
     *
     * @param videoUrl 视频帧URL列表
     * @param query 查询文本
     * @return 分析结果
     */
    String analyzeVideo(String videoUrl, String query);

    /**
     * OCR识别图片文字
     *
     * @param photoUrl 图片URL
     * @return 识别结果
     */
    String ocrImage(String photoUrl);



    /**
     * 评论评分
     *
     * @param topic 主题
     * @param content 评论内容
     * @return 分析结果
     */
    String commentScore(String topic, String content);

    /**
     * 帖子评分
     *
     * @param topic 主题
     * @param content 帖子内容
     * @param picUrls 图片URL列表
     * @return 分析结果
     */
    String postScore(String topic, String content, List<String> picUrls, Boolean flag);

    /**
     * 评论总结
     *
     * @param requests
     * @return 分析结果
     */
    String commentSummary(List<CommentSummaryRequest> requests);

    String postSummary(List<PostSummaryRequest> requests);

    String postSummaryAll(List<String> batchResults);

    String truthValidate(String content, String query);

    String extractVehicleParam(String content);

}
