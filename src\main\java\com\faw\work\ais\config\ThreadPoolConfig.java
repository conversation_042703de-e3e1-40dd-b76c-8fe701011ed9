package com.faw.work.ais.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * <AUTHOR>
 * */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    //自定义使用参数
    @Value("${async.executor.thread.core_pool_size}")
    private int corePoolSize;   //配置核心线程数
    @Value("${async.executor.thread.max_pool_size}")
    private int maxPoolSize;    //配置最大线程数
    @Value("${async.executor.thread.queue_capacity}")
    private int queueCapacity;
    @Value("${async.executor.thread.name.prefix}")
    private String namePrefix;
    @Value("${async.executor.thread.keep_alive_seconds}")
    private int keepAliveSeconds;

    @Bean("threadPoolTaskExecutor")
    public Executor threadPoolTaskExecutor() {
        String threadNamePrefix = namePrefix;
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        try {
            log.info("开始初始化线程池 -->threadPoolTaskExecutor");
            taskExecutor.setThreadNamePrefix(threadNamePrefix);
            taskExecutor.setCorePoolSize(corePoolSize);
            taskExecutor.setMaxPoolSize(maxPoolSize);
            taskExecutor.setQueueCapacity(queueCapacity);
            taskExecutor.setKeepAliveSeconds(keepAliveSeconds);
            //设置线程池内线程名称的前缀-------阿里编码规约推荐--方便出错后进行调试
            taskExecutor.setThreadNamePrefix(namePrefix);
            // rejection-policy：当pool已经达到max size的时候，如何处理新任务
            // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
            taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
            //进行加载
            taskExecutor.initialize();
            log.info("初始化线程池完成:{}核心线程为{}-->", threadNamePrefix, taskExecutor.getCorePoolSize());
            return taskExecutor;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("初始化线程池失败:{}失败原因为:{}", threadNamePrefix, e.getMessage());
            return null;
        }
    }

    /**
     * 文档分片处理线程池
     * 用于并发调用大模型处理文档分片
     */
    @Bean("documentSplitThreadPool")
    public ThreadPoolTaskExecutor documentSplitThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        try {
            log.info("开始初始化文档分片处理线程池 -->documentSplitThreadPool");
            // 设置核心线程数为10，刚好处理一个批次的数据
            executor.setCorePoolSize(20);
            // 最大线程数设置为20，允许一定程度的并发增长
            executor.setMaxPoolSize(50);
            // 队列容量
            executor.setQueueCapacity(500);
            // 线程名称前缀
            executor.setThreadNamePrefix("doc-split-");
            // 线程空闲时间
            executor.setKeepAliveSeconds(60);
            // 拒绝策略：由调用线程处理
            executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
            // 初始化线程池
            executor.initialize();
            log.info("初始化文档分片处理线程池完成，核心线程数: {}", executor.getCorePoolSize());
            return executor;
        } catch (Exception e) {
            log.error("初始化文档分片处理线程池失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化文档分片处理线程池失败", e);
        }
    }


    /**
     * Embedding处理线程池
     * 用于并发调用大模型处理文档分片
     */
    @Bean("embeddingThreadPool")
    public ThreadPoolTaskExecutor embeddingThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        try {
            log.info("开始初始化Embedding处理线程池 -->embeddingThreadPool");
            // 设置核心线程数为10，刚好处理一个批次的数据
            executor.setCorePoolSize(5);
            // 最大线程数设置为20，允许一定程度的并发增长
            executor.setMaxPoolSize(50);
            // 队列容量
            executor.setQueueCapacity(500);
            // 线程名称前缀
            executor.setThreadNamePrefix("embedding-thread-");
            // 线程空闲时间
            executor.setKeepAliveSeconds(60);
            // 拒绝策略：由调用线程处理
            executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
            // 初始化线程池
            executor.initialize();
           log.info("初始化Embedding处理线程池完成，核心线程数: {}", executor.getCorePoolSize());
            return executor;
        } catch (Exception e) {
           log.error("初始化Embedding处理线程池失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化Embedding处理线程池失败", e);
        }
    }
}
