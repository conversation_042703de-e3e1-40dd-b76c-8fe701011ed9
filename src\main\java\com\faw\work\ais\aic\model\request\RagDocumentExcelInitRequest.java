package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * Excel数据初始化到非结构化文档请求
 *
 * <AUTHOR>
 */
@Data
@Validated
@Schema(description = "Excel数据初始化到非结构化文档请求")
public class RagDocumentExcelInitRequest {

    @NotNull(message = "类目ID不能为空")
    @Schema(description = "类目ID", required = true)
    private Long categoryId;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long ragKnowledgeId;

    @NotNull(message = "上传的Excel文件不能为空")
    @Schema(description = "上传的Excel文件（必须包含content列）", required = true)
    private MultipartFile file;

    @Schema(description = "标签")
    @NotNull(message = "标签不能为空")
    private String label;
}
