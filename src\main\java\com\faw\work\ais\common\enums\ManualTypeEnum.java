package com.faw.work.ais.common.enums;

import lombok.Getter;

/**
 * 手册类型枚举
 * <AUTHOR>
 */
@Getter
public enum ManualTypeEnum {

    /**
     * 用户手册 (A)
     */
    USER_MANUAL("A", "User Manual"),

    /**
     * 维修手册 (B)
     */
    MAINTENANCE_MANUAL("B", "Maintenance Manual");

    private final String code;
    private final String msg;

    ManualTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 根据code获取枚举实例
     */
    public static ManualTypeEnum getByCode(String code) {
        for (ManualTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}