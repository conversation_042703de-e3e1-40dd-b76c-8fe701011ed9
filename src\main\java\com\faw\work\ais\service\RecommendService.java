package com.faw.work.ais.service;

import com.faw.work.ais.common.dto.chat.RecommendRequest;

import java.util.List;

public interface RecommendService {

    /**
     * 根据用户请求获取推荐问题列表
     * @param request 推荐请求
     * @return 推荐问题列表
     */
    List<String> getRecommendations(RecommendRequest request);

    /**
     * 根据用户请求获取指定数量的推荐问题列表
     * @param request 推荐请求
     * @param count 数量
     * @return 推荐问题列表
     */
    List<String> getRecommendations(RecommendRequest request, int count, float similarityThreshold, float rate, List<String> excludeIds);
} 