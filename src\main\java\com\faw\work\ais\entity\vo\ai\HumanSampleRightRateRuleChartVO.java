package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "人工抽检准确率大饼图-规则维度")
@Data
public class HumanSampleRightRateRuleChartVO {

    @Schema(description = "人工抽检总数")
    private String sampleTotalNum;

    @Schema(description = "人工抽检准确率,算法为(TP+FN/抽检总数)")
    private String sampleRightRate;

    @Schema(description = "TP-AI通过人工通过-准确率")
    private String aiPassHumanPassRate;

    @Schema(description = "TP-AI通过人工通过-数量")
    private String aiPassHumanPassNum;

    @Schema(description = "FP-AI驳回人工通过-准确率")
    private String aiUnpassHumanPassRate;

    @Schema(description = "FP-AI驳回人工通过-数量")
    private String aiUnpassHumanPassNum;

    @Schema(description = "TN-AI通过人工驳回-准确率")
    private String aiUnpassHumanUnpassRate;

    @Schema(description = "TN-AI通过人工驳回-数量")
    private String aiUnpassHumanUnpassNum;

    @Schema(description = "FN-AI驳回人工驳回-准确率")
    private String aiPassHumanUnpassRate;

    @Schema(description = "FN-AI驳回人工驳回-数量")
    private String aiPassHumanUnpassNum;
    @Schema(description = "系统id")
    private String systemId;

    @Schema(description = "系统日期")
    private String checkDate;
}
