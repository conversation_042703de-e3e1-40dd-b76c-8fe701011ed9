package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FAQ机器人知识绑定响应对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ机器人知识绑定响应对象")
public class FaqRobotKnowledgeBindResponse {
    
    @Schema(description = "总处理知识数量")
    private Integer totalCount;
    
    @Schema(description = "向量化成功数量")
    private Integer successCount;
    
    @Schema(description = "向量化失败数量")
    private Integer failCount;
    
    @Schema(description = "绑定是否成功")
    private Boolean isSuccess;
} 