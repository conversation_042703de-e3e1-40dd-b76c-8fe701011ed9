package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.mapper.rag.RagKnowledgeDocumentJoinsMapper;
import com.faw.work.ais.aic.model.domain.RagKnowledgeDocumentJoinsPO;
import com.faw.work.ais.aic.model.domain.RagKnowledgePO;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentJoinsPageRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentBindRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentUnbindRequest;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentBindResponse;
import com.faw.work.ais.aic.service.RagKnowledgeDocumentJoinsService;
import com.faw.work.ais.aic.service.RagKnowledgeService;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UserThreadLocalUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 知识库文档关联表 服务实现类
 *
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
public class RagKnowledgeDocumentJoinsServiceImpl extends ServiceImpl<RagKnowledgeDocumentJoinsMapper, RagKnowledgeDocumentJoinsPO> implements RagKnowledgeDocumentJoinsService {

    @Autowired
    private RagKnowledgeService ragKnowledgeService;

    @Autowired
    private RagDocumentService ragDocumentService;

    @Override
    public List<RagKnowledgeDocumentJoinsPO> getJoinsList(RagKnowledgeDocumentJoinsPO joins) {
        LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();

        if (joins != null) {
            // 按条件查询
            if (joins.getRagKnowledgeId() != null) {
                queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getRagKnowledgeId, joins.getRagKnowledgeId());
            }

            if (joins.getDocumentId() != null) {
                queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getDocumentId, joins.getDocumentId());
            }
        }

        // 按ID降序排序
        queryWrapper.orderByDesc(RagKnowledgeDocumentJoinsPO::getId);

        return this.list(queryWrapper);
    }

    @Override
    public Page<RagKnowledgeDocumentJoinsPO> getJoinsPage(RagKnowledgeDocumentJoinsPageRequest request) {
        Page<RagKnowledgeDocumentJoinsPO> page = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (request.getBaseId() != null) {
            queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getRagKnowledgeId, request.getBaseId());
        }

        if (request.getDocumentId() != null) {
            queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getDocumentId, request.getDocumentId());
        }

        // 按ID降序排序
        queryWrapper.orderByDesc(RagKnowledgeDocumentJoinsPO::getId);

        return this.page(page, queryWrapper);
    }

    @Override
    public List<RagKnowledgeDocumentJoinsPO> getByBaseId(Long baseId) {
        if (baseId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getRagKnowledgeId, baseId);
        return this.list(queryWrapper);
    }

    @Override
    public List<RagKnowledgeDocumentJoinsPO> getByDocumentId(Long documentId) {
        if (documentId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getDocumentId, documentId);
        return this.list(queryWrapper);
    }

    @Override
    public boolean existsJoin(Long baseId, Long documentId) {
        if (baseId == null || documentId == null) {
            return false;
        }

        LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getRagKnowledgeId, baseId)
                    .eq(RagKnowledgeDocumentJoinsPO::getDocumentId, documentId);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public RagKnowledgeDocumentBindResponse bindDocuments(RagKnowledgeDocumentBindRequest request) {
        log.info("开始绑定知识库文档关系: ragKnowledgeId={}, documentIds={}",
            request.getRagKnowledgeId(), request.getDocumentIds());

        // 1. 校验知识库是否存在
        RagKnowledgePO knowledge = ragKnowledgeService.getById(request.getRagKnowledgeId());
        if (knowledge == null) {
            throw new BizException("知识库不存在: ragKnowledgeId=" + request.getRagKnowledgeId());
        }

        // 2. 校验文档是否存在
        List<RagDocumentPO> documents = ragDocumentService.listByIds(request.getDocumentIds());
        if (CollUtil.isEmpty(documents)) {
            throw new BizException("文档列表为空或文档不存在");
        }

        // 3. 构建响应对象
        RagKnowledgeDocumentBindResponse response = new RagKnowledgeDocumentBindResponse();
        response.setRagKnowledgeId(request.getRagKnowledgeId());
        response.setKnowledgeName(knowledge.getName());
        response.setSuccessDocumentIds(new ArrayList<>());
        response.setFailDocumentIds(new ArrayList<>());
        response.setFailReasons(new ArrayList<>());

        // 4. 批量绑定处理
        LocalDateTime now = LocalDateTime.now();
        String currentUser = UserThreadLocalUtil.getRealName();

        for (Long documentId : request.getDocumentIds()) {
            try {
                // 检查是否已经绑定
                if (existsJoin(request.getRagKnowledgeId(), documentId)) {
                    response.getFailDocumentIds().add(documentId);
                    response.getFailReasons().add("文档ID " + documentId + " 已经绑定到该知识库");
                    continue;
                }

                // 检查文档是否存在
                boolean documentExists = documents.stream()
                    .anyMatch(doc -> doc.getId().equals(documentId));
                if (!documentExists) {
                    response.getFailDocumentIds().add(documentId);
                    response.getFailReasons().add("文档ID " + documentId + " 不存在");
                    continue;
                }

                // 创建关联关系
                RagKnowledgeDocumentJoinsPO join = new RagKnowledgeDocumentJoinsPO();
                join.setRagKnowledgeId(request.getRagKnowledgeId());
                join.setDocumentId(documentId);
                join.setCreatedBy(currentUser);
                join.setCreatedAt(now);
                join.setUpdatedBy(currentUser);
                join.setUpdatedAt(now);

                boolean saveResult = this.save(join);
                if (saveResult) {
                    response.getSuccessDocumentIds().add(documentId);
                } else {
                    response.getFailDocumentIds().add(documentId);
                    response.getFailReasons().add("文档ID " + documentId + " 保存关联关系失败");
                }

            } catch (Exception e) {
                log.error("绑定文档失败: documentId={}", documentId, e);
                response.getFailDocumentIds().add(documentId);
                response.getFailReasons().add("文档ID " + documentId + " 绑定异常: " + e.getMessage());
            }
        }

        // 5. 设置统计信息
        response.setSuccessCount(response.getSuccessDocumentIds().size());
        response.setFailCount(response.getFailDocumentIds().size());

        log.info("知识库文档绑定完成: ragKnowledgeId={}, 成功={}, 失败={}",
            request.getRagKnowledgeId(), response.getSuccessCount(), response.getFailCount());

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RagKnowledgeDocumentBindResponse unbindDocuments(RagKnowledgeDocumentUnbindRequest request) {
        log.info("开始解绑知识库文档关系: ragKnowledgeId={}, documentIds={}",
            request.getRagKnowledgeId(), request.getDocumentIds());

        // 1. 校验知识库是否存在
        RagKnowledgePO knowledge = ragKnowledgeService.getById(request.getRagKnowledgeId());
        if (knowledge == null) {
            throw new BizException("知识库不存在: ragKnowledgeId=" + request.getRagKnowledgeId());
        }

        // 2. 构建响应对象
        RagKnowledgeDocumentBindResponse response = new RagKnowledgeDocumentBindResponse();
        response.setRagKnowledgeId(request.getRagKnowledgeId());
        response.setKnowledgeName(knowledge.getName());
        response.setSuccessDocumentIds(new ArrayList<>());
        response.setFailDocumentIds(new ArrayList<>());
        response.setFailReasons(new ArrayList<>());

        // 3. 批量解绑处理
        for (Long documentId : request.getDocumentIds()) {
            try {
                // 检查是否存在绑定关系
                if (!existsJoin(request.getRagKnowledgeId(), documentId)) {
                    response.getFailDocumentIds().add(documentId);
                    response.getFailReasons().add("文档ID " + documentId + " 未绑定到该知识库");
                    continue;
                }

                // 删除关联关系
                LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getRagKnowledgeId, request.getRagKnowledgeId())
                           .eq(RagKnowledgeDocumentJoinsPO::getDocumentId, documentId);

                boolean removeResult = this.remove(queryWrapper);
                if (removeResult) {
                    response.getSuccessDocumentIds().add(documentId);
                } else {
                    response.getFailDocumentIds().add(documentId);
                    response.getFailReasons().add("文档ID " + documentId + " 删除关联关系失败");
                }

            } catch (Exception e) {
                log.error("解绑文档失败: documentId={}", documentId, e);
                response.getFailDocumentIds().add(documentId);
                response.getFailReasons().add("文档ID " + documentId + " 解绑异常: " + e.getMessage());
            }
        }

        // 4. 设置统计信息
        response.setSuccessCount(response.getSuccessDocumentIds().size());
        response.setFailCount(response.getFailDocumentIds().size());

        log.info("知识库文档解绑完成: ragKnowledgeId={}, 成功={}, 失败={}",
            request.getRagKnowledgeId(), response.getSuccessCount(), response.getFailCount());

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByKnowledgeId(Long ragKnowledgeId) {
        if (ragKnowledgeId == null) {
            return 0;
        }

        log.info("删除知识库的所有关联关系: ragKnowledgeId={}", ragKnowledgeId);

        LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getRagKnowledgeId, ragKnowledgeId);

        int count = Math.toIntExact(this.count(queryWrapper));
        boolean result = this.remove(queryWrapper);

        if (result) {
            log.info("成功删除知识库关联关系: ragKnowledgeId={}, 删除数量={}", ragKnowledgeId, count);
            return count;
        } else {
            log.warn("删除知识库关联关系失败: ragKnowledgeId={}", ragKnowledgeId);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByDocumentId(Long documentId) {
        if (documentId == null) {
            return 0;
        }

        log.info("删除文档的所有关联关系: documentId={}", documentId);

        LambdaQueryWrapper<RagKnowledgeDocumentJoinsPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RagKnowledgeDocumentJoinsPO::getDocumentId, documentId);

        int count = Math.toIntExact(this.count(queryWrapper));
        boolean result = this.remove(queryWrapper);

        if (result) {
            log.info("成功删除文档关联关系: documentId={}, 删除数量={}", documentId, count);
            return count;
        } else {
            log.warn("删除文档关联关系失败: documentId={}", documentId);
            return 0;
        }
    }
}