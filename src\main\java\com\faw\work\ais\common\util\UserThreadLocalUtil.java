package com.faw.work.ais.common.util;


import com.faw.work.ais.common.UserInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 当前本地线程存储用户信息等
 */
@Slf4j
public final class UserThreadLocalUtil {
	/**
	 * 当前登录人
	 */
	private static final ThreadLocal<UserInfo> CURRENT_USER = new ThreadLocal<>();

	public static void setUserInfo(UserInfo userInfo) {
		CURRENT_USER.set(userInfo);
	}

	/**
	 * 获取iwork用户id，例如panxin5
	 * @return {@link String}
	 */
	public static String getCurrentName() {
		UserInfo userInfo = CURRENT_USER.get();
		if (userInfo != null) {
			return userInfo.getUserName();
		}
		log.warn("the user info is null");
		return null;
	}

	/**
	 * 获取真实姓名  eg.潘鑫
	 *
	 * @return {@link String }
	 */
	public static String getRealName() {
		UserInfo userInfo = CURRENT_USER.get();
		if (userInfo != null) {
			return userInfo.getRealName();
		}
		log.warn("the user info is null");
		return null;
	}

	public static String getIdmId() {
		UserInfo userInfo = CURRENT_USER.get();
		if (userInfo != null) {
			return userInfo.getIdmId();
		}
		log.warn("the user info is null");
		return null;
	}

	public static void clear() {
		CURRENT_USER.remove();
	}
}
