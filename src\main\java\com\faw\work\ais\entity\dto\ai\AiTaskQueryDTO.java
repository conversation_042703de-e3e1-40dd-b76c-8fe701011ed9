package com.faw.work.ais.entity.dto.ai;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 历史结果查询接口入参
 */

@Schema(description = "历史结果查询接口入参")
@Data
public class AiTaskQueryDTO extends Page {

    @Schema(description = "交易时间开始")
    @ApiModelProperty(value ="处理时间开始")
    private String dealTimeStart;

    @Schema(description = "交易时间结束")
    @ApiModelProperty(value = "处理时间介绍")
    private String dealTimeEnd;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private String taskType;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String bizType;

    /**
     * 任务结果
     */
    @Schema(description = "任务结果")
    private String taskResult;

    /**
     * 文件类型0表示文档1表示图片
     */
    @Schema(description = "文件类型0表示文档1表示图片")
    private String contentType;

    /**
     * 系统id
     */
    @Schema(description = "系统id")
    private String systemId;

    /**
     * 任务的状态0已收到，1AI已处理，2表示未回调，3表示已回调
     */
    @Schema(description = "任务的状态0已收到，1AI已处理，2表示未回调，3表示已回调")
    private String taskStatus;

    /**
     * 业务主键
     */
    @Schema(description = "业务主键")
    private String bizId;

    /**
     * version
     */
    @Schema(description = "版本")
    private String version;
}
