package com.faw.work.ais.convert;

import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import com.faw.work.ais.aic.model.request.FaqCategoryCreateRequest;
import com.faw.work.ais.aic.model.request.FaqCategoryUpdateRequest;
import com.faw.work.ais.aic.model.response.FaqCategoryResponse;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类目实体转换器
 */
@Component
public class FaqCategoryPOConverter {
    
    /**
     * 将创建请求转换为实体
     */
    public FaqCategoryPO convert2FaqCategoryPO(FaqCategoryCreateRequest request) {
        if (request == null) {
            return null;
        }
        
        FaqCategoryPO po = new FaqCategoryPO();
        po.setName(request.getName());
        po.setParentId(request.getParentId());
        po.setCreatedBy(request.getCreatedBy());
        po.setCreatedAt(LocalDateTime.now());
        po.setUpdatedBy(request.getCreatedBy());
        po.setUpdatedAt(LocalDateTime.now());
        
        return po;
    }
    
    /**
     * 将更新请求应用到实体
     */
    public void updateFaqCategoryPO(FaqCategoryPO po, FaqCategoryUpdateRequest request) {
        if (po == null || request == null) {
            return;
        }
        
        po.setName(request.getName());
        po.setUpdatedBy(request.getUpdatedBy());
        po.setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 将实体转换为响应
     */
    public FaqCategoryResponse convert2FaqCategoryResponse(FaqCategoryPO po) {
        if (po == null) {
            return null;
        }
        
        FaqCategoryResponse response = new FaqCategoryResponse();
        response.setId(po.getId());
        response.setName(po.getName());
        response.setParentId(po.getParentId());
        response.setCreatedBy(po.getCreatedBy());
        response.setCreatedAt(po.getCreatedAt());
        response.setUpdatedBy(po.getUpdatedBy());
        response.setUpdatedAt(po.getUpdatedAt());
        
        return response;
    }
    
    /**
     * 将实体列表转换为树形响应
     */
    public List<FaqCategoryResponse> convert2FaqCategoryTreeResponse(List<FaqCategoryPO> poList) {
        if (poList == null || poList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 转换为响应对象
        List<FaqCategoryResponse> responseList = poList.stream()
                .map(this::convert2FaqCategoryResponse)
                .collect(Collectors.toList());
        
        // 构建父子关系映射
        Map<String, List<FaqCategoryResponse>> childrenMap = responseList.stream()
                .filter(item -> item.getParentId() != null)
                .collect(Collectors.groupingBy(FaqCategoryResponse::getParentId));

        // 设置子节点
        responseList.forEach(item -> {
            if (item.getId() != null) {
                List<FaqCategoryResponse> children = childrenMap.get(item.getId());
                item.setChildren(children);
            }
        });

        // 返回顶层节点
        return responseList.stream()
                .filter(item -> item.getParentId() == null)
                .collect(Collectors.toList());
    }
}