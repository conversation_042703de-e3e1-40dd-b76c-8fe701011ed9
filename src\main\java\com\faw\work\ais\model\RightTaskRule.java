package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "任务规则表")
public class RightTaskRule {

    @Schema(description = "主键")
    @TableId(type = IdType.AUTO)
    private Integer id;

    @Schema(description = "通过")
    private Integer aiPass;

    @Schema(description = "人工通过")
    private Integer humanPass;

    @Schema(description = "拒绝")
    private Integer aiReject;

    @Schema(description = "人工驳回")
    private Integer humanReject;

    @Schema(description = "系统ID")
    private String systemId;

    @Schema(description = "任务类型")
    private String bizType;

    @Schema(description = "校验日期")
    private String checkDate;
}
