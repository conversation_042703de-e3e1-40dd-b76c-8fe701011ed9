package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 规则信息
 * @program: work-ais
 * @description:
 * @create: 2022-04-08 10:07
 **/
@Data
public class TaskRuleVO {

    @Schema(description = "规则代码")
    private String taskType;

    @Schema(description = "规则名称")
    private String taskName;

    @Schema(description = "规则创建时间")
    private String createTime;

    @Schema(description = "业务场景类型")
    private String bizType;

    @Schema(description = "节约时间单位分钟")
    private String saveTime;

    @Schema(description = "系统代码")
    private String systemId;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "规则对应的调用大模型次数")
    private String countRule;

}
