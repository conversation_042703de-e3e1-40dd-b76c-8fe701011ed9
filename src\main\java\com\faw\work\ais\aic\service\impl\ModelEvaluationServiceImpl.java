package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.dcp.common.files.service.FileStorageService;
import com.dcp.common.files.vo.FileInfoVO;
import com.dcp.common.rest.Result;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.model.dto.ModelEvaluationInputDTO;
import com.faw.work.ais.aic.model.dto.ModelEvaluationOutputDTO;
import com.faw.work.ais.aic.model.request.ModelEvaluationRequest;
import com.faw.work.ais.aic.model.response.ModelEvaluationResponse;
import com.faw.work.ais.aic.service.ModelEvaluationService;
import com.faw.work.ais.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 大模型评测服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelEvaluationServiceImpl implements ModelEvaluationService {

    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    @Qualifier("retryExecutor")
    private Executor evaluationExecutor;

    private static final String XLSX = ".xlsx";
    private static final String XLS = ".xls";

    @Override
    public ModelEvaluationResponse evaluateModel(MultipartFile file, ModelEvaluationRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始大模型评测，文件名: {}, 参数: {}", file.getOriginalFilename(), JSONUtil.toJsonStr(request));

        // 校验文件
        validateFile(file);

        // 读取Excel数据
        List<ModelEvaluationInputDTO> inputData = readExcelData(file);
        log.info("读取到{}条数据", inputData.size());

        if (CollUtil.isEmpty(inputData)) {
            throw new BizException("Excel文件中没有有效数据");
        }

        // 使用线程安全的集合存储结果
        List<ModelEvaluationOutputDTO> outputData = Collections.synchronizedList(new ArrayList<>());
        List<String> errorMessages = Collections.synchronizedList(new ArrayList<>());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 创建异步任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < inputData.size(); i++) {
            final int index = i;
            final ModelEvaluationInputDTO input = inputData.get(i);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始处理第{}行数据: {}", index + 1, input.getUserInput());

                    // 构建评测prompt
                    // String prompt = buildEvaluationPrompt(input.getUserInput(), input.getModelResult());
                    String prompt = input.getUserInput();

                    // 调用大模型进行评测
                    String testResult = BaiLianUtils.callForObject(
                            request.getWorkSpace(),
                            request.getApiKey(),
                            request.getAppId(),
                            prompt,
                            String.class
                    );

                    if (StrUtil.isNotBlank(testResult)) {
                        // 创建输出数据
                        ModelEvaluationOutputDTO output = ModelEvaluationOutputDTO.builder()
                                .userInput(input.getUserInput())
                                .groundAnswer(input.getGroundAnswer())
                                .testResult(testResult)
                                .build();

                        outputData.add(output);
                        successCount.incrementAndGet();
                        log.info("第{}行数据处理成功", index + 1);
                    } else {
                        String errorMsg = String.format("第%d行：大模型返回结果为空", index + 1);
                        errorMessages.add(errorMsg);
                        failCount.incrementAndGet();
                        log.warn(errorMsg);
                    }

                } catch (Exception e) {
                    String errorMsg = String.format("第%d行处理失败: %s", index + 1, e.getMessage());
                    errorMessages.add(errorMsg);
                    failCount.incrementAndGet();
                    log.error("第{}行数据处理失败", index + 1, e);
                }
            }, evaluationExecutor);

            futures.add(future);
        }

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            log.info("所有评测任务已完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        catch (Exception e) {
            log.error("等待评测任务完成时发生异常", e);
            throw new BizException("评测任务执行异常: " + e.getMessage());
        }

        // 生成输出Excel文件
        String downloadUrl = generateOutputExcel(outputData);

        long endTime = System.currentTimeMillis();
        long processingTime = endTime - startTime;

        ModelEvaluationResponse response = ModelEvaluationResponse.builder()
                .total(inputData.size())
                .successCount(successCount.get())
                .failCount(failCount.get())
                .downloadUrl(downloadUrl)
                .errorMessages(errorMessages)
                .processingTime(processingTime)
                .build();

        log.info("大模型评测完成，总计: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                response.getTotal(), response.getSuccessCount(), response.getFailCount(), processingTime);

        return response;
    }

    /**
     * 校验上传文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BizException("上传文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename) || (!originalFilename.endsWith(XLSX) && !originalFilename.endsWith(XLS))) {
            throw new BizException("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }
    }

    /**
     * 读取Excel数据
     */
    private List<ModelEvaluationInputDTO> readExcelData(MultipartFile file) {
        List<ModelEvaluationInputDTO> dataList = new ArrayList<>();

        try {
            ExcelReader excelReader = EasyExcel.read(file.getInputStream(), ModelEvaluationInputDTO.class, new ReadListener<ModelEvaluationInputDTO>() {
                @Override
                public void invoke(ModelEvaluationInputDTO data, AnalysisContext context) {
                    if (data != null && StrUtil.isNotBlank(data.getUserInput()) && StrUtil.isNotBlank(data.getGroundAnswer())) {
                        dataList.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共{}条有效数据", dataList.size());
                }
            }).build();

            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
            excelReader.finish();

        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            throw new BizException("读取Excel文件失败: " + e.getMessage());
        }

        return dataList;
    }

    /**
     * 构建评测prompt
     */
    private String buildEvaluationPrompt(String userInput, String modelResult) {
        return String.format(
                "请对以下大模型的回答进行评测：\n\n" +
                        "用户输入：%s\n\n" +
                        "模型回答：%s\n\n" +
                        "请从准确性、相关性、完整性等维度对模型回答进行评价，并给出评测结果。",
                userInput, modelResult
        );
    }

    /**
     * 生成输出Excel文件
     */
    private String generateOutputExcel(List<ModelEvaluationOutputDTO> outputData) {
        try {
            // 1. 生成Excel文件到字节数组输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, ModelEvaluationOutputDTO.class)
                    .sheet("评测结果")
                    .doWrite(outputData);

            // 2. 生成文件名和扩展名
            String originalFilename = "evaluation_result_" + System.currentTimeMillis() + ".xlsx";
            String extension = FilenameUtils.getExtension(originalFilename);

            // 3. 生成唯一的文件标识符
            String key = UUID.randomUUID().toString().replace("-", "") + "." + extension;

            // 4. 将字节数组转换为MultipartFile（根据fileStorageService的接口要求）
            byte[] fileBytes = outputStream.toByteArray();
            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    originalFilename,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileBytes
            );

            // 5. 上传文件到华为云OBS
            Result<FileInfoVO> fileResult = fileStorageService.huaweiObsUpload(file, key);

            // 6. 检查上传结果
            if (fileResult == null || fileResult.getData() == null) {
                throw new BizException("文件上传失败，返回结果为空");
            }

            String downloadUrl = fileResult.getData().getUrl();
            log.info("输出Excel文件生成成功，文件名: {}, 下载链接: {}", originalFilename, downloadUrl);

            // 7. 关闭输出流
            outputStream.close();

            return downloadUrl;

        } catch (Exception e) {
            log.error("生成输出Excel文件失败", e);
            throw new BizException("生成输出文件失败: " + e.getMessage());
        }
    }


}
