package com.faw.work.ais.entity.dto.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * AI队列参数实体包含调用python服务的入参和构造ai_task_result表的入参
 */

@Data
public class CosQueueDTO {

    @Schema(description = "ai审核模型id")
    @ApiModelProperty(value = "ai审核模型id")
    private Long auditModelSceneId;

    @NotNull
    @Schema(description = "系统id ")
    private String systemId;

    @Schema(description = "被校验文件信息")
    private List<BeCheckFileDTO> contents;

    @Schema(description = "被校验文件信息")
    private List<BeCheckFileDTO> toPythonFileInfos;

    @NotEmpty
    @Schema(description = "校的字段和内容[  {{   \\\"isQualified\\\": false,    \\\"explain\\\": \\\"根据图片内容，该贷款申请未通过。因为在界面上显示着“很抱歉，您该笔借款未审批通过\\\"  }}]")
    private String givenInfoJson;

    @NotEmpty
    @Schema(description = "校验字段的自然文字描述 其中isQualified是一个true或者false的布尔值类型，描述放在explain里面")
    private String givenInfoJsonDesc;

    @Schema(description = "回调地址-暂时不用保留")
    private String callbackUrl;

    @Schema(description = "回调类型-暂时不用保留")
    private String callbackType;

    @NotEmpty
    @Schema(description = "线下约定 对方提供")
    private String taskType;

    @NotEmpty
    @Schema(description = "业务主键")
    private String bizId;

    @NotEmpty
    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "自定义参数")
    private String callBackCustomParam;

    @NotNull
    @Schema(description = "版本")
    private String version;

    @NotNull
    @Schema(description = "对方提供一个标识uuid，查日志用")
    private String traceId;

    @Schema(description = "一次AI审核的单据ID也叫批次Id，一个batchId对应多个traceId")
    private String batchId;
}
