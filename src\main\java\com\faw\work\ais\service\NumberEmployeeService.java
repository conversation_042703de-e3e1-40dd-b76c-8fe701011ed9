package com.faw.work.ais.service;

import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.dto.ai.ProcessInfoDTO;
import com.faw.work.ais.entity.vo.ai.*;

import java.util.List;

/**
 * 数字员工看板
 */
public interface NumberEmployeeService {

    /**
     * 数字员工看板
     * @param numberEmployeeDTO
     * @return
     */
    NumberEmployeeVO getInfo(NumberEmployeeDTO numberEmployeeDTO);

    /**
     * 数字员工看板-获取折线图信息
     * @param numberEmployeeDTO
     * @return
     */
    List<RateTrendChartVO> getTableLineInfo(NumberEmployeeDTO numberEmployeeDTO, List<String> bizTypes, String aiSaveFlag);

    /**
     * 获取l3流程信息
     * @return
     */
    List<FlowInfoVO> getL3FlowBusiness();

    /**
     * 获取处理人对应的业务单元信息
     * @return
     */
    List<BizUnitInfoVO> getBizUnitInfos();

    /**
     * 根据l3流程代码获取业务单元列表
     * @return
     */
    List<BizUnitInfoVO> getl3BizUnitInfos(ProcessInfoDTO processInfoDTO);

    /**
     * 获取项目下拉列表根据业务单元代码
     * @return
     */
    public List<SystemVO> getSystemInfoByUnitCode(ProcessInfoDTO processInfoDTO);

    /**
     * Ai覆盖角色数
     * @param
     */
    public String getAiCoverRoleNum();

    /**
     * Ai覆盖业务单元数
     * @param
     */
    public String getAiCoverBizNum();

    /**
     * Ai覆盖规则数
     * @param
     */
    public String getAiCoverRuleNum();
    public AiCoverDicVO getAiCoverRuleRate();
    /**
     * 数字员工看板-优化后
     * @param numberEmployeeDTO
     * @return
     */
    NumberEmployeeVO getInfoNew(NumberEmployeeDTO numberEmployeeDTO);

    /**
     * 更新数字员工看板AI覆盖规则数、AI覆盖业务单元数、AI覆盖角色数信息
     */
    void updateAiCoverInfo();
}
