package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 图片解析返回结果
 *
 * <AUTHOR>
 * @since 2025-04-22 9:56
 */
@Data
@Schema(description = "大模型响应")
public class AnalysisResponse {

    @Schema(description = "id")
    private String id;

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "响应信息")
    private List<ChoiceEntity> choices;

    @Schema(description = "实体")
    private String object;

    @Schema(description = "使用情况")
    private UsageEntity usage;

    @Schema(description = "创建时间")
    private Date created;

}
