package com.faw.work.ais.util;

import java.util.Collection;
import java.util.Objects;

/**
 * 对象验证工具类，提供参数和状态的检查方法。
 */
public final class ValidationUtils {

    /**
     * 私有构造函数，防止类被实例化。
     */
    private ValidationUtils() {
        // 私有构造函数，阻止实例化
    }

    /**
     * 检查对象是否为 null，如果是则抛出 IllegalArgumentException。
     *
     * @param object  要检查的对象。
     * @param message 异常信息。
     */
    public static void requireNonNull(Object object, String message) {
        if (object == null) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 检查字符串是否为 null 或空。
     *
     * @param s       要检查的字符串。
     * @param message 异常信息。
     */
    public static void requireNonEmpty(String s, String message) {
        if (s == null || s.trim().isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 检查集合是否为 null 或空。
     *
     * @param collection 要检查的集合。
     * @param message    异常信息。
     */
    public static void requireNonEmpty(Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 检查一个布尔表达式是否为 true。如果为 false，则抛出 IllegalArgumentException。
     *
     * @param expression 待验证的布尔表达式。
     * @param message    异常信息。
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new IllegalArgumentException(message);
        }
    }
}
