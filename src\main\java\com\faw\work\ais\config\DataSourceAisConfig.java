package com.faw.work.ais.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.logging.stdout.StdOutImpl;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.stream.Stream;


@Primary
@Configuration
@MapperScan(basePackages = "com.faw.work.ais.mapper.ais", sqlSessionFactoryRef = "aisSqlSessionFactory")
public class DataSourceAisConfig {

    @Bean(name = "aisDatasource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.ais")
    public DataSource aisDatasource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "aisSqlSessionFactory")
    @Primary
    public SqlSessionFactory aisSqlSessionFactory(@Qualifier("aisDatasource") DataSource dataSource)
            throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setCallSettersOnNulls(true);
        configuration.setLogImpl(StdOutImpl.class);
        bean.setDataSource(dataSource);
        Resource[] aisResources = new PathMatchingResourcePatternResolver().getResources("classpath:mapper/ais/**.xml");
        Resource[] chatResources = new PathMatchingResourcePatternResolver().getResources("classpath:mapper/chat/**.xml");
        Resource[] resources = Stream.concat(Arrays.stream(aisResources), Arrays.stream(chatResources))
                .toArray(Resource[]::new);
        bean.setMapperLocations(resources);
        bean.setPlugins(mybatisPlusInterceptor());
        return bean.getObject();
    }

    /**
     * 数据源事务配置
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "aisTransactionManager")
    @Primary
    public DataSourceTransactionManager aisTransactionManager(@Qualifier("aisDatasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "aisSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate aisSqlSessionTemplate(@Qualifier("aisSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(){
        MybatisPlusInterceptor interceptor=new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

}