<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.content.ContentRuleMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule, created_by, created_at, updated_by, updated_at
    </sql>
    <update id="disableOtherRules">
        UPDATE content_rule
        SET status = CASE
         WHEN id = #{id} THEN 1
         ELSE 0
        END
        WHERE type = #{type}
    </update>

    <!--    &lt;!&ndash; 分页查询机器人列表 &ndash;&gt;-->
    <!--    <select id="selectPageWithParams" resultType="com.faw.work.ais.entity.domain.FaqRobotPO">-->
    <!--        SELECT-->
    <!--        <include refid="Base_Column_List"/>-->
    <!--        FROM faq_robot-->
    <!--        <where>-->
    <!--            <if test="keyword != null and keyword != ''">-->
    <!--                AND robot_name LIKE CONCAT('%', #{keyword}, '%')-->
    <!--            </if>-->
    <!--            <if test="status != null">-->
    <!--                AND status = #{status}-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        ORDER BY updated_at DESC-->
    <!--    </select>-->

</mapper>