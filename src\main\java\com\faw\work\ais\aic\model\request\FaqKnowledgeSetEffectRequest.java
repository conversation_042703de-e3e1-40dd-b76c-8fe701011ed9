package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FAQ知识请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ知识请求对象")
public class FaqKnowledgeSetEffectRequest {
    @Schema(description = "FAQ知识ID")
    @NotNull(message = "FAQ知识ID不能为空")
    private String id;


    @Schema(description = "生效类型 00-永久有效 01-临时有效")
    private String effectiveType;
    @Schema(description = "生效开始时间 yyyy-MM-dd HH:mm:ss")
    private String effectiveStartTime;
    @Schema(description = "生效结束时间 yyyy-MM-dd HH:mm:ss")
    private String effectiveEndTime;


}
