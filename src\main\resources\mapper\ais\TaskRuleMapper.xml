<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.TaskRuleMapper">
  <resultMap id="BaseResultMap" type="com.faw.work.ais.model.TaskRule">
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="prompt" jdbcType="VARCHAR" property="prompt" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
    <result column="id" jdbcType="VARCHAR" property="id" />
  </resultMap>
  <insert id="insert" parameterType="com.faw.work.ais.model.TaskRule">
    insert into task_rule (task_type, prompt, desc, system_id, version, create_time)
    values (#{param.taskType}, #{param.prompt}, #{param.desc}, #{param.systemId}, #{param.version}, sysdate())
  </insert>
  <update id="update" parameterType="com.faw.work.ais.model.File">

  </update>

  <select id="getLastPromptBySystemId" resultType="com.faw.work.ais.model.TaskRule">
    SELECT
      t.prompt,
      t.version,
      t.system_id systemId
        FROM
      task_rule t
    <where>
      <if test="systemId !=null and systemId != ''">
        AND t.system_id = #{systemId}
      </if>
      <if test="taskType !=null and taskType != ''">
        AND t.task_type = #{taskType}
      </if>
    </where>
    ORDER BY t.version DESC
    LIMIT 1
  </select>
  <select id="getCallBackUrl" resultType="java.lang.String">
    SELECT
        t.call_back_url
    FROM system_call_back_url t
    WHERE
        t.system_id = #{systemId}
  </select>

  <select id="getTaskRule" resultType="com.faw.work.ais.entity.vo.ai.TaskRuleVO">
    SELECT
      t.task_type taskType,
      t.task_name taskName,
      t.desc,
      t.system_id systemId,
      t.version,
      t.create_time createTime,
      t.update_time updateTime
    FROM
      task_rule t
    ORDER BY
      t.create_time DESC
  </select>

  <select id="getTaskRuleBySystemId" resultType="com.faw.work.ais.entity.vo.ai.TaskRuleVO">
    SELECT
      t.task_type taskType,
      t.task_name taskName,
      t.desc,
      t.system_id systemId,
      t.version,
      t.create_time createTime,
      t.update_time updateTime
    FROM
      task_rule t
      where  t.system_id = #{systemId}
    ORDER BY
      t.create_time DESC
  </select>
</mapper>