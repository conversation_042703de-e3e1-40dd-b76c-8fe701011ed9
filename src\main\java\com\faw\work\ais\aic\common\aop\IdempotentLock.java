package com.faw.work.ais.aic.common.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 幂等锁
 *  示例：
 *     1. @IdempotentLock(expression = "#request.policyId", duration = 5L)，reuqest为请求参数对象，policyId为请求参数的属性名
 *     2. @IdempotentLock(duration = 5L)，默认表达式为"#request.toString()"，request为请求参数对象
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface IdempotentLock {


    /**
     * 锁的前缀
     * @return {@link String}
     */
    String prefix() default "";

    /**
     * 锁的名字
     * @return {@link String}
     */
    String lockName() default "";

    /**
     * 允许两次请求的时间间隔(默认30秒),如果设置，建议大于接口的执行时间
     * 单位：秒
     * @return long
     */
    long duration() default 30L;


    /**
     * 未抢到锁的提示信息
     * @return {@link String}
     */
    String description() default "操作过于频繁";

    /**
     * SPEL表达式
     *
     * @return {@link String }
     */
    String expression() default "";
}
