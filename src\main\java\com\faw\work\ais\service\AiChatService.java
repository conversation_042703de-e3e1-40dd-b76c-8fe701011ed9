package com.faw.work.ais.service;

import com.dcp.common.rest.Result;
import com.faw.work.ais.common.dto.chat.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 灵小犀模型 服务接口
 *
 * <AUTHOR>
 * @since 2025-04-03 15:12
 */
public interface AiChatService {

    /**
     * 连续会话（流式对象）
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Flux<AiChatResponse> continuousSession(AiChatRequest request);

    /**
     * 语音转文字
     *
     * @param file 语音文件
     * @return 文字
     */
    Result<VoiceToTextEntity> voiceToText(MultipartFile file);

    /**
     * 语音转文字纠正
     *
     * @param file 语音文件
     * @return 文字
     */
    String voiceToTextRectify(MultipartFile file);

    /**
     * 保存会话记录
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<AppChatHistoryDto> saveSessionRecord(AppChatHistoryDto request);

    /**
     * 查询会话记录
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<List<AppChatHistoryDto>> querySessionRecord(AppChatHistoryDto request);

    /**
     * 查询会话记录详情
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<List<AppChatHistoryDetailDto>> querySessionDetail(AppChatHistoryDto request);

    /**
     * 删除会话记录
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<String> deleteSessionRecord(AppChatHistoryDto request);

}
