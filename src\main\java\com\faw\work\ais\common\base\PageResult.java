package com.faw.work.ais.common.base;

import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:分页结果
 * <AUTHOR>
 * @date 2022年08月31日 12:40
 */

@Tag(name = "分页返回结果")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResult<T> {

    @Schema(name = "总数量")
    private Long totalNum;

    @Schema(name = "有下一页")
    private Boolean hasNextPage;

    @Schema(name = "当前页")
    private Integer currentPage;

    @Schema(name = "页面大小")
    private Integer pageSize;

    @Schema(name = "数据")
    private List<T> data;

    public static <T, R> PageResult<R> from(PageInfo<T> pageInfo, Function<T, R> function) {
        List<T> list = pageInfo.getList();

        return PageResult.<R>builder()
                .data(list.stream().map(function).collect(Collectors.toList()))
                .totalNum(pageInfo.getTotal())
                .build();
    }

    public static <T> PageResult<T> from(PageInfo<T> pageInfo) {

        return PageResult.<T>builder()
                .data(pageInfo.getList())
                .totalNum(pageInfo.getTotal())
                .build();
    }

}
