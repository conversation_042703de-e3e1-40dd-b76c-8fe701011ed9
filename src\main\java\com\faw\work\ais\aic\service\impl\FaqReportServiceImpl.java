package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper;
import com.faw.work.ais.aic.mapper.faq.FaqKnowledgeMapper;
import com.faw.work.ais.aic.mapper.faq.FaqRobotKnowledgeJoinsMapper;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.request.FaqReportQueryRequest;
import com.faw.work.ais.aic.model.response.FaqReportResponse;
import com.faw.work.ais.aic.service.FaqReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

/**
 * FAQ运营报表服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FaqReportServiceImpl implements FaqReportService {

    private final FaqHitLogMapper faqHitLogMapper;
    private final FaqKnowledgeMapper faqKnowledgeMapper;
    private final FaqRobotKnowledgeJoinsMapper faqRobotKnowledgeJoinsMapper;

    @Override
    public FaqReportResponse getReport(FaqReportQueryRequest request) {
        String startTime = request.getStartTime();
        String endTime = request.getEndTime();
        String robotId = request.getRobotId();
        String env = request.getEnv();

        FaqReportResponse response = new FaqReportResponse();

        // 1. & 2. 知识总数 和 有效知识命中占比
        calculateKnowledgeStats(response, robotId, env, startTime, endTime);

        // 3. 知识命中占比
        calculateHitRate(response, robotId, env, startTime, endTime);

        // 4. 热门榜-TOP10
        response.setHotList(faqHitLogMapper.findTop10HitKnowledge(robotId, env, startTime, endTime));

        // 5. 冷门榜-无访问知识
        response.setColdList(findColdKnowledge(robotId, env, startTime, endTime));

        return response;
    }

    private void calculateKnowledgeStats(FaqReportResponse response, String robotId, String env, String startTime, String endTime) {
        // 获取绑定的原始知识ID列表（只统计原始知识，不包含相似知识）
        List<String> robotKnowledgeIds = faqRobotKnowledgeJoinsMapper.findKnowledgeIdsByRobotId(robotId, env);

        if (CollUtil.isEmpty(robotKnowledgeIds)) {
            response.setTotalKnowledgeCount(0L);
            response.setEffectiveKnowledgeHitRate("0.00%");
            return;
        }

        // 修复：根据环境查询有效知识总数
        long totalKnowledgeCount = faqKnowledgeMapper.countEffectiveKnowledgeForRobot(robotKnowledgeIds, endTime, env);
        response.setTotalKnowledgeCount(totalKnowledgeCount);

        if (totalKnowledgeCount == 0) {
            response.setEffectiveKnowledgeHitRate("0.00%");
            return;
        }

        // 修复：只统计原始知识的命中，不包含相似知识
        Long hitEffectiveKnowledgeCount = faqHitLogMapper.countHitEffectiveOriginalKnowledge(robotId, env, startTime, endTime);
        if (hitEffectiveKnowledgeCount == null) {
            hitEffectiveKnowledgeCount = 0L;
        }

        BigDecimal rate = BigDecimal.valueOf(hitEffectiveKnowledgeCount)
                .divide(BigDecimal.valueOf(totalKnowledgeCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        response.setEffectiveKnowledgeHitRate(String.format("%.2f%%", rate));
    }

    private void calculateHitRate(FaqReportResponse response, String robotId, String env, String startTime, String endTime) {
        Long hitChatCount = faqHitLogMapper.countTotalChat(robotId, env, startTime, endTime, "1");
        Long noHitChatCount = faqHitLogMapper.countTotalChat(robotId, env, startTime, endTime, "0");

        // 修复：空值处理
        if (hitChatCount == null) {
            hitChatCount = 0L;
        }
        if (noHitChatCount == null) {
            noHitChatCount = 0L;
        }

        long totalChat = hitChatCount + noHitChatCount;

        if (totalChat == 0) {
            response.setKnowledgeHitSessionRate("0.00%");
            return;
        }

        BigDecimal rate = BigDecimal.valueOf(hitChatCount)
                .divide(BigDecimal.valueOf(totalChat), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        response.setKnowledgeHitSessionRate(String.format("%.2f%%", rate));
    }

    private List<FaqKnowledgePO> findColdKnowledge(String robotId, String env, String startTime, String endTime) {
        // 获取机器人绑定的原始知识ID列表
        List<String> robotKnowledgeIds = faqRobotKnowledgeJoinsMapper.findKnowledgeIdsByRobotId(robotId, env);
        if (CollUtil.isEmpty(robotKnowledgeIds)) {
            return Collections.emptyList();
        }

        // 修复：查询命中的原始知识ID（不包含相似知识）
        List<String> hitKnowledgeIds = faqHitLogMapper.findHitOriginalKnowledgeIdsInDateRange(robotId, env, startTime, endTime);

        // 找出未被命中的原始知识ID，
        List<String> coldKnowledgeIds = robotKnowledgeIds.stream()
                .filter(id -> !hitKnowledgeIds.contains(id))
                // .limit(10)
                .toList();

        if (CollUtil.isEmpty(coldKnowledgeIds)) {
            return Collections.emptyList();
        }

        // 修复：根据环境查询知识详情
        return faqKnowledgeMapper.selectBatchIdsByEnv(coldKnowledgeIds, env);
    }
}