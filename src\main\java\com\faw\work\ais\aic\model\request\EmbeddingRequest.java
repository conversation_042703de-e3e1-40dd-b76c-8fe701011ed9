package com.faw.work.ais.aic.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文本嵌入请求实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingRequest {
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 输入文本
     */
    private String input;
    
    /**
     * 编码格式
     */
    private String encoding_format;
} 