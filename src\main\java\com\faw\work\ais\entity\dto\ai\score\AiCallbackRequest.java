package com.faw.work.ais.entity.dto.ai.score;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * AI回调入参
 */
@Schema(description = "大模型回调入参")
@Data
public class AiCallbackRequest {

    @NotBlank(message = "systemId不能为空")
    @Schema(description = "业务系统主键")
    private String systemId;

    /**
     * 大客户报备主键ID
     * bizId = taskId
     */
    @NotBlank(message = "bizId不能为空")
    @Schema(description = "业务主键")
    private String bizId;

    /**
     * 业务类型
     * bizType = fileId
     */
    @NotBlank(message = "bizType不能为空")
    @Schema(description = "业务类型")
    private String bizType;

    /**
     * 自定义参数
     */
    @Schema(description = "自定义参数")
    private String callbackCustomParam;

    /**
     * 描述
     */
    @Schema(description = "评分描述")
    private String message;

    /**
     * 约定的返回格式如下
     * 成功：{“success”: 1,“failReason”: null}
     * 失败：{“success”: 0,“failReason”: "失败原因"}
     */
    @NotBlank(message = "taskResult不能为空")
    @Schema(description = "AI任务执行结果")
    private String taskResult;

    /**
     * 解析成功后的taskResult参数
     */
    @Schema(description = "解析成功后的taskResult参数")
    private AiTaskResultDTO aiTaskResultDTO;
}
