package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * FAQ知识列表查询响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ知识列表查询响应")
public class FaqKnowledgeListResponse {

    @Schema(description = "知识ID")
    private String knowledgeId;

    @Schema(description = "知识名称")
    private String knowledgeName;

    @Schema(description = "类目名称")
    private String categoryName;

    @Schema(description = "命中次数")
    private Long hitCount;
} 