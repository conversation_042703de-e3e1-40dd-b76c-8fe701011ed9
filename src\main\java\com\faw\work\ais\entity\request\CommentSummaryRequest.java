package com.faw.work.ais.entity.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 评论请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "评论评分请求对象")
public class CommentSummaryRequest {


    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "主题内容不能为空")
    @Schema(description = "主题内容")
    private String topic;

    @NotBlank(message = "评论内容不能为空")
    @Schema(description = "评论内容")
    private String content;

    @NotBlank(message = "标签不能为空")
    @Schema(description = "标签")
    private String tag;

}
