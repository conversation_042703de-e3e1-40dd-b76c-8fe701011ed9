package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * FAQ命中日志详情响应对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "FAQ命中日志详情响应对象")
public class FaqHitLogDetailResponse {

    @Schema(description = "标准明细ID")
    private String detailId;

    @Schema(description = "机器人ID")
    private String robotId;

    @Schema(description = "知识ID")
    private String knowledgeId;

    @Schema(description = "命中时间")
    private LocalDateTime hitTime;

    @Schema(description = "环境：prod-正式环境, test-测试环境")
    private String environment;

    @Schema(description = "对话ID")
    private String chatId;

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "是否有效知识")
    private Boolean isEffective;

    @Schema(description = "是否命中")
    private Boolean isHit;

    @Schema(description = "用户问法文本")
    private String userQuestion;

    @Schema(description = "匹配到的答案内容")
    private String matchedAnswer;

    @Schema(description = "匹配度分数")
    private BigDecimal matchScore;

    @Schema(description = "匹配到的FAQ标题")
    private String faqTitle;

    @Schema(description = "关联的标注任务ID")
    private String annotationTaskId;

    @Schema(description = "标注类型：correct-正确，error-错误，uncovered-未覆盖，invalid-无效，pending-待定")
    private String annotationType;

    @Schema(description = "标注子类型")
    private String annotationSubtype;

    @Schema(description = "是否锁定（已标注）")
    private Boolean isLocked;

    @Schema(description = "标注人ID")
    private String annotatorId;

    @Schema(description = "标注人姓名")
    private String annotatorName;

    @Schema(description = "标注时间")
    private LocalDateTime annotatedAt;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "匹配类型：无答案-no_answer，有答案-has_answer")
    private String matchType;
}
