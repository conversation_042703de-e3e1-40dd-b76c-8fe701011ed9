package com.faw.work.ais.message;

import com.faw.work.ais.config.RabbitMQConfig;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RabbitService {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    RabbitMQConfig rabbitMQConfig;

    /**
     * 初始消息队列
     * @param object
     */
    public void convertAndSend(Object object) {
        rabbitTemplate.convertAndSend(rabbitMQConfig.getExchangeName(), rabbitMQConfig.getRoutingKey(), object);
    }

    /**
     * 异常消息队列
     * @param object
     */
    public void convertAndSendError(Object object) {
        rabbitTemplate.convertAndSend(rabbitMQConfig.getExchangeName(), rabbitMQConfig.getErrorRouting(), object);
    }

    /**
     * 补能中心（国补）消息队列
     * @param object
     */
    public void convertAndSendBnzx(Object object) {
        rabbitTemplate.convertAndSend(rabbitMQConfig.getExchangeName(), rabbitMQConfig.getBnzxRouting(), object);
    }

}
