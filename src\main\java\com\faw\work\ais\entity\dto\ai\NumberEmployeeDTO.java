package com.faw.work.ais.entity.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "数字员工看板入参")
@Data
public class NumberEmployeeDTO {

    @Schema(description = "AI审核日期开始时间")
    private String aiCheckDateStart;

    @Schema(description = "AI审核日期结束时间")
    private String aiCheckDateEnd;

    @Schema(description = "L3流程")
    private String l3Flow;

    @Schema(description = "业务单元")
    private String bizType;

    @Schema(description = "项目(系统id)")
    private String systemId;

    @NotNull
    @Schema(description = "AI触发任务数标识；1-选中；0-未选中")
    private String aiTriggerTaskFlag;

    @NotNull
    @Schema(description = "抽检任务数标识(单据)；1-选中；0-未选中")
    private String sampleTaskFlag;

    @NotNull
    @Schema(description = "抽检任务数标识（规则）；1-选中；0-未选中")
    private String sampleTaskRuleFlag;

    @NotNull
    @Schema(description = "AI节省信息标识；1-选中；0-未选中")
    private String aiSaveFlag;

    @Schema(description = "统计类型；1-日；2-周；3-月")
    private String totalType;

    @Schema(description = "业务单元列表-（根据选中的l3流程和业务单元计算出来的最终业务单元，统计时都使用这个字段）")
    private List<String> bizTypes;

}
