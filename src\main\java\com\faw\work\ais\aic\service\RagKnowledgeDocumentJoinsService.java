package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.RagKnowledgeDocumentJoinsPO;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentJoinsPageRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentBindRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentUnbindRequest;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentBindResponse;

import java.util.List;

/**
 * 知识库文档关联表 服务接口
 *
 * <AUTHOR> Assistant
 */
public interface RagKnowledgeDocumentJoinsService extends IService<RagKnowledgeDocumentJoinsPO> {

    /**
     * 根据条件查询关联列表
     *
     * @param joins 查询条件
     * @return 关联列表
     */
    List<RagKnowledgeDocumentJoinsPO> getJoinsList(RagKnowledgeDocumentJoinsPO joins);
    
    /**
     * 分页查询关联
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    Page<RagKnowledgeDocumentJoinsPO> getJoinsPage(RagKnowledgeDocumentJoinsPageRequest request);
    
    /**
     * 根据知识库ID查询关联列表
     *
     * @param baseId 知识库ID
     * @return 关联列表
     */
    List<RagKnowledgeDocumentJoinsPO> getByBaseId(Long baseId);
    
    /**
     * 根据文档ID查询关联列表
     *
     * @param documentId 文档ID
     * @return 关联列表
     */
    List<RagKnowledgeDocumentJoinsPO> getByDocumentId(Long documentId);
    
    /**
     * 查询知识库是否与文档关联
     *
     * @param baseId 知识库ID
     * @param documentId 文档ID
     * @return 是否存在关联
     */
    boolean existsJoin(Long baseId, Long documentId);

    /**
     * 批量绑定知识库和文档关系
     *
     * @param request 绑定请求
     * @return 绑定结果
     */
    RagKnowledgeDocumentBindResponse bindDocuments(RagKnowledgeDocumentBindRequest request);

    /**
     * 批量解绑知识库和文档关系
     *
     * @param request 解绑请求
     * @return 解绑结果
     */
    RagKnowledgeDocumentBindResponse unbindDocuments(RagKnowledgeDocumentUnbindRequest request);

    /**
     * 根据知识库ID删除所有关联关系
     *
     * @param ragKnowledgeId 知识库ID
     * @return 删除数量
     */
    int deleteByKnowledgeId(Long ragKnowledgeId);

    /**
     * 根据文档ID删除所有关联关系
     *
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteByDocumentId(Long documentId);
}