package com.faw.work.ais.entity.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数字员工看板-AI覆盖规则数")
@Data
public class NumberEmployeeAiCoverInfoDTO {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "AI覆盖角色数")
    private Integer aiCoverRoleNum;

    @Schema(description = "AI覆盖业务单元数")
    private Integer aiCoverBizNum;

    @Schema(description = "AI覆盖规则数")
    private Integer aiCoverRuleNum;

    @Schema(description = "处理人")
    private String dealName;
}
