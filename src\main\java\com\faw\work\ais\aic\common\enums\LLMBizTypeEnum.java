package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大模型业务类型枚举类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LLMBizTypeEnum {
    /**
     * 售后接待-情绪价值模型
     */
    DMS_AFTER_EMOTION("dms_after_emotion", "售后接待-情绪价值模型"),
    /**
     * 情绪识别模型
     */
    DMS_EMOTION("dms_emotion", "情绪识别模型"),
    /**
     * 情绪识别模型
     */
    DMS_PRODUCT("dms_product", "产品需求模型");

    private final String code;
    private final String desc;

}