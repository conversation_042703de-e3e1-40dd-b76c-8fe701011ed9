package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * FAQ知识复制请求对象
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ知识复制请求对象")
public class FaqKnowledgeCopyRequest {

    /**
     * 源类目ID集合
     */
    @NotEmpty(message = "源类目ID集合不能为空")
    @Schema(description = "源类目ID集合，需要复制的知识所在的类目ID列表")
    private List<String> sourceCategoryIds;

    /**
     * 目标类目ID
     */
    @NotBlank(message = "目标类目ID不能为空")
    @Schema(description = "目标类目ID，知识复制到的目标类目")
    private String targetCategoryId;
}
