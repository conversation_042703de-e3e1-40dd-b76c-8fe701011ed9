package com.faw.work.ais.aic.feign.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DgwResult {
    private Integer resultCode;
    private String errMsg;
    private Integer elapsedMilliseconds;
    private Object data;
    private Boolean success;
    private Map<String, Object> additionalProperties;
}
