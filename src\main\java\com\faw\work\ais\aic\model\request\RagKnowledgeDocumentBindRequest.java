package com.faw.work.ais.aic.model.request;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 知识库文档绑定请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库文档绑定请求")
public class RagKnowledgeDocumentBindRequest {

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long ragKnowledgeId;

    @NotEmpty(message = "文档ID列表不能为空")
    @Schema(description = "文档ID列表", required = true)
    private List<Long> documentIds;
}
