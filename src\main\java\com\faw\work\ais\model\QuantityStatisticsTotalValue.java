package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* 数量统计表
* Created  by Mr.hp
* DateTime on 2025-01-13 14:12:35
* <AUTHOR> Mr.hp
* @ApiModel(value = "QuantityStatisticsTotalValue", description = "数量统计表")
*/
@Data
@NoArgsConstructor
@Schema(description = "数量统计TotalValue")
public class QuantityStatisticsTotalValue implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
     * 系统ID
     * AI审核任务通过率趋势
     */
    @Schema(description = "审核任务通过率")
    private BigDecimal aiAuditTaskPassRate;
    /**
     * 系统ID
     * AI审核任务通过值
     */
    @Schema(description = "审核任务通过值")
    private BigDecimal aiAuditTaskPass;
    /**
     * 系统ID
     * AI审核任务通过值
     */
    @Schema(description = "审核任务总数")
    private BigDecimal aiAuditTaskTotal;


    /**
     * 系统ID
     * AI审核任务通过率趋势
     */
    @Schema(description = "批次-审核任务通过率")
    private BigDecimal aiBatchAuditTaskPassRate;
    /**
     * 系统ID
     * AI审核任务通过值
     */
    @Schema(description = "批次-审核任务通过值")
    private BigDecimal aiBatchAuditTaskPass;
    /**
     * 系统ID
     * AI审核任务通过值
     */
    @Schema(description = "批次-审核任务总数")
    private BigDecimal aiBatchAuditTaskTotal;


    /**
     * 系统ID
     * 人工复审材料准确率
     */
    @Schema(description = "人工复审材料准确率")
    private BigDecimal manualAccuracyRate;
    /**
     * 系统ID
     * 人工复审材料准确率
     */
    @Schema(description = "人工复审材料准确值")
    private BigDecimal manualAccuracy;
    /**
     * 系统ID
     * 人工复审材料准确率
     */
    @Schema(description = "人工复审材料准确总数")
    private BigDecimal manualAccuracyTotal;


}
