package com.faw.work.ais.aic.model.request;

import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Milvus集合初始化请求参数
 * <AUTHOR>
 */
@Data
public class MilvusCollectionRequest {

    String databaseName;
    /**
     * 集合名称
     */
    private String collectionName;

    /**
     * 集合描述
     */
    private String description = "知识库向量集合";

    /**
     * ID字段配置
     */
    private IdFieldConfig idField = new IdFieldConfig();

    /**
     * 向量字段配置
     */
    private VectorFieldConfig vectorField = new VectorFieldConfig();

    /**
     * 标量字段列表配置
     */
    private List<ScalarFieldConfig> scalarFields = new ArrayList<>();

    /**
     * ID字段配置
     */
    @Data
    public static class IdFieldConfig {
        /**
         * ID字段名称
         */
        private String name = "id";

        /**
         * ID字段数据类型
         */
        private DataType dataType = DataType.Int64;

        /**
         * 是否自动生成ID
         */
        private Boolean autoId = true;
    }

    /**
     * 向量字段配置
     */
    @Data
    public static class VectorFieldConfig {
        /**
         * 向量字段名称
         */
        private String name = "vector";

        /**
         * 向量维度
         */
        private Integer dimension = 1024;

        /**
         * 索引类型
         */
        private IndexParam.IndexType indexType = IndexParam.IndexType.AUTOINDEX;

        /**
         * 度量类型
         */
        private IndexParam.MetricType metricType = IndexParam.MetricType.COSINE;
    }

    /**
     * 标量字段配置
     */
    @Data
    public static class ScalarFieldConfig {
        /**
         * 字段名称
         */
        private String name;

        /**
         * 字段数据类型
         */
        private DataType dataType = DataType.VarChar;

        /**
         * 字段默认值
         */
        private String defaultValue;
        
        /**
         * 是否创建索引
         */
        private Boolean createIndex = false;
        
        /**
         * 字段最大长度（针对VarChar类型）
         */
        private Integer maxLength = 65535;
    }
} 