package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.RagDocumentExcelInitResponse;
import com.faw.work.ais.aic.model.response.RagDocumentProcessAllResponse;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentBindResponse;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.aic.service.RagKnowledgeDocumentJoinsService;
import com.faw.work.ais.aic.service.RagKnowledgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 文档表 控制器
 *
 * <AUTHOR> Assistant
 */
@Tag(name = "文档管理", description = "RAG文档管理相关接口")
@RestController
@Slf4j
@RequestMapping("/rag-document")
public class RagDocumentController {

    @Autowired
    private RagDocumentService ragDocumentService;

    @Autowired
    private RagKnowledgeDocumentJoinsService ragKnowledgeDocumentJoinsService;

    @Autowired
    private RagKnowledgeService ragKnowledgeService;

    @Operation(summary = "一.上传知识文档", description = "[author:10200571]")
    @PostMapping("/upload")
    public AiResult<RagDocumentPO> upload(@Valid @ModelAttribute RagDocumentAddRequest request) {
        RagDocumentPO document = ragDocumentService.upload(request);
        return AiResult.success(document);
    }


    @Operation(summary = "下载文档", description = "[author:10200571]")
    @GetMapping("/download/{documentId}")
    public void download(@PathVariable Long documentId, HttpServletResponse response) {
        log.info("接收到文档下载请求: documentId={}", documentId);
        ragDocumentService.download(documentId, response);

    }

    @Operation(summary = "二.文档切分", description = "[author:10200571]")
    @PostMapping("/splitDocument")
    public AiResult<String> splitDocument(@RequestParam("documentId") Long documentId) {
        ragDocumentService.splitDocument(documentId);
        return AiResult.success("文档切分处理成功");
    }


    @Operation(summary = "三.绑定知识库和文档关系", description = "[author:10200571]")
    @PostMapping("/bind-documents")
    public AiResult<RagKnowledgeDocumentBindResponse> bindDocuments(@Valid @RequestBody RagKnowledgeDocumentBindRequest request) {
        log.info("绑定知识库文档关系请求: {}", request);

        try {
            RagKnowledgeDocumentBindResponse response = ragKnowledgeDocumentJoinsService.bindDocuments(request);

            if (response.getFailCount() > 0) {
                log.warn("部分文档绑定失败: 成功={}, 失败={}", response.getSuccessCount(), response.getFailCount());
            }

            return AiResult.success(response);

        } catch (Exception e) {
            log.error("绑定知识库文档关系失败", e);
            return AiResult.fail("绑定失败: " + e.getMessage());
        }
    }


    @Operation(summary = "四.向量化文档片段", description = "[author:10200571]")
    @PostMapping("/vectorDocumentSplits")
    public AiResult<String> vectorDocumentSplits(@RequestParam("documentId") Long documentId) {
        boolean data = ragDocumentService.vectorDocumentSplits(documentId);
        if (data) {
            return AiResult.success("向量化文档片段成功");
        }
        return AiResult.fail("向量化文档片段失败");
    }

    @Operation(summary = "单纯删除文档向量索引", description = "[author:10200571]")
    @PostMapping("/deleteVectors")
    public AiResult<Long> deleteVectors(@RequestParam("documentId") Long documentId) {
        return AiResult.success(ragDocumentService.deleteDocumentVectors(documentId));
    }

    @Operation(summary = "删除文档切片信息", description = "[author:10200571]")
    @PostMapping("/deleteSplits")
    public AiResult<Boolean> deleteSplits(@RequestParam("documentId") Long documentId) {
        return AiResult.success(ragDocumentService.deleteDocumentSplits(documentId));
    }

    @Operation(summary = "养狗-根据类目ID删除所有相关文档，同时删除向量", description = "[author:10200571]")
    @PostMapping("/delete-by-category")
    public AiResult<String> deleteDocumentsByCategoryId(@RequestParam("categoryId") Long categoryId) {
        log.info("接收到根据类目ID删除文档请求: categoryId={}", categoryId);
        String result = ragDocumentService.deleteDocumentsByCategoryId(categoryId);
        return AiResult.success(result);
    }


    @Operation(summary = "核心接口:召回非结构化文档相似内容-新版本", description = "[author:10200571]")
    @PostMapping("/search-similar-content-new")
    public AiResult<List<SimilarContentSearchResponse>> searchSimilarContentNew(@RequestBody SearchContentRequest request) {
        List<SimilarContentSearchResponse> results = ragDocumentService.searchSimilarContentNew(request);
        return AiResult.success(results);
    }


    @Operation(summary = "非结构化文档一体化处理（基于URL：保存文档+分片+绑定知识库+向量化）", description = "[author:10200571]")
    @PostMapping("/process-all")
    @Deprecated
    public AiResult<List<RagDocumentProcessAllResponse>> processDocumentAll(@Valid @RequestBody List<RagDocumentProcessByUrlRequest> requestList) {
        List<RagDocumentProcessAllResponse> response = new ArrayList<>();
        for (RagDocumentProcessByUrlRequest request : requestList) {
            log.info("接收到基于URL的文档一体化处理请求: categoryId={}, ragKnowledgeId={}, fileUrl={}",
                    request.getCategoryId(), request.getRagKnowledgeId(), request.getFileUrl());

            RagDocumentProcessAllResponse temp = ragDocumentService.processDocumentByUrl(request);
            response.add(temp);
        }

        return AiResult.success(response);
    }




    @Operation(summary = "旗宝-同步知识中心文档（获取文档列表+过滤新文档+批量处理）", description = "[author:10200571]")
    @PostMapping("/sync-knowledge-documents")
    public AiResult<String> syncKnowledgeDocuments(@Valid @RequestBody SyncKnowledgeDocumentsRequest request) {
        String response = ragDocumentService.syncKnowledgeDocuments(request);
        return AiResult.success(response);
    }


    @Operation(summary = "旗宝-处理同步的文档（解析、切分、绑定、向量化）", description = "[author:10200571]")
    @PostMapping("/process-synced-documents")
    public AiResult<String> processSyncedDocuments(@Valid @RequestBody ProcessSyncedDocumentsRequest request) {
        log.info("开始处理同步的文档: documentIds={}", request.getCategoryIds());
        String result = ragDocumentService.processSyncedDocuments(request);
        return AiResult.success(result);

    }

    @Operation(summary = "Excel数据初始化到非结构化文档", description = "[author:10200571]")
    @PostMapping("/excel-init")
    public AiResult<RagDocumentExcelInitResponse> excelInit(@Valid @ModelAttribute RagDocumentExcelInitRequest request) {
        log.info("接收到Excel数据初始化请求: categoryId={}, ragKnowledgeId={}, fileName={}",
                request.getCategoryId(), request.getRagKnowledgeId(), request.getFile().getOriginalFilename());

        // 先删除该类别下所有文档
        ragDocumentService.deleteDocumentsByCategoryId(request.getCategoryId());

        // 再初始化文档
        RagDocumentExcelInitResponse response = ragDocumentService.initExcelToDocument(request);
        return AiResult.success(response);
    }

}
