package com.faw.work.ais.service.impl;

//import com.alibaba.cloud.commons.lang.StringUtils;
//import com.alibaba.nacos.common.codec.Base64;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.faw.work.ais.service.DingRobotService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.Arrays;

@Service
@Log4j2
public class DingRobotServiceImpl implements DingRobotService {

    @Override
    public Boolean pushRobotMsg(String robotToken, String userIds, String secret, String message, Boolean atFlag) {
        try {
            // 验签
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            log.info("dingTalk robot sign:{}", sign);

            //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send" + "?sign=" + sign + "&timestamp=" + timestamp);
            OapiRobotSendRequest req = new OapiRobotSendRequest();

            //定义文本内容
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            text.setContent(message);

            //定义 @ 对象
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setIsAtAll(atFlag);
            if (!StringUtils.isEmpty(userIds)) {
                at.setAtUserIds(Arrays.asList(userIds.split(",")));
            }

            //设置消息类型
            req.setMsgtype("text");
            req.setText(text);
            req.setAt(at);
            OapiRobotSendResponse rsp = client.execute(req, robotToken);
            log.info("dingTalk robot response:{}", rsp.getBody());
            return rsp.isSuccess();
        } catch (Exception e) {
            log.error("dingTalk robot response error!", e);
            return Boolean.FALSE;
        }
    }
}
