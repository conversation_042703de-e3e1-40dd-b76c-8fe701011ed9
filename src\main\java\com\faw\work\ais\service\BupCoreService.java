package com.faw.work.ais.service;

import com.faw.work.ais.entity.vo.ai.BizUnitInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowVO;

import java.util.List;

/**
 * 数字员工看板
 */
public interface BupCoreService {

    /**
     * 获取流程列表
     * @return 返回列表
     */
    FlowVO getFlowList();

    /**
     * 获取业务单元列表
     * @param req 请求对象
     * @return 返回列表
     */
    List<BizUnitInfoVO> getUnitList(FlowInfoVO req);
}
