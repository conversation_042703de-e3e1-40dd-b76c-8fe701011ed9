package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;

/**
 * 消息队列清理响应
 *
 * <AUTHOR>
 */
@Data
@Builder
@Schema(description = "消息队列清理响应")
public class MessageQueueCleanResponse {
    
    @Schema(description = "清理的消息数量")
    private Integer cleanCount;
    
    @Schema(description = "处理的请求ID列表")
    private java.util.List<String> requestIds;
} 