package com.faw.work.ais.service;

import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.entity.dto.ContentReviewResult;
import com.faw.work.ais.entity.request.CommentSummaryRequest;
import com.faw.work.ais.entity.request.PostSummaryRequest;

import java.util.List;

public interface ContentLLMService {

    /**
     * 评论评分
     *
     * @param topic 主题
     * @param content 评论内容
     * @return 分析结果
     */
    String commentScore(String topic, String content, List<ContentReviewResult> orginalRules);
    /**
     * 帖子评分
     *
     * @param topic 主题
     * @param content 帖子内容
     * @param photoUrls 图片URL列表
     * @return 分析结果
     */
    String postScore(String topic, String content, List<String> photoUrls, Boolean flag, List<ContentReviewResult> rules);

    /**
     * 评论总结
     *
     * @param requests
     * @return 分析结果
     */
    String commentSummary(List<CommentSummaryRequest> requests, String commentRule);

    String postSummary(List<PostSummaryRequest> requests, String postRule);


    String truthValidate(String content, String query);

    String extractVehicleParam(String content);
}
