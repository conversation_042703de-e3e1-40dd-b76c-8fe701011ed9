package com.faw.work.ais.mapper.ais;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.AiScene;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* ai场景配置表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 10:57:36
*/
@InterceptorIgnore(tenantLine = "true")
public interface AiSceneDao {

    /**
    * 新增
    */
    public int insert(@Param("aiScene") AiScene aiScene);

    /**
    * 删除
    */
    public int delete(@Param("id") Long id);

    /**
    * 修改
    */
    public int update(@Param("aiScene") AiScene aiScene);


    /**
    * 根据id查询 getAiSceneById
    */
    public AiScene getAiSceneById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<AiScene> getAiSceneList(@Param("aiScene")AiScene aiScene);
    public List<AiScene> getExitCodeAiSceneList(@Param("aiScene")AiScene aiScene);

    public List<AiScene> getExitNameAiSceneList(@Param("aiScene")AiScene aiScene);

    AiScene getAiSceneByCode(@Param("sceneCode") String sceneCode);
}

