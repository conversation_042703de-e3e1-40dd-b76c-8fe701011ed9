package com.faw.work.ais.common.enums;

/*
bpm审批结束节点回调，审批状态常量值
*/

/**
 * <AUTHOR>
 * @date 2024/01/04
 */
public enum WorkFlowEnum {
    /**
     * 流程结束时间码
     * */
    PROCESS_END_STATUS("end"),
    /**
     * 流程驳回
     * */
    REJECT_STATUS("REJECTTOSTART"),
    /**
     * 流程通过，bpm没有这个状态码，通过的时候返回的是null，这个是为了方便处理逻辑自己自定义的
     * */
    PASS_STATUS("pass"),
    /**
     * 流程撤回
     * */
    WITHDRAW_STATUS("withdraw"),

    /**
     * 流程删除
     * */
    DELETE_STATUS("delete");

    WorkFlowEnum( String statusDesc) {
        this.status = statusDesc;
    }

    private final String status;

    public String getStatus() {
        return status;
    }
}
