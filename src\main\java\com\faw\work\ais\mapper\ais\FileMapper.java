package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.vo.ai.BeCheckFileInfoVO;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.faw.work.ais.model.File;

import java.math.BigInteger;
import java.util.List;

@Mapper
public interface FileMapper {

    int insert (@Param("param") File file);

    int update (@Param("param") File file);

    /**
     * 检查md5文件是否在数据库中存在
     * @param key
     * @return
     */
    int ifKeyExists(@Param("key") String key);

    com.faw.work.ais.model.File getFileInfo(@Param("param") com.faw.work.ais.model.File file);
    com.faw.work.ais.model.File getFileOldInfo(@Param("param") com.faw.work.ais.model.File file);

}
