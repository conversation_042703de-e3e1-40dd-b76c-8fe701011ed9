<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqAnnotationStatisticsMapper">

    <!-- 根据任务ID查询统计信息 -->
    <select id="findByTaskId" resultType="com.faw.work.ais.aic.model.domain.FaqAnnotationStatisticsPO">
        SELECT *
        FROM faq_annotation_statistics
        WHERE task_id = #{taskId}
        ORDER BY annotation_type, annotation_subtype
    </select>

    <!-- 更新或插入统计信息 -->
    <insert id="upsertStatistics">
        INSERT INTO faq_annotation_statistics (
            id, task_id, annotation_type, annotation_subtype, count, created_at, updated_at
        ) VALUES (
            REPLACE(UUID(), '-', ''), #{taskId}, #{annotationType}, #{annotationSubtype}, #{count}, NOW(), NOW()
        ) ON DUPLICATE KEY UPDATE
            count = #{count},
            updated_at = NOW()
    </insert>

    <!-- 删除任务的所有统计信息 -->
    <delete id="deleteByTaskId">
        DELETE FROM faq_annotation_statistics
        WHERE task_id = #{taskId}
    </delete>

    <!-- 批量插入统计信息 -->
    <insert id="batchInsert">
        INSERT INTO faq_annotation_statistics (
            id, task_id, annotation_type, annotation_subtype, count, created_at, updated_at
        ) VALUES
        <foreach collection="statistics" item="stat" separator=",">
            (
                #{stat.id}, #{stat.taskId}, #{stat.annotationType}, #{stat.annotationSubtype},
                #{stat.count}, #{stat.createdAt}, #{stat.updatedAt}
            )
        </foreach>
    </insert>

    <!-- 重新计算并更新任务的统计信息 -->
    <delete id="recalculateStatistics">
        <!-- 先删除旧的统计数据 -->
        DELETE FROM faq_annotation_statistics WHERE task_id = #{taskId};
        
        <!-- 重新计算并插入统计数据 -->
        INSERT INTO faq_annotation_statistics (id, task_id, annotation_type, annotation_subtype, count, created_at, updated_at)
        SELECT 
            REPLACE(UUID(), '-', '') as id,
            #{taskId} as task_id,
            COALESCE(annotation_type, 'unknown') as annotation_type,
            annotation_subtype,
            COUNT(*) as count,
            NOW() as created_at,
            NOW() as updated_at
        FROM faq_annotation_detail
        WHERE task_id = #{taskId}
        AND annotation_type IS NOT NULL
        GROUP BY annotation_type, annotation_subtype;
    </delete>

</mapper>
