package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
/**
* 数量统计表
* Created  by Mr.hp
* DateTime on 2025-01-13 14:12:35
* <AUTHOR> Mr.hp
* @ApiModel(value = "QuantityStatistics", description = "数量统计表")
*/
@Data
@NoArgsConstructor
@Schema(description = "数量统计表")
public class QuantityStatistics implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    * @ApiModelProperty(value = "主键ID")
    */
    @Schema(description = "主键ID")
    private Long id;

    /**
    * 时间点（yyyy-MM-dd）
    * @ApiModelProperty(value = "时间点（yyyy-MM-dd）")
    */
    @Schema(description = "时间点（yyyy-MM-dd）")
    private String timing;

    /**
    * 系统ID
    * @ApiModelProperty(value = "系统ID")
    */
    @Schema(description = "系统ID")
    private String systemId;

    /**
    * 数据类型（0-AI审核任务数，1-AI审核任务通过数，2-AI审核材料数，3-AI审核材料通过数，4-人工审核任务数，5-人工审核任务通过数，6-人工复审材料数，7-人工复审材料准确数，8-TP数，9-FP数，10-TN数，11-FN数）
    */
    @Schema(description = "数据类型（0-AI审核任务数，1-AI审核任务通过数，2-AI审核材料数，3-AI审核材料通过数，4-人工审核任务数，5-人工审核任务通过数，6-人工复审材料数，7-人工复审材料准确数，8-TP数，9-FP数，10-TN数，11-FN数）")
    private Integer dataType;
    /**
     * 数据类型（0-AI审核任务数，1-AI审核任务通过数，2-AI审核材料数，3-AI审核材料通过数，4-人工审核任务数，5-人工审核任务通过数，6-人工复审材料数，7-人工复审材料准确数，8-TP数，9-FP数，10-TN数，11-FN数）
     */
    @Schema(description = "数据类型（0-AI审核任务数，1-AI审核任务通过数，2-AI审核材料数，3-AI审核材料通过数，4-人工审核任务数，5-人工审核任务通过数，6-人工复审材料数，7-人工复审材料准确数，8-TP数，9-FP数，10-TN数，11-FN数）")
    private List<Integer> dataTypeList;

    /**
    * 数量
    * @ApiModelProperty(value = "数量")
    */
    @Schema(description = "数量")
    private BigDecimal quantity;

    /**
    * 创建时间
    * @ApiModelProperty(value = "创建时间")
    */
    @Schema(description = "创建时间")
    private String createTime;

    /**
    * 更新时间
    * @ApiModelProperty(value = "更新时间")
    */
    @Schema(description = "更新时间")
    private String updateTime;

    /**
     * 开始创建时间
     */
    @Schema(description = "开始创建时间")
    private String startTime;
    /**
     * 结束创建时间
     */
    @Schema(description = "结束创建时间")
    private String endTime;

    /**
     * 入参列表类型 0-AI审核，1-人工审核
     */
    @Schema(description = "入参列表类型 0-AI审核，1-人工审核")
    private Integer dataFlag;


}
