package com.faw.work.ais.aic.model.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RerankResponse {
    private Output output;
    private Usage usage;
    private String request_id;

    @Data
    public static class Output {
        private List results;
    }

    @Data
    public static class Result {
        private Document document;
        private int index;
        private double relevance_score;
    }

    @Data
    public static class Document {
        private String text;
    }

    @Data
    public static class Usage {
        private int total_tokens;
    }
} 