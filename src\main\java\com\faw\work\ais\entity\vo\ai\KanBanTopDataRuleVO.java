package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 运营看板顶部数据-规则维度
 */
@Data
public class KanBanTopDataRuleVO {

    @Schema(description = "AI审核总量")
    private String aiCheckAllNum;

    @Schema(description = "AI审核通过数量")
    private String aiPassNum;

    @Schema(description = "AI审核未通过数量")
    private String aiUnpassNum;

    @Schema(description = "AI审核通过且人工审核通过数量")
    private String aiPassHumanPassNum;

    @Schema(description = "AI审核通过且人工审核驳回数量")
    private String aiPassHumanUnpassNum;

    @Schema(description = "AI审核驳回且人工审核驳回数量")
    private String aiUnpassHumanUnpassNum;

    @Schema(description = "AI审核驳回且人工审核通过数量")
    private String aiUnpassHumanPassNum;

    @Schema(description = "规则准确率")
    private String ruleRate;
}
