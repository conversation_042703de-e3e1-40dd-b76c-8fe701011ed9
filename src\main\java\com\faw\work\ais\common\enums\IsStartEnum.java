package com.faw.work.ais.common.enums;

/**
 * 是否启用
 * @author: fengjixiang
 * @createTime: 2024/01/03 9:12
 * @version:1.0
 * @target
 */
public enum IsStartEnum {


    NOT_START(0, "禁用"),
    CONFIRM_START(1, "启用");

    IsStartEnum(Integer status, String statusDesc) {
        this.code = status;
        this.msg = statusDesc;
    }
    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
