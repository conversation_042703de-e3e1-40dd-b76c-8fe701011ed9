package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.model.AiAuditModelPoint;
import com.faw.work.ais.model.AiAuditModelScene;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;

/**
* ai审核场景关系表
* Created  by <PERSON>.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-05-08 10:52:10
*/
@InterceptorIgnore(tenantLine = "true")
public interface AiAuditModelSceneDao {

    /**
    * 新增
    */
    public int insert(@Param("aiAuditModelScene") AiAuditModelScene aiAuditModelScene);
    public int insertBatch(@Param("aiAuditModelScenes") List<AiAuditModelScene> aiAuditModelScene);

    /**
    * 删除
    */
    public int delete(@Param("id") Long id);

    /**
    * 修改
    */
    public int update(@Param("aiAuditModelScene") AiAuditModelScene aiAuditModelScene);


    /**
    * 根据id查询 getAiAuditModelSceneById
    */
    public AiAuditModelScene getAiAuditModelSceneById(@Param("id") Long id);

    /**
    * 全部查询
    */
    public List<AiAuditModelScene> getAiAuditModelSceneList(@Param("aiAuditModelScene")AiAuditModelScene aiAuditModelScene);



}

