package com.faw.work.ais.service.impl;

import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

@Service
public class PromptService {

    private String promptText;

    @PostConstruct
    public void init() {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("prompt.txt")) {
            if (inputStream == null) {
                throw new IllegalStateException("prompt.txt not found in classpath");
            }

            promptText = new BufferedReader(new InputStreamReader(inputStream))
                    .lines().collect(Collectors.joining("\n"));

        } catch (Exception e) {
            throw new RuntimeException("Failed to load prompt.txt", e);
        }
    }

    public String getPromptText() {
        return promptText;
    }
}
