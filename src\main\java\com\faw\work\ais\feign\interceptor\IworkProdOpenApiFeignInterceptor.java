package com.faw.work.ais.feign.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.SpringUtils;
import com.faw.work.ais.common.util.UcgTokenutils;
import com.faw.work.ais.config.UgcProdConfig;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;

/**
 * <AUTHOR>
 * 调用工作流和角色工作台feign接口拦截器
 */
@Slf4j
public class IworkProdOpenApiFeignInterceptor implements RequestInterceptor {

    @Resource(name = "ugcProdConfig")
    private UgcProdConfig ugcConfig;

    @Resource
    private RedissonClient redissonClient;

    // 开放api网关token
    private final String tokenKey = "ucg:aio:prod:gatewayToken";

    /**
     * 路径参数添加token
     * */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        try {
            String token;
            // redis缓存中获取token
            RBucket<String> tokenBucket = redissonClient.getBucket(tokenKey);
            if(tokenBucket.isExists()){
                token = tokenBucket.get();
            } else {
                log.info("当前运行环境："+ "prod");
                JSONObject jsonObject = UcgTokenutils.getUcgAccessToken("prod",ugcConfig.getProdAppKey(),ugcConfig.getProdAppSecret());
                log.info("调用工作台获取的token：" + jsonObject);
                Integer successCode = 200;
                if (successCode.equals(jsonObject.getInteger("code"))) {
                    token = jsonObject.getJSONObject("data").getString("access_token");
                    long expireTime = jsonObject.getJSONObject("data").getLong("expire");

                    // 过期时间少于30秒不做缓存处理
                    if (expireTime > 30) {
                        tokenBucket.set(token);
                        tokenBucket.expire(Duration.ofSeconds(expireTime - 20));
                    }
                }else {
                    log.info("获取工作台token失败" + jsonObject);
                    throw new BizException("获取工作台token失败" + jsonObject);
                }
            }
            requestTemplate.query("access_token",token);
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }

    }



}
