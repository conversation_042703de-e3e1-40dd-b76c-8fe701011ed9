package com.faw.work.ais.common.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聊天工具方法 枚举类
 *
 * <AUTHOR>
 * @since 2025-06-05 16:29
 */
@Getter
@AllArgsConstructor
public enum ChatToolEnum {

    /**
     * 获取当前时间方法
     */
    GET_CURRENT_TIME("getCurrentTime", "获取当前时间方法"),

    /**
     * 在线客服方法
     */
    STAFF_SERVICE("staffService", "在线客服方法：当用户提及人工客服、在线客服时执行"),

    /**
     * 车辆手册信息获取方法
     */
    GET_VEHICLE_MANUAL("getVehicleManual", "车辆手册信息获取：根据用户提供的车系查询车辆手册信息"),

    /**
     * 图片解析方法
     */
    ANALYZE_PHOTO("analyzePhoto", "图片解析方法：根据提供的图片连接和聊天记录解析图片内容"),

    /**
     * 上门取送车方法
     */
    PICK_UP_CAR("pickUpCar", "上门取送车方法：根据用户提供的取车时间和取车地点预约上门取送车服务"),

    /**
     * 维修保养方法
     */
    MAINTENANCE("maintenance", "维修保养方法：预约维修保养"),

    /**
     * 车辆故障方法
     */
    VEHICLE_FAULT("vehicleFault", "车辆故障方法：帮助用户识别车辆故障"),

    /**
     * 维修进度方法
     */
    REPAIR_PROGRESS("repairProgress", "维修进度方法：帮助用户查询车辆维修进度");


    private final String name;
    private final String description;

    /**
     * 根据名称获取枚举值
     *
     * @param name 枚举名称
     * @return 枚举值
     */
    public static ChatToolEnum getByName(String name) {
        for (ChatToolEnum value : values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

}
