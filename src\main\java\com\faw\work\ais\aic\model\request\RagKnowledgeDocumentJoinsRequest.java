package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库文档关联请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "知识库文档关联请求")
public class RagKnowledgeDocumentJoinsRequest {

    @Schema(description = "绑定ID")
    private String id;

    @Schema(description = "知识库id")
    private Long baseId;

    @Schema(description = "文档id")
    private Long documentId;
} 