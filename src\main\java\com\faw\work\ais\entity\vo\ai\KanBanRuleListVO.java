package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 看板的列表数据
 */
@Data
//public class KanBanRuleListVO extends KanBanBillListVO{
public class KanBanRuleListVO {

    @Schema(description = "每次AI请求的id" )
    private String traceId;

    @Schema(description = "规则名称" )
    private String ruleName;

    @Schema(description = "规则Id" )
    private String ruleId;

    @Schema(description = "规则材料链接-待定这里建议一个新接口，只显示一个按钮，把tranceId和batchId传过去查" )
    private String ruleFileUrl;

    @Schema(description = "规则AI审核状态;0-驳回；1-通过；" )
    private String ruleAiCheckStatus;

    @Schema(description = "规则人工审核状态；0-驳回；1-通过" )
    private String ruleHumanCheckStatus;

    @Schema(description = "AI审核原因" )
    private String aiCheckReason;

    @Schema(description = "人工审核原因" )
    private String humanCheckReason;

    @Schema(description = "AI审核时间" )
    private String aiCheckTime;

    @Schema(description = "人工审核时间" )
    private String humanCheckTime;

}
