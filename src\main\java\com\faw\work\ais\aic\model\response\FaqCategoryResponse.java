package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类目响应实体
 */
@Data
@Schema(description = "类目响应")
public class FaqCategoryResponse {
    
    @Schema(description = "类目ID")
    private String id;
    
    @Schema(description = "类目名称")
    private String name;
    
    @Schema(description = "空间ID")
    private Integer spaceId;
    
    @Schema(description = "父类ID")
    private String parentId;
    
    @Schema(description = "创建人")
    private String createdBy;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新人")
    private String updatedBy;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "子类目列表")
    private List<FaqCategoryResponse> children;
} 