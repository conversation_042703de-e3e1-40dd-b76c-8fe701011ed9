package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * log_info_python_callback_new
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "日志Python信息表")
public class LogInfoPythonCallbackWithBLOBs extends LogInfoPythonCallback implements Serializable {
    /**
     * 调用python服务的入参
     */
    @Schema(description = "调用python服务的入参")
    private String pythonParam;

    /**
     * python服务返回结果
     */
    @Schema(description = "服务返回结果")
    private String pythonResultJson;

    /**
     * 回调入参
     */
    @Schema(description = "回调入参")
    private String callbackParam;

    /**
     * 回调结果
     */
    @Schema(description = "回调结果")
    private String callbackResult;

    @Schema(description = "串行版本 UID")
    private static final long serialVersionUID = 1L;
}