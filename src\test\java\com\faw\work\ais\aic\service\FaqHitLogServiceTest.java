package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper;
import com.faw.work.ais.aic.model.response.FaqHitLogCleanResponse;
import com.faw.work.ais.aic.service.impl.FaqHitLogServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FAQ命中日志Service测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class FaqHitLogServiceTest {

    @Mock
    private FaqHitLogMapper faqHitLogMapper;

    @InjectMocks
    private FaqHitLogServiceImpl faqHitLogService;

    @Test
    void testCleanOldHitLogs() {
        // 模拟清理了100条记录
        when(faqHitLogMapper.cleanOldHitLogs()).thenReturn(100);

        // 执行清理操作
        FaqHitLogCleanResponse response = faqHitLogService.cleanOldHitLogs();

        // 验证结果
        assertNotNull(response);
        assertEquals(100, response.getCleanCount());
        assertNotNull(response.getCleanTime());
        assertEquals("成功清理一个月前的FAQ命中日志数据", response.getDescription());

        // 验证Mapper方法被调用
        verify(faqHitLogMapper, times(1)).cleanOldHitLogs();
    }

    @Test
    void testCleanOldHitLogsWithZeroRecords() {
        // 模拟没有记录需要清理
        when(faqHitLogMapper.cleanOldHitLogs()).thenReturn(0);

        // 执行清理操作
        FaqHitLogCleanResponse response = faqHitLogService.cleanOldHitLogs();

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCleanCount());
        assertNotNull(response.getCleanTime());
        assertEquals("成功清理一个月前的FAQ命中日志数据", response.getDescription());

        // 验证Mapper方法被调用
        verify(faqHitLogMapper, times(1)).cleanOldHitLogs();
    }
}
