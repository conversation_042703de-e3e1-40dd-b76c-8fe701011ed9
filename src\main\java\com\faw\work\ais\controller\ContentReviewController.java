package com.faw.work.ais.controller;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.domain.ContentRulePO;
import com.faw.work.ais.entity.request.*;
import com.faw.work.ais.service.*;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/content")
@Tag(name = "内容审核", description = "内容审核相关接口")
@Slf4j
@RequiredArgsConstructor
public class ContentReviewController {

    private final ContentRuleService contentRuleService;
    private final ContentReviewService contentReviewService;

    @PostMapping("/commentReview")
    @Operation(summary = "评论评分", description = "[author:10207439]")
    public Response<JSONObject> commentScore(@RequestBody @Valid CommentRequest request) {
        log.info("接收到评论评分请求: 主题:{},评论:{}", request.getTopic(), request.getContent());

        JSONObject jsonObject = contentReviewService.commentReview_v1(request);
        return Response.success(jsonObject);
    }
    @PostMapping("/postReview")
    @Operation(summary = "动态评分", description = "[author:10207439]")
    public Response<JSONObject> postReview(@RequestBody @Valid PostRequest request) {
        log.info("接收到动态评分请求: 内容:{},图片地址:{}", request.getContent(), request.getPicUrls());

        JSONObject jsonObject = contentReviewService.postReview_v1(request);
        return Response.success(jsonObject);

    }

    @PostMapping("/commentSummary")
    @Operation(summary = "评论总结", description = "[author:10207439]")
    public Response<String> commentSummary(@RequestBody @Valid List<CommentSummaryRequest> requests) {
        log.info("接收到评论总结请求：{}", JSON.toJSONString(requests));

//        return Response.success(contentReviewService.commentSummary(requests));
        contentReviewService.commentSummary(requests);
        return Response.success("处理中");
    }

    @PostMapping("/postSummary")
    @Operation(summary = "动态总结", description = "[author:10207439]")
    public Response<String> postSummary(@RequestBody @Valid List<PostSummaryRequest> requests) {
        log.info("接收到动态总结请求：{}", JSON.toJSONString(requests));

        contentReviewService.postSummary(requests);
        return Response.success("处理中");
    }

    @PostMapping("/queryRule")
    @Operation(summary = "查询规则", description = "[author:10207439]")
    public Response<List<ContentRulePO>> queryCommentRule(@RequestParam @Valid int type) {

        List<ContentRulePO> res = contentRuleService.getBaseMapper().selectList(
                new QueryWrapper<ContentRulePO>()
                        .eq("type", type)
                        .orderByDesc("created_at")
        );
        return Response.success(res);
    }

    @PostMapping("/postReviewTruth")
    @Operation(summary = "动态评分（事实）", description = "[author:10207439]")
    public Response<JSONObject> postReviewTruth(@RequestBody @Valid PostRequest request) {
        JSONObject jsonObject = contentReviewService.postReviewTruth(request, 3);
        return Response.success(jsonObject);
    }

    @PostMapping("/editContentRule")
    @Operation(summary = "规则启用/禁用", description = "[author:10207439]")
    public Response<String> editRule(@RequestBody @Valid ContentRulePO request) {
        String res = contentReviewService.editRule(request);
        return Response.success(res);
    }

    @PostMapping("/deleteContentRule")
    @Operation(summary = "删除规则", description = "[author:10207439]")
    public Response<String> deleteRule(@RequestParam @Valid Long id) {
        String res = contentReviewService.deleteRule(id);
        return Response.success(res);
    }

}
