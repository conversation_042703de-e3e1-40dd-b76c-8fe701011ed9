package com.faw.work.ais.aic.common.constant;

/**
 * FAQ命中类型枚举
 *
 * <AUTHOR>
 */
public enum FaqHitTypeEnum {
    DIRECT("direct", "直接命中"),
    RECOMMEND("recommend", "推荐命中");

    private final String code;
    private final String desc;

    FaqHitTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
} 