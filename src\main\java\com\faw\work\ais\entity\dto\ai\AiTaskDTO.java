package com.faw.work.ais.entity.dto.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;


/**
 * aiTask入参
 * <AUTHOR>
 * @since 2024/4/3
 */
@Schema(description = "aiTask入参")
@Data
public class AiTaskDTO {

    @Schema(description = "系统 ID")
    @NotNull
    @ApiModelProperty(value = "系统id ")
    private String systemId;

    @Schema(description = "ai审核模型id")
    @ApiModelProperty(value = "ai审核模型id")
    private Long auditModelSceneId;

    @Schema(description = "内容")
    @ApiModelProperty(value = "被校验文件信息")
    private List<BeCheckFileDTO> contents;

    @Schema(description = "给定信息 json")
    @NotEmpty
    @ApiModelProperty(value = "校的字段和内容{\"idcard\":\"123456456\",\"vin\":\"220122455577778884\",\"userType\":\"A类\"}")
    private String givenInfoJson;

    @Schema(description = "给定信息 JSON DESC")
    @NotEmpty
    @ApiModelProperty(value = "校验字段的自然文字描述 其中isQualified是一个true或者false的布尔值类型，描述放在explain里面")
    private String givenInfoJsonDesc;

    @Schema(description = "回调 URL")
    @ApiModelProperty(value = "回调地址-暂时不用保留")
    private String callbackUrl;

    @Schema(description = "回调类型")
    @ApiModelProperty(value = "回调类型-暂时不用保留")
    private String callbackType;

    @Schema(description = "任务类型")
    @NotEmpty
    @ApiModelProperty(value = "线下约定，对方提供;当bizType类型不够用的时候，增加taskType")
    private String taskType;

    @Schema(description = "业务 ID")
    @NotEmpty
    @ApiModelProperty(value = "业务主键")
    private String bizId;

    @Schema(description = "业务类型")
    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @Schema(description = "回调自定义参数")
    @ApiModelProperty(value = "自定义参数")
    private String callBackCustomParam;

    @Schema(description = "版本")
    @ApiModelProperty(value = "版本")
    private String version;

    @Schema(description = "跟踪 ID")
    @NotNull
    @ApiModelProperty(value = "对方提供一个标识uuid，查日志用")
    private String traceId;

    @Schema(description = "测试")
    @ApiModelProperty(value = "测试")
    private String test;

    @Schema(description = "批次 ID")
    @ApiModelProperty(value = "一个batchId包含多次python请求，一个batchId对应多个traceId")
    private String batchId;
}
