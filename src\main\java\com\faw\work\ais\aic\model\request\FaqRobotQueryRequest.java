package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FAQ机器人查询请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ机器人查询请求对象")
public class FaqRobotQueryRequest {
    
    @Schema(description = "机器人名称")
    private String robotName;
    
    @Schema(description = "页码", defaultValue = "1")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;
    
    @Schema(description = "每页大小", defaultValue = "10")
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize;
} 