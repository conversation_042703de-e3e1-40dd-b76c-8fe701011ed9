package com.faw.work.ais.entity.dto.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.wildfly.common.annotation.NotNull;

import java.util.Date;

/**
 * AI的对应的人工审核结果
 * <AUTHOR>
 * @since 2024/7/11
 */
@Schema(description = "AI的对应的人工审核结果")
@Data
public class HumanResultDTO {

    /**
     * 人工审核结果-单次; 0-驳回；1-通过；单次
     */
    @ApiModelProperty("人工审核结果-单次; 0-驳回；1-通过；单次")
    @NotNull
    private String humanCheckResultSingle;

    /**
     * 人工驳回原因；
     */
    @ApiModelProperty("人工驳回原因；")
    @NotNull
    private String humanRefuseReason;

    /**
     * 审核单据的批次id
     */
    @ApiModelProperty("审核单据的批次id")
    @NotNull
    private String batchId;

    /**
     * 调用一次AI审核的唯一id
     */
    @ApiModelProperty("调用一次AI审核的唯一id")
    @NotNull
    private String traceId;

    /**
     * 业务主键，不唯一
     */
    @ApiModelProperty("业务主键，不唯一")
    @NotNull
    private String bizId;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    @NotNull
    private String taskType;

    /**
     * 业务类型
     */
    @ApiModelProperty
    private String bizType;

    /**
     * 人工审核时间/
     */
    @ApiModelProperty("人工审核时间")
    private String humanCheckTime;

    /**
     * 系统id
     */
    @ApiModelProperty("系统id")
    @NotNull
    private String systemId;

    /**
     * 人工审核结果-单据维度; 0-驳回；1-通过；按单据维度，多次审核一次失败就为驳回，全部通过算通过；
     */
    @ApiModelProperty("人工审核结果-单据维度; 0-驳回；1-通过；按单据维度，多次审核一次失败就为驳回，全部通过算通过；")
    @NotNull
    private String humanCheckResultFinal;

}
