package com.faw.work.ais.entity.vo.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "审核结果准确率返回对象")
@Data
public class AiOperationCenterCountVo {

    @Schema(description = "AI审核单据数量")
    private BigDecimal aiBillApproveCount;

    @Schema(description = "人工审核单据数量")
    private BigDecimal humanBillApproveCount;

    @Schema(description = "AI审核通过数量")
    private BigDecimal aiApprovePassCount;

    @Schema(description = "AI审核驳回数量")
    private BigDecimal aiApproveRejectCount;

    @Schema(description = "TP-AI通过人工通过-准确率")
    private BigDecimal tpRightRate;

    @Schema(description = "TP-AI通过人工通过-数量")
    private BigDecimal tpCount;

    @Schema(description = "TN-AI通过人工驳回-数量")
    private BigDecimal tnCount;

    @Schema(description = "FN-AI驳回人工驳回-准确率")
    private BigDecimal fnRightRate;

    @Schema(description = "FP-AI驳回人工通过-数量")
    private BigDecimal fpCount;

    @Schema(description = "FN-AI驳回人工驳回-数量")
    private BigDecimal fnCount;

}
