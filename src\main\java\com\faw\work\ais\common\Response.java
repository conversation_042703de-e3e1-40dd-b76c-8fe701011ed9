package com.faw.work.ais.common;

import com.faw.work.ais.common.enums.IEnum;
import com.faw.work.ais.common.enums.ResEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.slf4j.MDC;

import java.time.LocalDateTime;

/**
 * 响应信息主体
 */

@Schema(description = "响应信息主体")
@Data
public class Response<T> {

    @Schema(description = "响应代码 成功：200；失败：-111" )
    private  String code;

    @Schema(description = "响应描述：请求成功，请求失败" )
    private  String message;

    @Schema(description = "唯一码，查日志使用" )
    private String traceId;

    @Schema(description = "响应的时间戳" )
    private LocalDateTime timestamp;

    @Schema(description = "返回数据的结构体" )
    private T data;

    private Response(String code, String message, String traceId, T data, LocalDateTime timestamp) {
        this.code = code;
        this.message = message;
        this.traceId = traceId;
        this.data = data;
        this.timestamp = timestamp;
    }

    public Response() {
        super();
    }

    private Response(T data) {
        super();
        this.data = data;
    }

    private Response(String message, String code) {
        super();
        this.code = code;
        this.message = message;
    }

    private Response(Throwable e) {
        super();
        this.message = e.getMessage();
        this.code = ResEnum.SUCCESS_CODE.code();
    }

    public Boolean isSuccess() {
        return ResEnum.SUCCESS_CODE.code().equals(this.code);
    }

    public static <T> Response<T> of() {
        return Response.<T>builder().build();
    }

    public static <T> Response<T> of(T data) {
        return Response.<T>builder().data(data).build();
    }

    public static <T> Response<T> of(String message, String code) {
        return Response.<T>builder().message(message).code(code).build();
    }

    public static <T> Response<T> of(T data, String msg) {
        return Response.<T>builder().data(data).message(msg).build();
    }

    public static <T> Response<T> fail(IEnum errorEnum) {
        return Response.<T>builder()
                .message(errorEnum.msg())
                .code(errorEnum.code())
                .build();
    }

    public static <T> Response<T> fail(String msg) {
        return Response.<T>builder()
                .message(msg)
                .code(ResEnum.FAIL_CODE.code())
                .build();
    }

    public static <T> Response<T> success(T data) {
        return Response.<T>builder()
                .message("操作成功")
                .code(ResEnum.SUCCESS_CODE.code())
                .data(data)
                .build();
    }
    public static <T> Response<T> success() {
        return Response.<T>builder()
                .message(ResEnum.SUCCESS_CODE.code())
                .code(ResEnum.SUCCESS_CODE.code())
                .build();
    }

    public static <T> Response<T> successMsg(String msg) {
        return Response.<T>builder()
                .message(msg)
                .code(ResEnum.SUCCESS_CODE.code())
                .build();
    }
    public static <T> ResponseBuilder<T> builder() {
        return new ResponseBuilder<>();
    }


    @Schema(description = "响应生成器")
    public static class ResponseBuilder<T> {
        @Schema(description = "代码$值")
        private String code$value;
        @Schema(description = "message$value")
        private String message$value;
        @Schema(description = "数据")
        private T data;

        ResponseBuilder() {
        }

        public ResponseBuilder<T> code(final String code) {
            this.code$value = code;
            return this;
        }

        public ResponseBuilder<T> message(final String message) {
            this.message$value = message;
            return this;
        }

        public ResponseBuilder<T> data(final T data) {
            this.data = data;
            return this;
        }

        public Response<T> build() {
            String code$value = this.code$value;
            String message$value = this.message$value;
            String traceId = MDC.getMDCAdapter().get("traceId");
            return new Response<>(code$value, message$value, traceId, this.data, LocalDateTime.now());
        }

        public String toString() {
            return "Response.ResponseBuilder(code$value=" + this.code$value + ", msg$value=" + this.message$value + ", data=" + this.data + ")";
        }
    }

}
