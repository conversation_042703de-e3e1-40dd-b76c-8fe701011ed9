package com.faw.work.ais.service.impl;

import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.common.dto.chat.RecommendRequest;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.service.RecommendService;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;

import static com.faw.work.ais.aic.config.MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD;

@Service
@Slf4j
@RequiredArgsConstructor
public class RecommendServiceImpl implements RecommendService {

    private final EmbeddingService embeddingService;
    private final MilvusService milvusService;

    private static final int DEFAULT_RECOMMENDATION_COUNT = 10;

    @Override
    public List<String> getRecommendations(RecommendRequest request) {
        return getRecommendations(request, DEFAULT_RECOMMENDATION_COUNT, 0.5f, 0.5f, request.getExcludeIds());
    }

    @Override
    public List<String> getRecommendations(RecommendRequest request, int count, float similarityThreshold, float rate, List<String> excludeIds) {

        // 历史对话记录
        List<String> historyList = request.getHistoryQuestions();

        // 用户标签
        List<String> labelList = request.getLabel();
        String series = request.getSeries();
        if (series != null){
            // 将车型拼接在lableList的每个元素的前面
            labelList = labelList.stream()
                    .map(label -> series + "：" + label)
                    .collect(Collectors.toList());
        }


//        if (CollectionUtils.isEmpty(historyList)) {
//            log.info("用户 {} 历史记录为空, 无法进行个性化推荐。", request.getUserId());
//            // TODO: 可以返回默认的热门问题列表
//            return Collections.emptyList();
//        }

        List<String> labelResultList = getSimilarList(request, count, similarityThreshold, excludeIds, labelList);
        List<String> historyResultList = getSimilarList(request, count, similarityThreshold, excludeIds, historyList);

        return shuffleAndCompletion(count, rate, labelResultList, historyResultList);

    }

    private List<String> shuffleAndCompletion(int count, float rate, List<String> labelResultList, List<String> historyResultList) {

        // 此处完全是考虑每次进入的问题随机
        // 将两个列表转换为新的ArrayList以便随机打乱顺序
        List<String> shuffledLabelList = new ArrayList<>(labelResultList);
        List<String> shuffledHistoryList = new ArrayList<>(historyResultList);
        // 随机打乱两个列表
        Collections.shuffle(shuffledLabelList);
        Collections.shuffle(shuffledHistoryList);

        LinkedHashSet<String> unique = new LinkedHashSet<>();

        // 计算标签结果应该占用的数量
        int labelCount = Math.round(count * rate);

        for (String s : shuffledHistoryList) {
            if (unique.size() >= count - labelCount) break;
            unique.add(s);
        }

        for (String s : shuffledLabelList) {
            if (unique.size() >= count) break;
            unique.add(s);
        }

        // 转换为ArrayList并获取指定数量的结果
        List<String> result = new ArrayList<>(unique).subList(0, Math.min(count, unique.size()));

        // 最后再次随机打乱结果
        Collections.shuffle(result);

        return result;
    }

    private List<String> getSimilarList(RecommendRequest request, int count, float similarityThreshold, List<String> excludeIds, List<String> historyList) {
        // 2. 批量获取历史问题的 Embedding
        List<EmbeddingPropertyDTO> embeddingDTOs = embeddingService.getEmbeddingList(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, historyList);
        if (CollectionUtils.isEmpty(embeddingDTOs)) {
            log.warn("无法获取用户 {} 历史记录的 embedding。", request.getUserId());
            return Collections.emptyList();
        }

        // 从DTO列表中提取向量
        List<List<Float>> historyEmbeddings = embeddingDTOs.stream()
                .map(EmbeddingPropertyDTO::getEmbedding)
                .filter(Objects::nonNull)
                .map(this::toFloatList)
                .collect(Collectors.toList());

        // 3. 调用 Milvus 批量搜索
        // 此处的 filterString 可以用来加入其他过滤条件，例如根据用户车型、标签等
        String bizInfoValue = request.getRobotId();
        String filterString = "biz_info == \"" + bizInfoValue + "\"";
        if (!CollectionUtils.isEmpty(excludeIds)){
            String idList = excludeIds.stream()
                    .map(id -> "\"" + id + "\"")
                    .collect(Collectors.joining(","));
            filterString += " and id not in [" + idList + "]";
        }
        SearchResp searchResp = milvusService.batchSearch(
                FAQ_COLLECTION_NAME_PROD,
                historyEmbeddings,
                count,
                filterString);

        // 4. 处理和排序搜索结果
        return processAndRankResults(searchResp, count, similarityThreshold);
    }

    private List<String> processAndRankResults(SearchResp searchResp, int count, float similarityThreshold) {
        if (searchResp == null) {
            return Collections.emptyList();
        }

        List<ScoredResult> scoredResults = new ArrayList<>();
        Set<String> uniqueContents = new HashSet<>(); // 用于去重

        // Milvus 批量检索结果结构：List<List<SearchResp.SearchResult>>
        List<List<SearchResp.SearchResult>> searchResults = searchResp.getSearchResults();
        if (searchResults == null) {
            return Collections.emptyList();
        }

        outer:
        for (int i = 0; i < DEFAULT_RECOMMENDATION_COUNT; i++) {
            for (List<SearchResp.SearchResult> resultList : searchResults) {
                if (resultList == null || i >= resultList.size()) continue;
                SearchResp.SearchResult result = resultList.get(i);
                String content = (String) result.getEntity().get("content");
                Float score = result.getScore();
                if (content != null && uniqueContents.add(content) && score != null && score > similarityThreshold) {
                    scoredResults.add(new ScoredResult(content, score));
                    if (scoredResults.size() >= count) break outer;
                }
            }
        }

        // 按分数降序排序，取前N条，只返回content字段
        return scoredResults.stream()
                .map(ScoredResult::getContent)
                .limit(count)
                .collect(Collectors.toList());
    }

    private List<Float> toFloatList(float[] array) {
        if (array == null) {
            return Collections.emptyList();
        }
        List<Float> list = new ArrayList<>(array.length);
        for (float f : array) {
            list.add(f);
        }
        return list;
    }

    @Getter
    @AllArgsConstructor
    private static class ScoredResult {
        private final String content;
        private final float score;
    }
} 