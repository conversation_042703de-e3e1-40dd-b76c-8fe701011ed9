package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.*;
import com.faw.work.ais.entity.request.CommentRequest;
import com.faw.work.ais.entity.request.CommentSummaryRequest;
import com.faw.work.ais.entity.request.PostSummaryRequest;
import com.faw.work.ais.feign.chat.AliYunFeignClient;
import com.faw.work.ais.feign.content.AliFeignClient;
import com.faw.work.ais.service.LLMVLService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class LLMVLServiceImpl implements LLMVLService {

    public static final String ANALYSIS_PROMPT = "你是一位专业的视频内容分析师，你的任务是对给定的视频进行深入分析。请根据以下指导原则完成任务：\n" +
            "\n" +
            "- 仔细观看并理解视频内容。\n" +
            "- 分析视频的主题、风格、目标受众以及可能想要传达的信息或情感。\n" +
            "- 如果适用的话，评估视频的技术质量（如剪辑、音效、视觉效果等）。\n" +
            "- 提供关于视频如何影响观众的看法或感受的见解。\n" +
            "- 根据需要，提出改进建议以增强视频的效果。\n" +
            "\n" +
            "请基于上述要求对指定视频进行全面分析。";


    // public static final String PHOTO_ANALYSIS_PROMPT = "你是一个汽车行业图片识别专家，能分析出图片的内容，要求准确精炼。识别出来的内容尽量和汽车相关，尤其关注汽车故障类问题，例如XX故障，XX问题";
    public static final String PHOTO_ANALYSIS_PROMPT = "你是一位专业的图像分析与识别专家，你的任务是从给定的图片中提取出关键信息。这包括但不限于：\n" +
            "- 识别并描述图片中的主要对象或场景。\n" +
            "- 分析图片的颜色、纹理等视觉特征。\n" +
            "- 提取任何可能的文字信息（如果存在）。\n" +
            "- 对图片的整体内容给出一个简短但全面的总结。\n" +
            "\n" +
            "请基于以上要求对提供的图片进行详细的分析，并报告你的发现。";

    public static final String OCR_PROMPT = "你是一位专业的OCR文字识别专家，你的任务是从给定的图片中提取出所有文字内容。请遵循以下要求：\n" +
            "- 识别图片中的所有文字，包括主要文本和次要文本。\n" +
            "- 保持文字的原始格式和顺序。\n" +
            "- 如果有表格，尽量保持表格的结构。\n" +
            "- 对于难以识别的文字，可以用[?]标记。\n" +
            "- 不要添加任何解释或分析，只需提供识别出的文字内容。\n" +
            "\n" +
            "请基于以上要求对提供的图片进行OCR文字识别，并返回识别结果。";

    public static final String COMMENT_SUMMARY_PROMPT = """
                                                                
                                                                你是一个专业的规则制定人员，请根据下列红旗智联app的评论内容及其标签（优质评论/普通评论）制定一套规则，包括评分的维度、各项分数和总分阈值
                                                                1.设计方案：
                                                                - 按总分100分的标准进行设计，除了阈值这一项其余每一项的得分之和应为100分
                                                                - 最后一项为固定的得分阈值，得分阈值高于此分数即为优质评论，低于此分数则为普通评论
                                                                - 按照总结的这套规则评分后，每一条评论均符合各自的标签
                                            
                                                                2. 输出格式必须为严格合法的JSON：
                                                                {
                                                                    "...": {"score": 总分，例如20, "reason": 评判标准，例如"主题明确，逻辑清晰"},
                                                                    ...,
                                                                    "得分阈值":{"score":数字，例如80, "reason": 原因，例如"80分标准能够进行有效评分"}
                                                                }
                                                                
                                                                3. 必须遵守：
                                                                - 仅基于可见内容判断，禁止猜测或假设
                                                                - 总分原因尽可能清晰具体
                                                                - 确保JSON格式正确（引号、逗号、括号闭合）
                                                                - 不要添加任何额外文本
                                                                """;

    public static final String COMMENT_PROMPT = """
                                                                你是一个专业的红旗智联评论审核助手，下列提供了动态主题和评论，请严格按如下规则为评论打分：
                                                                1. 如果评论中出现黄色暴力、不文明用语、广告这些内容，认为是较差评论-1分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "violation": {"score": -1, "reason": "原因分析"}
                                                                }
                                                                1. 如果评论中只有毫无意义的数字或字符或表情或重复的“红旗加油加油加油”，“赞赞赞”等，认为是较差评论0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "meaningless": {"score": 0, "reason": "原因分析"}
                                                                }
                                                                1. 如果评论中出现车辆出现的故障，消极态度认为是较差评论10分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "fault": {"score": 10, "reason": "原因分析"}
                                                                }
                                                                1. 如果评论中表达对红旗车辆的询问，例如上市时间、价格等，需要提到具体询问内容，注意不是客服的语气，得110分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "interaction": {"score": 110, "reason": "原因分析"}
                                                                }
                                                                1. 按总分100分制对以下五项独立评分：
                                                                   - 相关性：评论是否与所提供的主题直接相关，注意，只需要和提供的主题相关此项即可得到最少15分，不必提到红旗或汽车，若提及红旗但与所提供的主题无关得0分
                                                                   - 内容质量：评论的内容是否有价值，是否提供了有用的信息或见解，注意，过于简短仅内容质量为5分，品牌方或客服人员的宣传回复例如您、你好、欢迎这类语言仅内容质量为10分，若与红旗无关并且均为语录类表达、对人生的看法之类仅内容质量为0分，剩余四项必须也要返回分数
                                                                   - 情感倾向：评论是否积极正面，是否表达了对红旗品牌的喜爱和支持，如果只有鼓励、人生哲理、语录类表达得0分
                                                                   - 表达清晰度：评论的语言是否通顺、清晰，是否易于理解
                                                                   - 创新性：评论是否有独特的观点或创意
                                            
                                                                2. 输出格式必须为严格合法的JSON：
                                                                {
                                                                    "relation": {"score": 0-20, "reason": "相关性分析"},
                                                                    "quality": {"score": 0-30, "reason": "内容质量分析"},
                                                                    "emotion": {"score": 0-20, "reason": "情感分析"},
                                                                    "definition": {"score": 0-20, "reason": "表达清晰度分析"},
                                                                    "innovation": {"score": 0-10, "reason": "评论是否有独特的观点或创意"}
                                                                }
                                                                
                                                                3. 必须遵守：
                                                                - 返回字数不超过100字
                                                                - 仅基于可见内容判断，禁止猜测或假设
                                                                - 评分原因需包含具体数据
                                                                - 确保JSON格式正确（引号、逗号、括号闭合）
                                                                - 不要添加任何额外文本
                                                                """;

    public static final String POST_SUMMARY_PROMPT = """
                                                                你是一个专业的规则制定人员，请根据下列红旗智联app的动态内容（话题+文字+图片+标签）制定一套规则，包括评分的维度以及解释、各项分数和总分阈值
                                                                1.设计方案：
                                                                    - 按总分100分的标准进行设计，除了权重这一项其余每一项的得分之和应为100分
                                                                    - 不要有父子项，一个标准一个得分
                                                                    - 话题为官方发布，话题本身不作为评判标准
                                                                    - 需要考虑内容和话题相关度、图片和内容相关度，若不相关则非优质内容
                                                                    - 提供的只有话题、文字和图片信息，并没有点赞数、评论数量、互动信息等，不要考虑这些因素
                                                                    - 得分阈值高于80分即为优质动态，低于80分则为普通动态
                                                                    - 按照总结的这套规则评分后，提供的每一条动态均符合各自的标签
                                                                2. 必须遵守：
                                                                - 仅基于可见内容判断，禁止猜测或假设
                                                                - 评分原因需包含具体数据
                                                                - 仅返回规则，不要返回具体评分案例
                                                                """;

    public static final String POST_SUMMARY_ALL_PROMPT = """
                                                                你是一个专业的规则制定人员，请根据不同维度的规则中进行更细致的总结，总结动态加精的几点原因以及每一点原因的加权占比，制定一套规则，包括评分的维度、各项分数和总分阈值
                                                                1.设计方案：
                                                                - 按总分100分的标准进行设计，除了阈值这一项其余每一项的得分之和应为100分
                                                                - 不要有父子项，一个标准一个分数
                                                                - 最后一项为固定的得分阈值，得分阈值高于此分数即为优质评论，低于此分数则为普通评论
                                                                
                                                                2. 输出格式必须为严格合法的JSON：
                                                                {
                                                                    规则1，例如"内容质量": {"score": 当前规则总分，例如20, "reason": 评判标准，例如"主题明确，逻辑清晰"},
                                                                    ...,
                                                                    "得分阈值":{"score":数字，例如80, "reason": 原因，例如"80分标准能够进行有效评分"}
                                                                }
                                            
                                                                3. 必须遵守：
                                                                - 仅基于可见内容判断，禁止猜测或假设
                                                                - 总分原因尽可能清晰具体
                                                                - 确保JSON格式正确（引号、逗号、括号闭合）
                                                                - 不要添加任何额外文本
                                                                """;

    public static final String FULL_POST_PROMPT = """
                                                                你是一个专业的红旗智联app动态审核助手，下列提供了动态话题、正文内容和图片列表，请严格按如下规则为动态打分：
                                                                1. 如果动态中出现【黄色暴力】、【不文明用语】,认为是违规动态得-1分，并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                注意：此规则只考虑违规性，一定不要考虑和话题的相关性以及是否符合话题要求
                                                                {
                                                                    "violation": {"score": -1, "reason": "原因分析"}
                                                                }
                                                                1. 如果动态中表达出对红旗的不满，认为是较差动态0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "complaint": {"score": 0, "reason": "原因分析"}
                                                                }
                                                                1. 如果动态内容中只有毫无意义的数字或字符或表情，认为是较差动态0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "meaningless": {"score": 0, "reason": "原因分析"}
                                                                }
                                                                1. 若没有出现上述三种情况，按总分100分制对以下五项独立评分：
                                                                   - 内容与话题相关性：分析[话题]与[正文内容]（包括标题和正文）的相关性。重点分析正文内容是否围绕[话题]展开
                                                                   - 内容质量：考察动态内容是否深入、完整、有见解，能否提供有价值的信息或情感共鸣，如果只有鼓励、人生哲理、语录类表达得0分，如果语言中存在重复用词得0分，内容不明确，有潜在误导性最多得10分
                                                                   - 语言表达：考察文字表述是否清晰、流畅，语法是否正确，是否有错别字
                                                                   - 情感倾向：评论是否积极正面，是否表达了对红旗品牌的喜爱和支持，如果只有鼓励、人生哲理、语录类表达得0分
                                                                   - 图片相关性：考察图片与文字内容的相关度，是否能够有效补充或解释文字内容，如果没有图片得0分
                                                                   - 图片清晰度和美观度：考察图片是否清晰，细节是否丰富，如果没有图片得0分
                                                                   - 创新性和独特性：考察动态内容或表现形式是否有新颖之处，是否能给人耳目一新的感觉
                                                                2. 输出格式必须为严格合法的JSON：
                                                                {
                                                                    "contentRelation": {"score": 0-20, "reason": "内容与话题契合度分析"},
                                                                    "contentQuality": {"score": 0-30, "reason": "内容质量分析"},
                                                                    "expression": {"score": 0-10, "reason": "语言表达分析"},
                                                                    "emotion": {"score": 0-10, "reason": "情感分析"},
                                                                    "picRelation": {"score": 0-10, "reason": "图片相关性分析"},
                                                                    "imageQuality": {"score": 0-10, "reason": "图片清晰度和美观度分析"},
                                                                    "innovation": {"score": 0-10, "reason": "创新性分析"},
                                                                }
                                                                
                                                                3. 必须遵守：
                                                                - 返回字数不超过100字
                                                                - 仅基于可见内容判断，禁止猜测或假设
                                                                - 评分原因需包含具体数据
                                                                - 确保JSON格式正确（引号、逗号、括号闭合）
                                                                - 不要添加任何额外文本
                                                                """;

    public static final String POST_PROMPT = """
                                                                你是一个专业的红旗智联app动态审核助手，下列提供了动态话题、正文内容和图片列表，请严格按如下规则为动态打分：
                                                                1. 如果动态中出现【黄色暴力】、【不文明用语】,认为是违规动态得-1分，并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                注意：此规则只考虑违规性，一定不要考虑和话题的相关性以及是否符合话题要求
                                                                {
                                                                    "violation": {"score": -1, "reason": "原因分析"}
                                                                }
                                                                1. 如果动态中表达出对红旗的不满，认为是较差动态0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "complaint": {"score": 0, "reason": "原因分析"}
                                                                }
                                                                1. 如果动态内容中只有毫无意义的数字或字符或表情，认为是较差动态0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
                                                                {
                                                                    "meaningless": {"score": 0, "reason": "原因分析"}
                                                                }
                                                                1. 若没有出现上述三种情况，按总分80分制对以下五项独立评分：
                                                                   - 内容与话题相关性：分析[话题]与[正文内容]（包括标题和正文）的相关性。重点分析正文内容是否围绕[话题]展开
                                                                   - 内容质量：考察动态内容是否深入、完整、有见解，能否提供有价值的信息或情感共鸣，如果只有鼓励、人生哲理、语录类表达得0分，如果语言中存在重复用词得0分，内容不明确，有潜在误导性最多得10分
                                                                   - 语言表达：考察文字表述是否清晰、流畅，语法是否正确，是否有错别字
                                                                   - 情感倾向：评论是否积极正面，是否表达了对红旗品牌的喜爱和支持，如果只有鼓励、人生哲理、语录类表达得0分
                                                                   - 图片相关性：考察图片与文字内容的相关度，是否能够有效补充或解释文字内容，如果没有图片得0分
                                                                   - 图片清晰度和美观度：考察图片是否清晰，细节是否丰富，如果没有图片得0分
                                                                   - 创新性和独特性：考察动态内容或表现形式是否有新颖之处，是否能给人耳目一新的感觉
                                                                2. 输出格式必须为严格合法的JSON：
                                                                {
                                                                    "contentRelation": {"score": 0-16, "reason": "内容与话题契合度分析"},
                                                                    "contentQuality": {"score": 0-24, "reason": "内容质量分析"},
                                                                    "expression": {"score": 0-8, "reason": "语言表达分析"},
                                                                    "emotion": {"score": 0-8, "reason": "情感分析"},
                                                                    "picRelation": {"score": 0-8, "reason": "图片相关性分析"},
                                                                    "imageQuality": {"score": 0-8, "reason": "图片清晰度和美观度分析"},
                                                                    "innovation": {"score": 0-8, "reason": "创新性分析"},
                                                                }
                                            
                                                                3. 必须遵守：
                                                                - 返回字数不超过100字
                                                                - 仅基于可见内容判断，禁止猜测或假设
                                                                - 评分原因需包含具体数据
                                                                - 确保JSON格式正确（引号、逗号、括号闭合）
                                                                - 不要添加任何额外文本
                                                                """;
    public static final String EXTRACT_PROMPT = """
            你是一个车辆参数提取助手，请根据提供的描述提取车系名称以及数字类型的参数名称和值，例如：
            {
            "name":"天工05",
            "续航":"1000km",
            "车长":"5m"
            },若没有提到任何数字类型的车辆参数信息，返回
            {
            "name":null
            }
            """;

    public static final String TRUTH_PROMPT = """
            你是一个参数对比助手，请根据提供的两段车辆参数判断描述是否一致，误差不超过10%均认为一致
            一致则返回“是”，
            不一致则返回“否”。
            注意，不要添加任何额外文本
            """;
    @Autowired
    private AliYunFeignClient aliYunFeignClient;

    @Autowired
    private AliFeignClient aliFeignClient;

    public String analyzePhoto(List<String> photoUrls, String query) {

        log.info("[PhotoAnalysisService][analyzePhoto][entrance] photoUrls: {}, query: {}", photoUrls, query);

        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", PHOTO_ANALYSIS_PROMPT)
        ));

        // User message - 支持多张图片
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含所有图片和文本的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        // 添加所有图片URL
        for (String photoUrl : photoUrls) {
            contentList.add(Map.of(
                    "type", "image_url",
                    "image_url", Map.of("url", photoUrl)
            ));
        }

        // 添加文本查询
        contentList.add(Map.of("type", "text", "text", query));

        userMessage.setContent(contentList);

        PhotoAnalysisRequest request = new PhotoAnalysisRequest();
        request.setModel("qwen-vl-max"); // 更新为最新模型版本
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[PhotoAnalysisService][analyzePhoto] request: {}", JSON.toJSONString(request));
        PhotoAnalysisResponse response = aliYunFeignClient.analyzePhoto(request);

        log.info("[PhotoAnalysisService][analyzePhoto] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未识别图片";
    }

    @Override
    public String analyzeVideo(String videoUrl, String query) {
        log.info("[PhotoAnalysisService][analyzeVideo][entrance] videoUrl: {}, query: {}", videoUrl, query);

        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", ANALYSIS_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含视频帧和文本的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        contentList.add(Map.of(
                "type", "video_url",
                "video_url", Map.of("url", videoUrl)
        ));


        // 添加文本查询
        contentList.add(Map.of("type", "text", "text", query));

        userMessage.setContent(contentList);

        PhotoAnalysisRequest request = new PhotoAnalysisRequest();
        request.setModel("qwen-vl-max");  // 使用最新模型版本，支持视频分析
        request.setTemperature("0.01");
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[PhotoAnalysisService][analyzeVideo] request: {}", JSON.toJSONString(request));
        PhotoAnalysisResponse response = aliYunFeignClient.analyzePhoto(request);

        log.info("[PhotoAnalysisService][analyzeVideo] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未识别视频";
    }

    @Override
    public String ocrImage(String photoUrl) {
        log.info("[PhotoAnalysisService][ocrImage][entrance] photoUrl: {}", photoUrl);

        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", OCR_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        // 添加图片URL
        contentList.add(Map.of(
                "type", "image_url",
                "image_url", Map.of(
                        "url", photoUrl,
                        "min_pixels", 28 * 28 * 4,
                        "max_pixels", 28 * 28 * 1280
                )
        ));

        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        PhotoAnalysisRequest request = new PhotoAnalysisRequest();
        request.setModel("qwen-vl-ocr");  // 使用OCR专用模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[PhotoAnalysisService][ocrImage] request: {}", JSON.toJSONString(request));
        PhotoAnalysisResponse response = aliYunFeignClient.analyzePhoto(request);

        log.info("[PhotoAnalysisService][ocrImage] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未识别图片文字";
    }

    @Override
    public String commentScore(String topic, String content) {
        log.info("评论主题 topic: {}, 评论内容 content: {}", topic, content);

        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", COMMENT_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        // 添加主题内容
        contentList.add(Map.of("type", "text", "text", "主题:" + topic));

        // 添加评论内容
        contentList.add(Map.of("type", "text", "text", "评论:" + content));

        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setTemperature(0.3f);
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentReview] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentReview] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }

    @Override
    public String postScore(String topic, String content, List<String> photoUrls, Boolean flag) {
        log.info("动态话题 topic: {}, 动态内容 content: {}, 动态图片: {}", topic, content, photoUrls);

        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        if (flag){
            systemMessage.setContent(List.of(
                    Map.of("type", "text", "text", FULL_POST_PROMPT)
            ));
        } else {
            systemMessage.setContent(List.of(
                    Map.of("type", "text", "text", POST_PROMPT)
            ));
        }


        // User message - 支持多张图片
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含所有图片和文本的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(photoUrls)) {
            // 添加所有图片URL
            for (String photoUrl : photoUrls) {
                contentList.add(Map.of(
                        "type", "image_url",
                        "image_url", Map.of("url", photoUrl)
                ));
            }
        }

        // 添加文本查询
        contentList.add(Map.of("type", "text", "text", "[话题]：" + topic));
        contentList.add(Map.of("type", "text", "text", "[正文内容]：" +content));
        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-vl-max"); // 更新为最新模型版本
        request.setTemperature(0.3f);
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[PostReview] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[PostReview] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未识别图片";
    }

    @Override
    public String commentSummary(List<CommentSummaryRequest> requests) {
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", COMMENT_SUMMARY_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        for (CommentSummaryRequest request : requests) {
            contentList.add(Map.of("type", "text", "text", "主题:" + request.getTopic() + "评论:" + request.getContent() + "标签:" + request.getTag()));
        }

        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummary] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummary] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }

    @Override
    public String postSummary(List<PostSummaryRequest> requests) {

        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", POST_SUMMARY_PROMPT)
        ));

        // User message - 支持多张图片
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含所有图片和文本的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        for (PostSummaryRequest request : requests) {
            contentList.add(Map.of("type", "text", "text", "主题:" + request.getTopic() + "动态:" + request.getContent() + "标签:" + request.getTag()));
            request.getPicUrls().forEach(photoUrl -> {
                contentList.add(Map.of(
                        "type", "image_url",
                        "image_url", Map.of("url", photoUrl)
                ));
            });
        }
        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-vl-max"); // 更新为最新模型版本
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[PostSummary] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[PostSummary] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未识别图片";
    }

    @Override
    public String postSummaryAll(List<String> batchResults) {
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", POST_SUMMARY_ALL_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        for (String batchResult : batchResults) {
            contentList.add(Map.of("type", "text", "text", batchResult));
        }

        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummaryAll] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummaryAll] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }
    @Override
    public String truthValidate(String content, String query) {
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", TRUTH_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        contentList.add(Map.of("type", "text", "text", content));
        contentList.add(Map.of("type", "text", "text", query));
        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummaryAll] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummaryAll] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }

    @Override
    public String extractVehicleParam(String content) {
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", EXTRACT_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        contentList.add(Map.of("type", "text", "text", content));
        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummaryAll] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummaryAll] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "提取失败";
    }
}
