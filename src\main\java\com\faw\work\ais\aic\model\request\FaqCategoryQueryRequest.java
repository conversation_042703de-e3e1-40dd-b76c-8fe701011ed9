package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FAQ类目查询请求对象
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ类目查询请求对象")
public class FaqCategoryQueryRequest {
    
    @Schema(description = "关键字")
    private String keyword;
    
    @Schema(description = "空间ID")
    private Integer spaceId;
    
    @Schema(description = "父ID")
    private Integer parentId;
} 