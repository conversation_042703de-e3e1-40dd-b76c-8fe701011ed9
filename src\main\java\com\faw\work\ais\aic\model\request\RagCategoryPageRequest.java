package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文档类目分页请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "文档类目分页请求")
public class RagCategoryPageRequest {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "类目名称")
    private String name;

    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页数量", defaultValue = "10")
    private Integer pageSize = 10;
} 