package com.faw.work.ais.aic.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 自定义FAQ知识Excel导入数据DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "自定义FAQ知识Excel导入数据DTO")
public class FaqKnowledgeCustomExcelDataDTO {
    
    /**
     * 类目名称
     */
    @ExcelProperty("类目名称")
    @Schema(description = "类目名称")
    private String categoryName;
    
    /**
     * 知识标题（对应question）
     */
    @ExcelProperty("知识标题")
    @Schema(description = "知识标题")
    private String title;
    
    /**
     * 相似问法（多条，使用换行符分隔）
     */
    @ExcelProperty("相似问法")
    @Schema(description = "相似问法")
    private String similarQuestions;
    
    /**
     * 纯文本答案
     */
    @ExcelProperty("答案（默认)【纯文本】")
    @Schema(description = "纯文本答案")
    private String plainTextAnswer;
    
    /**
     * 富文本答案
     */
    @ExcelProperty("答案（默认)【富文本】")
    @Schema(description = "富文本答案")
    private String richTextAnswer;
} 