package com.faw.work.ais.common.enums.chat;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别枚举
 *
 * <AUTHOR>
 * @since 2025-06-26 11:07
 */
@Getter
@AllArgsConstructor
public enum GenderEnum {

    /**
     * 男
     */
    MALE("MALE", "男"),

    /**
     * 女
     */
    FEMALE("FEMALE", "女"),

    /**
     * 未知
     */
    UNKNOWN("UNKNOWN", "未知");

    private final String code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return GenderEnum
     */
    public static GenderEnum getByCode(String code) {
        for (GenderEnum genderEnum : GenderEnum.values()) {
            if (genderEnum.getCode().equals(code)) {
                return genderEnum;
            }
        }
        return UNKNOWN;
    }
}
