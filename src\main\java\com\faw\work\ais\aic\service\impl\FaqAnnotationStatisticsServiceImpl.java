package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.mapper.faq.FaqAnnotationStatisticsMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationStatisticsPO;
import com.faw.work.ais.aic.model.response.FaqAnnotationStatisticsResponse;
import com.faw.work.ais.aic.service.FaqAnnotationDetailService;
import com.faw.work.ais.aic.service.FaqAnnotationStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * FAQ标注统计Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqAnnotationStatisticsServiceImpl implements FaqAnnotationStatisticsService {

    @Autowired
    private FaqAnnotationStatisticsMapper faqAnnotationStatisticsMapper;

    @Autowired
    private FaqAnnotationDetailService faqAnnotationDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createStatistics(FaqAnnotationStatisticsPO statistics) {
        if (ObjectUtil.isNull(statistics)) {
            throw new IllegalArgumentException("统计信息不能为空");
        }

        statistics.setCreatedAt(LocalDateTime.now());
        statistics.setUpdatedAt(LocalDateTime.now());

        int result = faqAnnotationStatisticsMapper.insert(statistics);
        if (result > 0) {
            log.info("成功创建统计信息，统计ID: {}", statistics.getId());
            return statistics.getId();
        } else {
            throw new RuntimeException("创建统计信息失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateStatistics(List<FaqAnnotationStatisticsPO> statisticsList) {
        if (ObjectUtil.isNull(statisticsList) || statisticsList.isEmpty()) {
            throw new IllegalArgumentException("统计信息列表不能为空");
        }

        LocalDateTime now = LocalDateTime.now();
        for (FaqAnnotationStatisticsPO statistics : statisticsList) {
            statistics.setCreatedAt(now);
            statistics.setUpdatedAt(now);
        }

        int result = faqAnnotationStatisticsMapper.batchInsert(statisticsList);
        log.info("批量创建统计信息，数量: {}", result);
        return result;
    }

    @Override
    public List<FaqAnnotationStatisticsPO> getStatisticsByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqAnnotationStatisticsMapper.findByTaskId(taskId);
    }

    @Override
    public FaqAnnotationStatisticsResponse getAnnotationStatistics(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        // 获取总数据量和已标注数量
        int totalCount = faqAnnotationDetailService.countByTaskId(taskId);
        int annotatedCount = faqAnnotationDetailService.countAnnotatedByTaskId(taskId);

        // 获取统计数据
        List<FaqAnnotationStatisticsPO> statistics = faqAnnotationStatisticsMapper.findByTaskId(taskId);

        // 构建响应数据
        Map<String, FaqAnnotationStatisticsResponse.AnnotationTypeStatistics> typeMap = new HashMap<>();

        for (FaqAnnotationStatisticsPO stat : statistics) {
            String type = stat.getAnnotationType();
            String subtype = stat.getAnnotationSubtype();
            Integer count = stat.getCount();

            FaqAnnotationStatisticsResponse.AnnotationTypeStatistics typeStats = typeMap.get(type);
            if (typeStats == null) {
                typeStats = FaqAnnotationStatisticsResponse.AnnotationTypeStatistics.builder()
                        .annotationType(type)
                        .annotationTypeName(getAnnotationTypeName(type))
                        .count(0)
                        .subtypeList(new ArrayList<>())
                        .build();
                typeMap.put(type, typeStats);
            }

            typeStats.setCount(typeStats.getCount() + count);

            if (StrUtil.isNotBlank(subtype)) {
                FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics subtypeStats = 
                        FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics.builder()
                                .annotationSubtype(subtype)
                                .annotationSubtypeName(getAnnotationSubtypeName(subtype))
                                .count(count)
                                .build();
                typeStats.getSubtypeList().add(subtypeStats);
            }
        }

        return FaqAnnotationStatisticsResponse.builder()
                .totalCount(totalCount)
                .annotatedCount(annotatedCount)
                .annotationList(new ArrayList<>(typeMap.values()))
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean upsertStatistics(String taskId, String annotationType, String annotationSubtype, Integer count) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        if (StrUtil.isBlank(annotationType)) {
            throw new IllegalArgumentException("标注类型不能为空");
        }
        if (ObjectUtil.isNull(count) || count < 0) {
            throw new IllegalArgumentException("数量不能为空且不能小于0");
        }

        int result = faqAnnotationStatisticsMapper.upsertStatistics(taskId, annotationType, annotationSubtype, count);
        if (result > 0) {
            log.info("成功更新统计信息，任务ID: {}, 类型: {}, 子类型: {}, 数量: {}", 
                    taskId, annotationType, annotationSubtype, count);
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recalculateStatistics(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        int result = faqAnnotationStatisticsMapper.recalculateStatistics(taskId);
        log.info("重新计算统计信息，任务ID: {}, 影响记录数: {}", taskId, result);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        int result = faqAnnotationStatisticsMapper.deleteByTaskId(taskId);
        log.info("删除任务统计信息，任务ID: {}, 删除记录数: {}", taskId, result);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean incrementStatistics(String taskId, String annotationType, String annotationSubtype) {
        // 获取当前统计数量
        List<FaqAnnotationStatisticsPO> currentStats = faqAnnotationStatisticsMapper.findByTaskId(taskId);
        int currentCount = 0;
        
        for (FaqAnnotationStatisticsPO stat : currentStats) {
            if (annotationType.equals(stat.getAnnotationType()) && 
                ObjectUtil.equal(annotationSubtype, stat.getAnnotationSubtype())) {
                currentCount = stat.getCount();
                break;
            }
        }

        return upsertStatistics(taskId, annotationType, annotationSubtype, currentCount + 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean decrementStatistics(String taskId, String annotationType, String annotationSubtype) {
        // 获取当前统计数量
        List<FaqAnnotationStatisticsPO> currentStats = faqAnnotationStatisticsMapper.findByTaskId(taskId);
        int currentCount = 0;
        
        for (FaqAnnotationStatisticsPO stat : currentStats) {
            if (annotationType.equals(stat.getAnnotationType()) && 
                ObjectUtil.equal(annotationSubtype, stat.getAnnotationSubtype())) {
                currentCount = stat.getCount();
                break;
            }
        }

        int newCount = Math.max(0, currentCount - 1);
        return upsertStatistics(taskId, annotationType, annotationSubtype, newCount);
    }

    /**
     * 获取标注类型名称
     */
    private String getAnnotationTypeName(String annotationType) {
        if (annotationType == null) return "";
        switch (annotationType) {
            case "correct": return "正确";
            case "error": return "错误";
            case "uncovered": return "未覆盖";
            case "invalid": return "无效";
            case "pending": return "待定";
            default: return annotationType;
        }
    }

    /**
     * 获取标注子类型名称
     */
    private String getAnnotationSubtypeName(String annotationSubtype) {
        if (annotationSubtype == null) return "";
        switch (annotationSubtype) {
            case "error_no_handle": return "错误暂不处理";
            case "error_modify_knowledge": return "错误修改知识";
            case "error_handled": return "错误已处理";
            case "uncovered_no_handle": return "未覆盖暂不处理";
            case "uncovered_add_knowledge": return "未覆盖新增知识";
            case "uncovered_handled": return "未覆盖已处理";
            default: return annotationSubtype;
        }
    }
}
