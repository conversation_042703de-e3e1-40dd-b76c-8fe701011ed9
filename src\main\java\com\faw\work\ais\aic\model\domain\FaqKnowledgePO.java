package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * FAQ知识库实体类
 * <AUTHOR>
 */
@Schema(description = "数据FAQ知识库实体类")
@Data
@Accessors(chain = true)
@TableName("faq_knowledge")
public class FaqKnowledgePO {
    
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 问题
     */
    @Schema(description = "问题")
    private String question;
    
    /**
     * 答案
     */
    @Schema(description = "答案")
    private String answer;
    
    /**
     * 命中次数
     */
    @Schema(description = "命中次数")
    private Long hitCount;

    /**
     * 分类id
     */
    @Schema(description = "分类id")
    private String categoryId;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createdBy;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updatedBy;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 相似度得分（非数据库字段，用于搜索结果）
     */
    @Schema(description = "相似度得分（非数据库字段，用于搜索结果）")
    @TableField(exist = false)
    private Float score;


    /**
     * 生效类型 00-永久有效 01-临时有效
     */
    @Schema(description = "生效类型 00-永久有效 01-临时有效")
    @TableField("effectiveType")
    private String effectiveType;
    /**
     * 有效开始时间
     */
    @Schema(description = "有效开始时间")
    @TableField("effectiveStartTime")
    private String effectiveStartTime;
    /**
     * 有效结束时间
     */
    @Schema(description = "有效结束时间")
    @TableField("effectiveEndTime")
    private String effectiveEndTime;

    /**
     * 发布状态 00-未发布 01-已发布
     */
    @Schema(description = "发布状态 00-未发布 01-已发布")
    private String publishStatus;

    /**
     * 答案类型 00-纯文本 01-富文本
     */
    @Schema(description = "答案类型 00-纯文本 01-富文本")
    private String answerType;

    @Schema(description = "类别名称")
    @TableField(exist = false)
    private String categoryName;

    @Schema(description = "知识版")
    @TableField("knowledge_version")
    private Integer knowledgeVersion;

} 