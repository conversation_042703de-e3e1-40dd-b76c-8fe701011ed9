package com.faw.work.ais.aic.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 向量搜索结果实体类
 * <AUTHOR>
 */
@Schema(description = "向量搜索结果实体类")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorSearchResult {
    
    /**
     * ID
     */
    @Schema(description = "向量ID")
    private String id;
    
    /**
     * 相似度分数
     */
    @Schema(description = "相似度分数")
    private Float score;


    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;
} 