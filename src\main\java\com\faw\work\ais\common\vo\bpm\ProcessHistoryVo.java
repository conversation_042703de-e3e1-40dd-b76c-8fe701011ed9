package com.faw.work.ais.common.vo.bpm;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "查询BPM流程进度历史返回值实体类")
@EqualsAndHashCode
public class ProcessHistoryVo {

    @Schema(description = "执行人")
    private String approver;

    @Schema(description = "节点ID")
    private String taskDefinitionKey;

    @Schema(description = "持续时间(分钟)")
    private long durationTime;

    @Schema(description = "顺序番号")
    private String level;

    @Schema(description = "意见")
    private String remark;

    @Schema(description = "时间")
    private LocalDateTime time;

    @Schema(description = "无")
    private String link;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "通过或驳回")
    private String state;

    @Schema(description = "执行人code")
    private String approvercode;

    @Schema(description = "任务ID")
    private String id;

    @Schema(description = "节点名称")
    private String title;
}
