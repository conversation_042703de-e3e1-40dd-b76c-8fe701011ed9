package com.faw.work.ais.aic.mapper.rag;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.entity.dto.AppSearchConfigDTO;
import com.faw.work.ais.aic.model.domain.RagDocumentSplitPO;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档片段表 Mapper 接口
 *
 * <AUTHOR> Assistant
 */
@Mapper
public interface RagDocumentSplitMapper extends BaseMapper<RagDocumentSplitPO> {

    /**
     * 根据文档ID查询片段列表
     *
     * @param documentId 文档ID
     * @return 片段列表
     */
    List<RagDocumentSplitPO> selectByDocumentId(@Param("documentId") String documentId);
    
    /**
     * 根据文档ID删除片段
     *
     * @param documentId 文档ID
     * @return 影响行数
     */
    int deleteByDocumentId(@Param("documentId") String documentId);

    /**
     * 更新点击次数
     *
     * @param id    本我
     * @param score 得分
     */
    void updateHitCount(Long id, Float score);

    /**
     * 查询应用搜索配置
     *
     * @param appId 应用ID
     * @return {@link AppSearchConfigDTO }
     */
    AppSearchConfigDTO selectAppSearchConfig(@NotNull(message = "应用ID不能为空") Long appId);
}