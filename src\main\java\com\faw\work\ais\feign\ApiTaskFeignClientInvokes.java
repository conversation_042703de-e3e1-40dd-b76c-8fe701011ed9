package com.faw.work.ais.feign;

import com.faw.work.ais.entity.dto.BaseOutDTO;
import com.faw.work.ais.entity.dto.ai.PythonAiTaskDTO;
import com.faw.work.ais.feign.interceptor.IworkOpenApiFeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = "aiUrl", url = "${feign.python.hostUrl}", configuration = {FeignClientConfig.class, IworkOpenApiFeignInterceptor.class})
public interface ApiTaskFeignClientInvokes {

    /**
     * ai识别结果python服务
     * @param info
     * @return
     */
    @RequestMapping(value = "/dataReview", method = RequestMethod.POST)
    BaseOutDTO aiTask(@RequestBody PythonAiTaskDTO info);

}
