package com.faw.work.ais.service;

import com.faw.work.ais.aic.service.FaqSimilarKnowledgeService;
import com.faw.work.ais.aic.model.domain.FaqSimilarKnowledgePO;

import java.util.List;

/**
 * 相似问服务接口
 * @deprecated 使用 {@link FaqSimilarKnowledgeService} 替代
 */
@Deprecated
public interface SimilarQuestionService {

    /**
     * 新增相似问
     *
     * @param similarQuestion 相似问实体
     */
    void saveSimilarQuestion(FaqSimilarKnowledgePO similarQuestion);

    /**
     * 批量新增相似问
     *
     * @param similarQuestionList 相似问列表
     * @return 是否成功
     */
    boolean saveBatchSimilarQuestion(List<FaqSimilarKnowledgePO> similarQuestionList);

    /**
     * 删除相似问
     *
     * @param id 相似问ID
     * @return 是否成功
     */
    boolean deleteSimilarQuestion(String id);

    /**
     * 根据原问题ID删除相似问
     *
     * @param originalId 原问题ID
     * @return 是否成功
     */
    boolean deleteByOriginalId(String originalId);

    /**
     * 获取相似问详情
     *
     * @param id 相似问ID
     * @return 相似问详情
     */
    FaqSimilarKnowledgePO getSimilarQuestionDetail(String id);

    /**
     * 根据原问题ID查询相似问列表
     *
     * @param originalId 原问题ID
     * @return 相似问列表
     */
    List<FaqSimilarKnowledgePO> listByOriginalId(String originalId);
} 