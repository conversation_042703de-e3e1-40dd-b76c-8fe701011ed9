
package com.faw.work.ais.common.vo.iworkUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * iwork分页查询用户列表返回数据实体（返回的参数很多，但是现在有数据的只有这几个字段）
 * <AUTHOR>
 * */
@Data()
@Schema(description = "iwork分页查询用户列表返回数据实体")
public class IworkUserInfo {
    @Schema(description = "登录账号")
    private String loginName;
    @Schema(description = "用户id")
    private String id;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "用户姓名")
    private String name;
    @Schema(description = "用户类型")
    private Integer userType;
    @Schema(description = "手机号")
    private String mobile;

}
