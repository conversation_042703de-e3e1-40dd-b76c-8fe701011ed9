package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * 大模型评测请求
 * <AUTHOR>
 */
@Data
@Schema(description = "大模型评测请求")
public class ModelEvaluationRequest {
    
    @Schema(description = "工作空间", example = "llm-xxx")
    @NotBlank(message = "工作空间不能为空")
    private String workSpace;
    
    @Schema(description = "API Key", example = "sk-xxx")
    @NotBlank(message = "API Key不能为空")
    private String apiKey;
    
    @Schema(description = "应用ID", example = "app-xxx")
    @NotBlank(message = "应用ID不能为空")
    private String appId;
    
    @Schema(description = "大模型调用的自定义参数")
    private Map<String, Object> bizParams;
}
