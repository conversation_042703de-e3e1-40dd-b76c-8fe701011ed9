package com.faw.work.ais.aic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.mapper.faq.FaqRobotKnowledgeJoinsProdMapper;
import com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsProdPO;
import com.faw.work.ais.aic.service.FaqRobotKnowledgeJoinsProdService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * FAQ机器人知识关联服务实现类
 * <AUTHOR>
 */
@Service
public class FaqRobotKnowledgeJoinsProdServiceImpl extends ServiceImpl<FaqRobotKnowledgeJoinsProdMapper, FaqRobotKnowledgeJoinsProdPO>
        implements FaqRobotKnowledgeJoinsProdService {

    @Override
    public List<String> getIdsByRobotId(String robotId) {
        return baseMapper.getIdsByRobotId(robotId);
    }

    @Override
    public void removeByRobotId(String robotId) {
        baseMapper.deleteByRobotId(robotId);
    }
}