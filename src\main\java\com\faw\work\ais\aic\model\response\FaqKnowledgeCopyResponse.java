package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * FAQ知识复制响应对象
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "FAQ知识复制响应对象")
public class FaqKnowledgeCopyResponse {

    /**
     * 复制的知识数量
     */
    @Schema(description = "复制的知识数量")
    private Integer knowledgeCount;

    /**
     * 复制的相似问数量
     */
    @Schema(description = "复制的相似问数量")
    private Integer similarKnowledgeCount;

    /**
     * 总复制数量
     */
    @Schema(description = "总复制数量")
    private Integer totalCount;
}
