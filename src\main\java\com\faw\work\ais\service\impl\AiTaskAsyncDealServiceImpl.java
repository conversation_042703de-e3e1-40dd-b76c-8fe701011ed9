package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.dcp.common.files.vo.FileInfoVO;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.FileUtil;
import com.faw.work.ais.common.util.HttpUtil;
import com.faw.work.ais.config.WhitelistConfig;
import com.faw.work.ais.entity.dto.MessageDTO;
import com.faw.work.ais.entity.dto.ai.*;
import com.faw.work.ais.feign.BnzxOpenApiFeignClient;
import com.faw.work.ais.feign.IworkOpenApiFeignClient;
import com.faw.work.ais.mapper.ais.*;
import com.faw.work.ais.message.RabbitService;
import com.faw.work.ais.model.LogInfoJava;
import com.faw.work.ais.model.LogInfoPythonCallbackWithBLOBs;
import com.faw.work.ais.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiTaskAsyncDealServiceImpl implements AiTaskAsyncDealService {

    private final FileMapper fileMapper;

    private final TaskRuleMapper taskRuleMapper;

    private final LogInfoJavaMapper logInfoJavaMapper;

    private final LogInfoPythonCallbackMapper logInfoPythonCallbackMapper;

    private final IworkOpenApiFeignClient iworkOpenApiFeignClient;

    private final BnzxOpenApiFeignClient bnzxOpenApiFeignClient;

    private final RabbitService rabbitService;

    private final WhitelistConfig whitelistConfig;

    private final FileUtil fileUtil;

    // 请求标识；2-批量请求
    final String BATCHFLAG_MORE = "2";

    @Override
    @Async
    public void asyncAiTaskBatch(List<AiTaskDTO> aiTaskDTOs) {
        // 保存AIO服务调用入参内容到日志表-单据维度
        savaJavaLogWhenGetRequest(null,aiTaskDTOs, BATCHFLAG_MORE);

        aiTaskDTOs.stream().forEach(x -> {
            try {
                // 保存AIO请求日志-规则维度
                savaPythonLogWhenGetRequest(x);
                String result = pushParamToMq(x);
            } catch (Exception ex) {
                log.error("--asyncAiTaskBatch接口处理失败--", ex);
                // 向mq推送时出现异常，直接回调；
                asyncErrorCallBack(x, ex.getMessage());
            }
        });
    }

    /**
     * 接到请求时报错java入参日志
     * @Param aiTaskDTO
     * @Param batchFlag 1-单次请求(从/asyncAiTask接口过来的请求)；2-批量请求（从/asyncAiTaskBatch或者/asyncPushToMq过来的请求）
     */
    private void savaJavaLogWhenGetRequest(AiTaskDTO aiTaskDTO,List<AiTaskDTO> aiTaskDTOS, String batchFlag){
        // 根据batch_id查询日志是否存在；存在则不保存
        if(BATCHFLAG_MORE.equals(batchFlag)){
            aiTaskDTO = aiTaskDTOS.get(0);
        }
        // 根据batch_id查询日志是否存在；存在则不保存
        LogInfoJava logInfoJava = logInfoJavaMapper.getInfoByBatchId(aiTaskDTO.getBatchId());
        if(logInfoJava == null){
            // 保存入参内容到java日志表
            LogInfoJava logInfoJavaNew = assimbleLogInfoJava(aiTaskDTO, aiTaskDTOS, batchFlag);
            logInfoJavaMapper.insert(logInfoJavaNew);
        }else {
            logInfoJava.setUpdateTime(new Date());
            logInfoJavaMapper.updateByPrimaryKeySelective(logInfoJava);
        }
    }

    /**
     * 保存batch_id,trace_id到python日志表
     * @param aiTaskDTO
     */
    private void savaPythonLogWhenGetRequest(AiTaskDTO aiTaskDTO){
        // 根据batch_id,traceId查询日志是否存在；存在则不保存
        LogInfoPythonCallbackWithBLOBs selectObj = new LogInfoPythonCallbackWithBLOBs();
        selectObj.setTraceId(aiTaskDTO.getTraceId());
        selectObj.setBatchId(aiTaskDTO.getBatchId());
        LogInfoPythonCallbackWithBLOBs logInfoPythonCallback = logInfoPythonCallbackMapper.selectByObj(selectObj);
        if(logInfoPythonCallback == null){
            // 保存入参内容到python日志表
            assimbleLogInfoPythonAndSave(aiTaskDTO);
        }else {
            logInfoPythonCallback.setUpdateTime(new Date());
            logInfoPythonCallback.setPushCount(logInfoPythonCallback.getPushCount() + 1);
            logInfoPythonCallbackMapper.updateByPrimaryKeyWithBLOBs(logInfoPythonCallback);
        }
    }

    private LogInfoPythonCallbackWithBLOBs assimbleCallBackParam(LogInfoPythonCallbackWithBLOBs inParam, String callBackParam){
        Date now = new Date();
        inParam.setCallbackParam(callBackParam);
        inParam.setCallbackTime(now);
        inParam.setUpdateTime(now);
        return inParam;
    }

    private LogInfoPythonCallbackWithBLOBs assimbleCallBackResultParam(LogInfoPythonCallbackWithBLOBs inParam, String callBackResultParam){
        Date now = new Date();
        inParam.setCallbackResult(callBackResultParam);
        inParam.setCallbackResultTime(now);
        inParam.setUpdateTime(now);
        return inParam;
    }

    /**
     * 拼装java请求日志的被调用入参
     * @param aiTaskDTO 单个参数
     * @param aiTaskDTOs 多个参数
     * @param flag 1-单次请求；2-批量请求
     * @return
     */
    private LogInfoJava assimbleLogInfoJava(AiTaskDTO aiTaskDTO, List<AiTaskDTO> aiTaskDTOs, String flag){
        LogInfoJava logInfoJava = new LogInfoJava();
        Date now = new Date();
        logInfoJava.setJavaCallTime(now);
        logInfoJava.setCreateTime(now);
        // 单次请求参数记录
        if("1".equals(flag)){
            String param = JSON.toJSONString(aiTaskDTO);
            logInfoJava.setBatchId(aiTaskDTO.getBatchId());
            logInfoJava.setJavaParam(param);
            logInfoJava.setSystemId(aiTaskDTO.getSystemId());
        }
        //  批量请求参数记录
        if("2".equals(flag)){
            AiTaskDTO aiTaskDTOBatch = aiTaskDTOs.get(0);
            String param = JSON.toJSONString(aiTaskDTOs);
            logInfoJava.setBatchId(aiTaskDTOBatch.getBatchId());
            logInfoJava.setJavaParam(param);
            logInfoJava.setSystemId(aiTaskDTOBatch.getSystemId());
        }
        return logInfoJava;
    }

    /**
     * 新增loginfo-python表数据
     * @param aiTaskDTO
     */
    private void assimbleLogInfoPythonAndSave(AiTaskDTO aiTaskDTO){
        Date now = new Date();
        LogInfoPythonCallbackWithBLOBs infoPython = new LogInfoPythonCallbackWithBLOBs();
        infoPython.setBatchId(aiTaskDTO.getBatchId());
        infoPython.setTraceId(aiTaskDTO.getTraceId());
        infoPython.setAuditModelSceneId(aiTaskDTO.getAuditModelSceneId());
        infoPython.setPushCount(0);
        infoPython.setCreateTime(now);
        logInfoPythonCallbackMapper.insert(infoPython);
    }

    private String pushParamToMq(AiTaskDTO aiTaskDTO){
        log.info("[AiTaskAsyncDealService][pushParamToMq][entrance] aiTaskDTO: {}", JSON.toJSONString(aiTaskDTO));

        log.info("[AiTaskAsyncDealService][pushParamToMq] -------------开始插入消息队列前置处理-----------------");

        List<BeCheckFileDTO> contents = aiTaskDTO.getContents();
        // 给Python服务的文件入参
        List<BeCheckFileDTO> toPythonFiles = new ArrayList<>();

        if(whitelistConfig.getFileUndoChecks().contains(aiTaskDTO.getSystemId())){
            // 推送消息队列
            sendAiTaskMessage(aiTaskDTO, toPythonFiles, contents);
        }else {
            if(contents != null && !contents.isEmpty()) {
                contents.forEach(x -> {
                    try {
                        // 计算文件的MD5值并上传文件到cos
                        CosInfoDTO cosDto = pushToCos(x, aiTaskDTO.getBizId());
                        // 构造给python服务的fileUrl为本次COS桶中的Url
                        BeCheckFileDTO toPythonFileInfos = BeCheckFileDTO.builder().fileUrl(cosDto.getFilePath())
                                .fileContentType(x.getFileContentType()).version(x.getVersion()).fileIndex(x.getFileIndex())
                                .fileDesc(x.getFileDesc()).cosKey(cosDto.getKey()).build();
                        toPythonFiles.add(toPythonFileInfos);

                    }catch (Exception ex){
                        log.warn("--asyncAiTask上传cos文件服务器错误-- message: ", ex);
                        throw new BizException("--asyncAiTask上传cos文件服务器错误--");
                    }
                });
                // 推送消息队列
                sendAiTaskMessage(aiTaskDTO, toPythonFiles, contents);
            }else{
                // 目前任务运营中心不需要使用推送消息队列
                sendAiTaskMessageNoFileInfo(aiTaskDTO);
            }
        }
        log.info("[AiTaskAsyncDealService][asyncDeal] -------------完成插入消息队列前置处理-----------------");
        return "";
    }

    private void asyncErrorCallBack(AiTaskDTO aiTaskDTO, String causeMsg){
        log.info(aiTaskDTO.getTraceId() + "-----------------------------推送mq过程中失败开始执行回调方法-------------------------");
        String callBackUrl = taskRuleMapper.getCallBackUrl(aiTaskDTO.getSystemId());
        try {
            // 拼装回调入参
            CallBackDTO callBackDTO = new CallBackDTO();
            // copy函数赋值失败，所以主动赋值试试
            callBackDTO.setCallbackCustomParam(aiTaskDTO.getCallBackCustomParam());
            callBackDTO.setTaskType(aiTaskDTO.getTaskType());
            callBackDTO.setSuccess("false");
            callBackDTO.setMessage("AI公共服务接口异常-推送mq失败" + causeMsg);
            log.info(aiTaskDTO.getTraceId() + "-----asyncErrorCallBack回调json格式入参----" + JSON.toJSONString(callBackDTO));
            LogInfoPythonCallbackWithBLOBs logInfoPythonCallback = logInfoPythonCallbackMapper.selectByTraceId(aiTaskDTO.getTraceId());
            logInfoPythonCallback = assimbleCallBackParam(logInfoPythonCallback, JSON.toJSONString(callBackDTO));
            logInfoPythonCallbackMapper.updateByTranceId(logInfoPythonCallback);
            log.info(aiTaskDTO.getTraceId() + "-------asyncErrorCallBack插入回调能力中心入参日志成功----------");
            Response result = null;
            if("BNZX".equals(aiTaskDTO.getSystemId())){
                result = bnzxOpenApiFeignClient.callBack(callBackDTO,"true", "15", "pc");
            }else {
                URI uri = new URI(callBackUrl);
                result = iworkOpenApiFeignClient.dynamicCallBack(uri, callBackDTO);
            }
            log.info(aiTaskDTO.getTraceId() + "-----asyncErrorCallBack回调返回值为-----" + JSON.toJSON(result));
            logInfoPythonCallback = assimbleCallBackResultParam(logInfoPythonCallback, JSON.toJSONString(result));
            logInfoPythonCallbackMapper.updateByPrimaryKey(logInfoPythonCallback);
            log.info(aiTaskDTO.getTraceId() + "-----asyncErrorCallBack插入回调能力中心的回调结果日志成功-----" + JSON.toJSON(result));
        }catch (Exception ex){
            log.error(aiTaskDTO.getTraceId() + "--asyncErrorCallBack执行回调函数出错--" , ex);
        }
        log.info(aiTaskDTO.getTraceId() + "--------------------------------asyncErrorCallBack执行回调方法结束---------------------------------"  + aiTaskDTO.getTraceId());
    }

    /**
     * 推送消息到队列
     *
     * @param aiTaskDTO         接口入参
     * @param toPythonFileInfos 给python的文件入参，url为AI对外公共服务COS桶中的数据
     */
    private void sendAiTaskMessage(AiTaskDTO aiTaskDTO, List<BeCheckFileDTO> toPythonFileInfos, List<BeCheckFileDTO> contents) {
        log.info(aiTaskDTO.getTraceId() + "---------------开始推送消息队列-----------" + aiTaskDTO.getTraceId());
        // 构造消息业务数据
        CosQueueDTO cosQueueDTO = new CosQueueDTO();
        BeanUtils.copyProperties(aiTaskDTO, cosQueueDTO);
        cosQueueDTO.setContents(contents);
        cosQueueDTO.setToPythonFileInfos(toPythonFileInfos);
        cosQueueDTO.setBatchId(aiTaskDTO.getBatchId());
        String jsonString = JSON.toJSONString(cosQueueDTO);
        // 封装消息结构体
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setJsonMapString(jsonString);
        messageDTO.setBusinessType("1");
        // 推送消息
        String messageJson = JSON.toJSONString(messageDTO);
        if ("BNZX".equals(aiTaskDTO.getSystemId())) {
            rabbitService.convertAndSendBnzx(messageJson);
        } else {
            rabbitService.convertAndSend(messageJson);
        }
        log.info(aiTaskDTO.getTraceId() + "---------------推送消息队列结束-----------" + aiTaskDTO.getTraceId());
    }

    /**
     * 没有文件信息-旗效看板使用
     * @param aiTaskDTO
     */
    private void sendAiTaskMessageNoFileInfo(AiTaskDTO aiTaskDTO){
        log.info(aiTaskDTO.getTraceId() + "---------------开始推送消息队列无文件信息-----------" + aiTaskDTO.getTraceId());
        // 构造消息业务数据
        CosQueueDTO cosQueueDTO = new CosQueueDTO();
        BeanUtils.copyProperties(aiTaskDTO, cosQueueDTO);
        String jsonString = JSON.toJSONString(cosQueueDTO);
        // 封装消息结构体
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setJsonMapString(jsonString);
        messageDTO.setBusinessType("1");
        // 推送消息
        String messageJson = JSON.toJSONString(messageDTO);
        rabbitService.convertAndSend(messageJson);
        log.info(aiTaskDTO.getTraceId() + "---------------推送消息队列结束无文件信息-----------" + aiTaskDTO.getTraceId());
    }

    /**
     * 文件上传到cos桶，并返回文件的fileId
     * @param beCheckFile
     * @param bizId
     * @return
     */
    private CosInfoDTO pushToCos(BeCheckFileDTO beCheckFile, String bizId){
        log.info("[AiTaskAsyncDealService][pushToCos][entrance] bizId: {}, beCheckFile: {}", bizId, JSON.toJSONString(beCheckFile));
        log.info("[AiTaskAsyncDealService][pushToCos][entrance] ----------------开始处理文件信息--------------");
        String cosKey = getCosKey();
            String fileName = getUrlFileName(beCheckFile.getFileUrl());
            String fileUrl = beCheckFile.getFileUrl();
            // 计算文件的md5值
            com.faw.work.ais.model.File fileInfo = getFileByUrl(fileUrl == null ? cosKey : fileUrl);
            String fileMd5 = fileInfo.getMd5();
            fileInfo.setRawName(fileName);
            fileInfo.setMd5(fileMd5);
            fileInfo.setFileDesc(beCheckFile.getFileDesc());
            fileInfo.setFileIndex(Integer.valueOf(beCheckFile.getFileIndex()));
            fileInfo.setVersion(beCheckFile.getVersion());

            CosInfoDTO cosInfo = new CosInfoDTO();
            cosInfo.setKey(cosKey);
            cosInfo.setLocalFile(fileUrl);

        try {
            fileInfo.setId(cosKey);
            //保存到cos
            FileInfoVO fileResult = fileUtil.uploadToCos(cosInfo);
            cosInfo.setFilePath(fileResult.getUrl());
            fileMapper.insert(fileInfo);
        } catch (Exception e) {
            log.error("[AiTaskAsyncDealService][pushToCos] 推送文件md5值到cos服务器出错 message: ", e);
            throw new BizException("------推送文件md5值到cos服务器出错----");
        }
        log.info("[AiTaskAsyncDealService][pushToCos][entrance] ----------------处理文件信息结束--------------");
        return cosInfo;
    }


    /**
     * 根据外系统的url读取文件的md5以及其他文件信息
     * @param urlPath
     * @return
     */
    public com.faw.work.ais.model.File getFileByUrl(String urlPath) {
        com.faw.work.ais.model.File file = new com.faw.work.ais.model.File();
        InputStream inputStream = null;
        try {
            HttpEntity result = HttpUtil.sendGet(urlPath,null ,null );
            if(result == null){
                throw new RuntimeException("请求url图片地址失败，url为" + urlPath);
            }
            inputStream =  result.getContent();
            long fileSize = result.getContentLength();
            log.info("--file size--" +fileSize);
            if(inputStream == null){
                throw new RuntimeException("文件信息读取失败，url为" + urlPath);
            }
            // 创建一个临时文件来存储下载的内容
            Path tempFile = Files.createTempFile("downloaded-file", null);
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);

            // 计算MD5哈希值
            MessageDigest md = MessageDigest.getInstance(CommonConstants.ENCRYPTION_ALGORITHM);
            byte[] fileContent = Files.readAllBytes(tempFile);
            md.update(fileContent);
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            String md5 = sb.toString();
            System.out.println("File MD5: " + md5);
            file.setMd5(sb.toString());
            file.setLength(String.valueOf(fileSize));
            // 清理临时文件
            Files.delete(tempFile);
        } catch (Exception e) {
            log.error("--计算文件md5值出现异常--错误信息 = " +  e.getMessage());
            throw new RuntimeException("--计算文件md5值出现异常--" + e.getMessage() + e.getCause());
        }finally {
            try {
                // 关闭文件流
                if(inputStream != null){
                    inputStream.close();
                }
            } catch (IOException ie) {
                log.error("----读取文件异常----错误信息 = " + ie.getMessage() + ie.getCause());
            }
        }
        return file;
    }

    private String getUrlFileName(String url){
        String fileName = "";
        String regex = "([^/]+)$"; // 匹配最后一个/后面的所有字符，直到字符串结束

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            fileName = matcher.group(1); // 获取匹配到的部分（即最后一个/后面的字符串）
        }
        return fileName;
    }

    /**
     * 生成18位随机数
     *
     * @return
     */
    private String getCosKey(){
        String timestampStr = String.format("%015d", System.currentTimeMillis()); // 15位时间戳
        Random random = new Random();
        int hundred = random.nextInt(9) + 1; // 1-9之间的百位
        int ten = random.nextInt(10); // 0-9之间的十位
        int one = random.nextInt(10); // 0-9之间的个位
        int randomNumber = hundred * 100 + ten * 10 + one; // 组合成三位数
        String  result = String.valueOf(randomNumber) + timestampStr;
        return result;
    }

}
