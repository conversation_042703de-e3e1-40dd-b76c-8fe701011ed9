package com.faw.work.ais.common.exception;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.enums.ResEnum;
import feign.codec.DecodeException;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
@Configuration
@Order(1)
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {Exception.class})
    public Response<JSONObject> defaultErrorHandler(Exception e) {
        log.warn("unknown exception", e);
        return Response.fail(e.getMessage());
    }

    @ExceptionHandler(value = {SystemException.class})
    public Response<JSONObject> systemException(Exception e) {
        log.warn("system exception", e);
        return Response.fail(e.getMessage());
    }


    @ExceptionHandler(BizException.class)
    public Response<Void> businessException(BizException e) {
        log.warn("business exception", e);
        if (Objects.nonNull(e.getErrorEnum())) {
            return Response.fail(e.getErrorEnum());
        }
        return Response.of(e.getMessage(), ResEnum.FAIL_CODE.code());
    }

    /**
     * 接口请求方式不匹配异常
     * */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Response<Void> businessException(HttpRequestMethodNotSupportedException e) {
        if (Objects.nonNull(e.getMessage())) {
            return Response.fail(e.getMessage());
        }
        return Response.of(e.getMessage(), ResEnum.FAIL_CODE.code());
    }


    @ExceptionHandler(DecodeException.class)
    public Response<Void> businessException(DecodeException e) {
        if (Objects.nonNull(e.getMessage())) {
            return Response.fail(e.getMessage());
        }
        return Response.of(e.getMessage(), ResEnum.FAIL_CODE.code());
    }


    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Response<Void> maxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        return Response.of("文件过大", ResEnum.FAIL_CODE.code());
    }

    /**
     * 注解参数校验异常拦截
     * */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Response<Void> illegalParamsExceptionHandler(MethodArgumentNotValidException e, ServletWebRequest request ) {
        String description = request.getDescription(true);
        Object target = e.getBindingResult().getTarget();
        log.warn("illegalParams, uri is {},request body is {}", description, JSON.toJSONString(target));
        FieldError first = e.getBindingResult().getFieldErrors().get(0);
        return  Response.fail(first.getField()+"-"+first.getDefaultMessage());
    }

    /**
     * body参数为null异常拦截
     * */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Response<Void> requestBodyMissedExceptionHandler(HttpMessageNotReadableException e, ServletWebRequest request ) {
        String description = request.getDescription(true);
        log.warn("illegalParams, uri is {},exception info is {}", description, e.getMessage());
        return  Response.fail(e.getMessage());
    }

    /**
     * 实体参数注解校验异常拦截
     * */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Response<Void> illegalParamsExceptionHandler2(ConstraintViolationException e) {
        log.warn(e.getMessage());
        return  Response.fail(e.getMessage());
    }

    /**
     * 拦截@RequestParam参数未传异常
     * */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Response<Void> methodArgumentTypeMismatchExceptionHandler(MethodArgumentTypeMismatchException e) {
        log.warn("必传参数缺失异常:{}",e.getMessage());
        return  Response.fail(e.getMessage());
    }

    /**
     * 拦截参数类型无法转换异常*
     * /
     * */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Response<Void> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException e) {
        log.warn("参数类型无法转换异常:{}",e.getMessage());
        return  Response.fail(e.getMessage());
    }

}

